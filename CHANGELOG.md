# Internal Dashboard Majoo

## v2.70.4 - 03 Jul 2025
- Enhancement:
    - #86eu1uw3w: Apply majoo-ui Table to:
        - dev/src/js/pages/Settings/ManagePaymentMethod/ManagePaymentMethod.js
        - dev/src/js/pages/Settings/Priviliege/MenuCms/component/TreeMenuCms.js
        - dev/src/js/pages/Settings/Priviliege/MenuRetina/component/TreeMenuRetina.js
    
## v2.70.3 - 02 Jul 2025
- Hotfix:
    - Page Banner - #86eu1d2ny: fixing filter status all

## v2.70.2 - 25 Jun 2025
- Hotfix:
    - Promo: fix error encode base64 when insert emoticon

## v2.70.1 - 25 Jun 2025
- Enhancement:
    - #86etqbacr: Apply majoo-ui Table to:
        - dev/src/js/pages/Settings/Priviliege/MenuRetina/RetinaRole.js
        - dev/src/js/pages/Support/AccessLogin/AccessLogin.js
        - dev/src/js/pages/Support/Backupdb/BackupDatabase.js
        - dev/src/js/pages/Support/BusinessDevelopment/BusinessCoaching.js
        - dev/src/js/pages/Support/BusinessDevelopment/BusinessLoaning.js
        - dev/src/js/pages/Sales/Support/PurchaseList/index.js
        - dev/src/js/pages/Sales/Support/category.js
        - dev/src/js/pages/Sales/Support/Claim.js
        - dev/src/js/pages/Sales/Support/Feedback.js
        - dev/src/js/pages/Sales/Support/supportLocation.js
        - dev/src/js/pages/Sales/Support/supportmaster.js
        - dev/src/js/pages/Sales/Support/Terminate.js
        - dev/src/js/pages/Settings/PaymentReport/PaymentReport.js
        - dev/src/js/pages/Sales/Portal/CampaignLeads.js
        - dev/src/js/pages/Sales/Portal/Collaboration.js
        - dev/src/js/pages/Sales/Portal/Demo.js
        - dev/src/js/pages/Sales/Portal/DemoMobile.js
        - dev/src/js/pages/Sales/Portal/GeneralQuestion.js
        - dev/src/js/pages/Sales/Portal/MauMajoo.js
        - dev/src/js/pages/Sales/Supplies/SuppliesProduct/components/AddProducts.js
        - dev/src/js/pages/Sales/Supplies/SuppliesProduct/components/Home.js
        - dev/src/js/pages/Sales/Supplies/WhitelistOutlets/index.js
        - dev/src/js/pages/Sales/Supplies/category.js
        - dev/src/js/pages/Sales/Supplies/master.js
        - dev/src/js/pages/Sales/Supplies/Supplies.js
        - dev/src/js/pages/Settings/Biller/components/TableHistorySubmission.js
        - dev/src/js/pages/Settings/Biller/Edc.js
        - dev/src/js/pages/Settings/Biller/NonCashSetting.js
        - dev/src/js/pages/Settings/Biller/ReportVa.js
        - dev/src/js/pages/Settings/Biller/TSelPoin.js
        - dev/src/js/pages/Settings/Biller/Wallet.js
        - dev/src/js/pages/Settings/ManageContent/Promo/index.js
        - dev/src/js/pages/Settings/ManageContent/SuppliesFormPortal/index.js
        - dev/src/js/pages/Settings/ManageContent/Blog.js
        - dev/src/js/pages/Settings/ManageContent/NewsAndPromo.js
        - dev/src/js/pages/Settings/ManageContent/NewsPortal.js
        - dev/src/js/pages/Settings/ManageContent/Solusi.js
        - dev/src/js/pages/Support/MarketPlace/Submission/GrabfoodSubmission.js
        - dev/src/js/pages/Support/MarketPlace/Tokopedia/Index.js
        - dev/src/js/pages/User/Live/Sales.js

## v2.70.0 - 24 Jun 2025
- Enhancement:
    - Enhance Integration Grabfood dan Grabmart

## v2.69.4 - 18 Jun 2025
- Hotfix:
    - #86etfgrng: maintain server.js from client / repository instead of from infra

## v2.69.4 - 18 Jun 2025
- Enhancement:
    - #86etqbacr: Apply majoo-ui Table to:
        - dev/src/js/pages/User/Profile/Active.js
        - dev/src/js/pages/User/Profile/UserDevice.js
        - dev/src/js/pages/User/Profile/Location.js
        - dev/src/js/pages/User/Profile/OutletProfile.js
        - dev/src/js/pages/User/Profile/AccountInformation/index.js
        - dev/src/js/pages/User/Profile/Order.js
        - dev/src/js/pages/User/Report/UserGrowth.js
        - dev/src/js/pages/User/Report/Active.js
        - dev/src/js/pages/User/Report/Unsubscribe.js
        - dev/src/js/pages/User/Report/Grab/SummaryGrabTransaction.js
        - dev/src/js/pages/User/Report/ShipmentReport/index.js
        - dev/src/js/pages/User/Report/Settlement/SettlementReport.js
        - dev/src/js/pages/User/Report/Settlement/SettlementSummaryReport.js
        - dev/src/js/pages/User/Report/Settlement/AutoReconciliation.js
        - dev/src/js/pages/Settings/ManageContent/TextBanner/Content.js
        - dev/src/js/pages/Settings/ManageContent/TooltipsGuidance/index.js
        - dev/src/js/pages/Settings/ManageContent/Vacancy/VacancyList.js
        - dev/src/js/pages/Settings/ManageUser/BlockList.js
        - dev/src/js/pages/Settings/ManageUser/ExceptionList.js
        - dev/src/js/pages/Settings/ManageUser/WatchList.js
        - dev/src/js/pages/Settings/ContentReview/ContentReview.js
        - dev/src/js/pages/Settings/DanaNmid/DanaNmid.js
        - dev/src/js/pages/Settings/Help/HelpCategory.js
        - dev/src/js/pages/Settings/Help/HelpList.js
        - dev/src/js/pages/Settings/ManageContent/Advertisement/index.js
        - dev/src/js/pages/Settings/ManageContent/ConsumerApp/index.js
        - dev/src/js/pages/Settings/ManageContent/Event/index.js
        - dev/src/js/pages/Settings/ManageContent/ManageImage/index.js
        - dev/src/js/pages/Settings/ManageContent/Multilanguage/index.js
        - dev/src/js/pages/Settings/ManageContent/PageBanner/index.js
        - dev/src/js/pages/Settings/ManageContent/Termandcondition.js
        - dev/src/js/pages/Settings/Priviliege/CmsRole.js
        - dev/src/js/pages/Settings/Priviliege/InternalRole.js
        - dev/src/js/pages/Support/Message/BroadcastMessage.js
        - dev/src/js/pages/User/Consultant/index.js
        - dev/src/js/pages/User/Consultant/detail.js
        - dev/src/js/pages/User/Kasbon/Outlet/index.js
        - dev/src/js/pages/User/Cashbon/Cashbon.js
        - dev/src/js/pages/User/MajooLite/BigPopUp/index.js
        - dev/src/js/pages/User/MajooLite/Notifications/index.js
        - dev/src/js/pages/User/MajooLite/TransactionReport/SettlementRequest.js
        - dev/src/js/pages/User/MajooLite/TransactionReport/TransactionDetails.js
        - dev/src/js/pages/User/MajooLite/TransactionReport/TransactionSummary.js
        - dev/src/js/pages/User/MajooLite/WalletSubmmision/Wallet.js
        - dev/src/js/pages/User/MajooLite/DataVerification/index.js
        - dev/src/js/pages/User/Operations/ItemUsedTransactions.js
        - dev/src/js/pages/User/Operations/UserTransactionBillerLog/index.js
        - dev/src/js/pages/User/Operations/ProductRecipe.js
        - dev/src/js/pages/User/Operations/ConsumerAppActivation/components/PageTable.js
        - dev/src/js/pages/User/Operations/StatusProduct.js
        - dev/src/js/pages/User/Onboarding/Mitra.js
        - dev/src/js/pages/User/Onboarding/Community.js
        - dev/src/js/components/recipientpopup/Container.js
        - dev/src/js/components/sidepopup/SidePopupDetailOutlet/DeviceList.js
        - dev/src/js/pages/Sales/Campaign/campaign.js
        - dev/src/js/pages/Sales/Campaign/package.js
        - dev/src/js/pages/Sales/Deposit/Deposit.js
        - dev/src/js/pages/Sales/DomainPremium/index.js
        - dev/src/js/pages/Sales/Funnel/DetailFunnel.js
        - dev/src/js/pages/Sales/Funnel/funnel.js
        - dev/src/js/pages/Sales/Funnel/Funnelreport.js
        - dev/src/js/pages/Sales/Funnel/Salesreport.js
        - dev/src/js/pages/User/Report/Weborder/TransactionSummaryReport.js
        - dev/src/js/pages/User/Report/Weborder/TransactionDetailsReport.js
        - dev/src/js/pages/User/Report/Weborder/SettlementRequest.js
        - dev/src/js/pages/User/Report/TelkomselPointReport/index.js
        - dev/src/js/pages/User/Report/Biller/DetailBillerDailyReport.js
        - dev/src/js/pages/User/MajooLite/WalletSubmmision/components/TableHistorySubmission.js

## v2.69.3 - 12 Jun 2025
- Enhancement:
    - #86ettfzyz: Add InputText debouncing functionality

## v2.69.2 - 5 Jun 2025
- Enhancement:
    - #86eth05kh: Apply majoo-ui Table to Majoo Admin User List page

## v2.69.1 - 26 Mei 2025
- Hotfix:
    - Laporan Settement & Ringkasan Settlement: init filter date today
    - move action update status to settlemen
    - Remove unused file 250516
    - Remove unused
        - dependencies: @babel/cli & @babel/runtime
        - libs: modernizr
        - update package-lock.json, and update docker to copy package-lock.json as well
    - Add jsconfig.json to help vscode scanning workspace (indexing)
    - Add knip config
    - Remove unused: webpack.config.js

## v2.69.0 - 14 Mei 2025
- Feature:
    - component privilege

## v2.68.4 - 13 Mei 2025
- Hotfix:
    - Maintain code: remove unused files & dependencies 05 Mei - 08 Mei 2025

## v2.68.3 - 7 Mei 2025
- Hotfix: 
    - prevent google indexing
    - Menu: #86etazmtx - Add menuList nullable condition 

## v2.68.2 - 1 Mei 2025
- Hotfix: Backup DB: Fix nullable column value

## v2.68.1 - 30 April 2025
- Hotfix: Backup DB: Apply debounce to Autocomplete

## v2.68.0 - 30 April 2025
- Feature: Backup DB Majoolite

## v2.67.3 - 28 April 2025
- Hotfix: Filter Vacancy List

## v2.67.2 - 16 April 2025
- Hotfix: Pembelian Support penambahan termin langganan

## v2.67.1 - 18 Maret 2025
- Hotfix: fix event time picker inputChangeHandler 

## v2.67.0 - 18 Maret 2025
- Enhance: Event Webinar

## v2.66.3 - 26 Februari 2025
- Hotfix:
    - Menu Retina: add component options

## v2.66.2 - 20 Januari 2025
- Hotfix:
    - Privacy Policy: add majoolite platform

## v2.66.1 - 17 Januari 2025
- Hotfix:
    - Settlement Rekonsiliasi: fix mdr decimal

## v2.66.0 - 14 Januari 2025
- Feature:
    - Wallet submission majoolite

## v2.65.4 - 10 Januari 2025
- Hotfix:
    - Wallet submission - Dana Statis: show print qris saat qr_string ada value nya

## v2.65.3 - 8 Januari 2025
- Hotfix:
    - Content Review: Penambahan colomn created date

## v2.65.2 - 6 Januari 2025
- Hotfix:
    - Wallet submission - Dana Statis: show qr string when edit

## v2.65.1 - 30 Desember 2024
- Hotfix:
    - Wallet submission - Dana Statis:
        - disabled test qr
        - fixing error simpan qris statis

## v2.65.0 - 27 Desember 2024
- Feature:
    - Wallet submission: Dana Statis QRIS

## v2.64.0 - 04 Desember 2024
- Enhancement:
    - Privacy Policy

## v2.63.2 - 11 Oktober 2024
- Hotifx:
    - Manage Content - Blog, News & Promo: set role marketing (role id: 24) allowed as author and reviewer

## v2.63.1 - 01 Oktober 2024
- Hotfix:
    - Promo Portal:
        - Handle filter reset table pagination
        - Add delete confirmation

## v2.63.0 - 26 September 2024
- Feature:
    - Promo Portal

## v2.62.0 - 19 September 2024
- Feature:
    - Upgrade webpack to v5.94.0

## v2.61.3 - 28 Agustus 2024
- Hotfix:
    - Enhance deploy prod

## v2.61.2 - 20 Agustus 2024
- Hotfix:
    - Weborder Transactions Detail: default date 1 day (yesterday - today)
    - Wallet Submission: Add button duplicate submission

## v2.61.1 - 13 Agustus 2024
- Hotfix: 
    - Support EXP: add loading & reset value after change outle

## v2.61.0 - 6 Agustus 2024
- Feature:
    - Actor Name
    - Suport exp: extend bill

## v2.60.1 - 6 Agustus 2024
- Hotfix:
    - Laporan Keuangan Konsultan change text area to wysisyg

## v2.60.0 - 30 Juli 2024
- Feature:
    - Laporan Keuangan

## v2.59.4 - 26 Juli 2024
- Hotfix:
    - Wallet: kirim outlet name qris xendit

## v2.59.3 - 19 Juli 2024
- Hotfix:
    - Wallet: hapus validasi KTP ketika edit

## v2.59.2 - 18 Juli 2024
- Hotfix 
    - Wallet: fixing open new tab when clicked detail

## v2.59.1 - 5 Juli 2024
- Hotfix 
    - Outlet Profile Side Popup width
    - Table v2 reset page when search
    - Table v2 page not selected when change page
    - Pengajuan Wallet: add payload outlet_name

## v2.59.0 - 4 Juli 2024
- Feature
    - Outlet Profile: Penambahan informasi device

## v2.58.4 - 13 Juni 2024
- Hotfix
    - Fix payload Generate NMID 

## v2.58.3 - 7 Juni 2024
- Hotfix
    - Ringkasan Settlement: handle response data null

## v2.58.2 - 31 Mei 2024
- Hotfix
    - Enhance Calender max 7 days
        - Summary Biller Daily
        - Ringkasan Settlement
    - Fix Page News/Blog/Solusi unable too upload image
    - Fix permalinks on page News wrong URL

## v2.58.1 - 28 Mei 2024
- Hotfix
    - Support - Delete Transaction: fix upload image

## v2.58.0 - 28 Mei 2024
- Feature
    - Supplies Category

## v2.57.0 - 20 Mei 2024
- Feature
    - Tools TAC 1

## v2.56.1 - 15 Mei 2024
- Hotfix
    - Wallet Submission: fix field nib & address merchant and outlet

## v2.56.0 - 14 Mei 2024
- Feature
    - Supplies Form Portal: new pages

## v2.55.3 - 1 Mei 2024
- Hotfix
    - fix some route cant access

## v2.55.2 - 29 April 2024
- Hotfix
    - Support: add column device id backup and sync

## v2.55.1 - 23 April 2024
- Hotfix
    - Transaction Online Payment: fix params fetch data Laporan Pembayaran

## v2.55.0 - 17 April 2024
- Enhancement
    - User Transaction Biller Log: add section proof of payment

## v2.54.0 - 15 April 2024
- Feature
    - Add APM
        - update router to v5
        - refactor base routing
        - enhance UI test
    - Wallet Submission: enhance form

## v2.53.1 - 4 April 2024
- Hotfix
    - Wallet Submission: fix map data address

## v2.53.0 - 2 April 2024
- Enhance
    - Enhance NMID

## v2.52.4 - 2 April 2024
- Enhance
    - Enhance BNI KUR Detail

## v2.52.4 - 2 April 2024
- Enhance
    - Enhance BNI KUR Detail

## v2.52.3 - 28 Maret 2024
- Hotfix
    - Fix can't buy subscription

## v2.52.2 - 27 Maret 2024
- Hotfix
    - Update option select for page pembelian support

## v2.52.1 - 5 Maret 2024
- Hotfix
    - Transaction Online Payment: Fix search parameter
    - Wallet Submission: handle show error print qris (global)

## v2.52.0 - 29 Februari 2024
- Release
    - Supplies Whitelist: Add franchise

## v2.51.0 - 27 Februari 2024
- Release
    - Wallet Submission: Add Filter Channel in List

## v2.50.0 - 27 Februari 2024
- Release
    - Wallet Submission: Adjustment Form

## v2.49.0 - 22 Februari 2024
- Release
    - Imprvement menu blogpost and solusi, add reviewer fields and detail

## v2.48.2 - 21 Februari 2024
- Hotfix
    - Update jenkins file for deploy staging

## v2.48.1 - 19 Februari 2024
- Hotfix
    - fix payload outlet profile

## v2.48.0 - 16 Februari 2024
- Feature
    - Transaction Online Payment

## v2.47.0 - 16 Februari 2024
- Feature
    - Release Kasbon Phase 1
        - Daftar Request Kasbon
        - Daftar Outlet

## v2.46.1 - 14 Februari 2024
- Hotfix
    - Wallet Submission: change input area to options kecamatan

## v2.46.0 - 14 Februari 2024
- Enhance
    - Wallet Submission: adjustment form and filter in list

## v2.45.0 - 6 Februari 2024
- Hotfix
    - Add page Consumer App Report from Portal

## v2.44.8 - 27 Januari 2024
- Hotfix
    - Change upload url in edc detail
## v2.44.7 - 15 Januari 2024
- Hotfix
    - Tooltip Guidance
        - Adjust selection category and article
        - Fix mapping selection article

## v2.44.6 - 12 Januari 2024
- Hotfix
    - Wallet Submission: error message condition

## v2.44.5 - 12 Januari 2024
- Hotfix
    - Wallet Submission: generate nmid dana change submission_id instead of outlet_id, nik validation

## v2.44.4 - 11 Januari 2024
- Hotfix
    - Tooltips Guidance: repointing source article list 

## v2.44.2 - 21 Desember 2023
- Hotfix:
    - fixing API BASE URL job vacancy to portal

## v2.44.1 - 30 November 2023
- Hotfix
    - Page Banner: adjust button footer state when status page banner is active

## v2.44.0 - 03 November 2023
- Feature
    - Supplies 1.4: Enhance page banner to dynamic array image content

## v2.43.1 - 03 November 2023
- Hotfix
    - Outlet Profile: add Customer ID on list and detail
    - Wallet submission: add outlet id when hit generate nmid with data provider, reset nmid when id provider changed

## v2.43.0 - 30 Oktober 2023
- Feature
    - Dana NMID and Detail Wallet Submission Add Option for Dana Provider

## v2.42.0 - 30 Oktober 2023
- Enhancement
    - Enhance Manage Multilanguage V2

## v2.41.0 - 19 Oktober 2023
- Enhancement
    - Add Options in Activation Type

## v2.40.0 - 13 Oktober 2023
- Feature
    - Enhance Support Feedback

## v2.39.0 - 13 Oktober 2023
- Feature
    - Manage Multilanguage Backend

## v2.38.1 - 11 Oktober 2023
- Hotfix
    - Support Buy fix price not showing first time component loaded

## v2.38.0 - 6 Oktober 2023
- Feature
    - Enhance Buy Support for Basic Starter

## v2.37.0 - 29 September 2023
- Feature
    - Enhance MDR Supplies

## v2.36.0 - 29 September 2023
- Feature
    - Weborder Transaction Detail Report

## v2.35.0 - 27 September 2023
- Feature
    - Subscription Package on setting banner

## v2.34.2 - 27 September 2023
- Hotfix
    - Fix switch on / off menu banner text

## v2.34.1 - 22 September 2023
- Hotfix
    - Migrate Map component API to openstreetmap

## v2.34.0 - 19 September 2023
- Feature
    - Portal News
    - Add menu Banner Text

## v2.33.1 - 1 September 2023
- Hotfix
    - Fix BigPopUp dialog delete issue

## v2.33.0 - 21 Agustus 2023
- Feature
    - Tooltips Guidance
- Hotfix
    - Fix BigPopUp draft issue

## v2.32.2 - 18 Agustus 2023
- Hotfix:
    - Enhance subcription reference and invoice CRM

## v2.32.1 - 11 Agustus 2023
- Hotfix:
    - majoolite notification page
        - https://app.clickup.com/t/860rgc084
        - https://app.clickup.com/t/860rh0zkh
        - https://app.clickup.com/t/860rh1v43

## v2.32.0 - 7 Agustus 2023
- Feature
    - Provisioning Management

## v2.31.0 - 4 Agustus 2023
- Feature
    - majoolite notification page

## v2.30.6 - 3 Agustus 2023
- Hotfix
    - big popup majoolite

## v2.30.5 - 4 July 2023
- Hotfix
    - Approval Endpoint Wallet Payment
        - Fix mid not set to payload when user click generate mid (xendit schema)

## v2.30.4 - 4 July 2023
- Hotfix
    - Wallet Submission
        - Fix wrong payload

## v2.30.3 - 3 July 2023
- Hotfix
    - Wallet Submission
        - Swap endpoint on approval status

## v2.30.2 - 3 July 2023
- Hotfix
    - Support Pembelian
        - Change restriction popup only enterprise

## v2.30.1 - 21 June 2023
- Hotfix
    - Wallet Submission
        - Add button generate MID for QRIS static xendit
        - Add function to hit activation account api if provider xendit

## v2.30.0 - 19 June 2023
-Feature
    - Operations
        - Items Used for Transaction
        - New Menu User Transaction Bilelr Log

## v2.29.1 - 15 June 2023
- Hotfix
    - Settings
        - Manage Content
            - Web Portal
                - Blog / Solusi
                    - Fixing Duplicate Content Label
    - Fixing auto logout when token is expired

## v2.29.1 - 09 June 2023
- Hotfix
    - Settings
        - Manage Content
            - Web Portal
                - Blog / Solusi
                    - Add Fungsion to change Alt Image Content at Blog and Solusi
    - User
        - Operations
            - User Product Recipe
                - Add Error Handel when failed print data

## v2.29.0 - 31 May 2023
-Feature
    - Manage Content
        - New Menu Manage Image for upload image assets link

## v2.28.0 - 24 May 2023
-Feature
    - New menu User Product Recipe

## v2.27.5 - 23 May 2023
-Hotfix
    - Majoolite
        - Data Verification
            - fix bug sorting data is not appropriate

## v2.27.4 - 12 May 2023
-Hotfix
    - Business Development
        -  Business Loan
            - fix bug blank page when open btn kur detail loan

## v2.27.3 - 5 May 2023
-Hotfix
    - Settings
        - Privilage
            - Takeout Support Access for Menu
        - Manage Content
            - Advertisment
                - Input content got encode for pas the cloudflare blocking              
    - User
        - Profile
            - Outlet Map
                - Add Fetch Settings for better performance     

## v2.27.2 - 19 April 2023
-Hotfix
    - Reconciliation Page
        - fix bug failed download template summary reconciliation  

## v2.27.1 - 18 April 2023
-Hotfix
    - Reconciliation Page
        - fix bug failed download reconciliation report 

## v2.27.0 - 18 April 2023
-Feature
    - Settings 
        - Privilage
            - Menu CMS Retina 

## v2.26.1 - 18 April 2023
-Hotfix
    - Report Settlement Page
        - added a column named platform in the table
        - added a column named payment method in the table
        - fix bug blink when search or filter oulet
    - Reconciliation Page
        - added a column named platform in the table
        - added a column named channel (Settlement Type) in the table
        - added filter by channel (Settlement Type)

## v2.26.0 - 18 April 2023
-Feature
    - move vacancy api

## v2.25.1 - 17 April 2023
-Feature
    - Settlement Filter Outlet Heavy Load pages fix
    - Informasi Akun 404 Side Fixing

## v2.25.0 - 13 April 2023
-Feature
    - Premium Domain

## v2.24.5 - 11 April 2023
-Hotfix
    - Change endpoint in Page Banner from page_banen/menu to page_banner/menu_retina

## v2.24.4 - 29 March 2023
-Hotfix
    - BackupDB can't download file bug fix
    - Page Banner change to better validation form

## v2.24.3 - 28 March 2023
-Hotfix
    - Add OVO minimum value transaction to Rp.1000,-
    - Failed Change Expired Date Qry At BackupDB

## v2.24.2 - 28 March 2023
-Hotfix
    - Fixing a bug when uploading CSV strings are not correct if there are letters with marks or accents

## v2.24.1 - 27 March 2023
-Hotfix
    - Refactor menu Support -> Backup/Sync
    - change UploadCSV Popup to global components
    - fix error when update settlement request weborder
    - fix failed when upload ringkasan settlement

## v2.24.0 - 21 March 2023
-Feature
    - User
        - Profile
            - AccountInformation
                - New "Integrated" status for Bank Raya

## v2.23.8 - 21 March 2023
-Hotfix
    - Settings
        - Manage Content
            - Page Banner
                - Enhance Field required Page Banner

## v2.23.7 - 20 March 2023
-Hotfix
    - Settings
        - Klopos Admin
            - UserList
                - Default Status Filter is Active
                - Change Button Cancel at Popup to Close
    - Bug at open create form at wallet-submission, fix
    - Avoid reload page when the image raya is empty

## v2.23.6 - 16 March 2023
-Hotfix
    - fix: jenkisfile and update node version in dockerfile

## v2.23.5 - 16 March 2023
-Hotfix
    - fix: jenkisfile remove deploy by branch master

## v2.23.4 - 16 March 2023
-Hotfix
    - update: jenkisfile for deploy production by tagging

## v2.23.3 - 16 March 2023
-Hotfix
    - Bug at open menu direct by link with role access - Done Fixing
    - Support: fixing pembelian support e-commerce

## v2.23.2 - 10 March 2023
-Hotfix
    - User
        - Majoo Lite
            - Wallet Submission
                - Text QR popup not create QR Images, Bug Fix
                - Session Data Outlet not return Outlet Name, Bug Fix
        - Profle
            - Outlet Map
                - Make Fast loading when load a Heavy Data Outlet, and show the progress at Top Right Map Components
 
## v2.23.1 - 09 March 2023
-Hotfix
    - User
        - Majoo Lite
            - Wallet Submission
                - Fix failed test QR wallet submission detail majoo lite
            - Transaction Report
                - Settlement Request
                    - display does not match when successfully updating upload settlement majoo lite


## v2.23.0 - 01 March 2023
-Feature
    - User List 
        - New Feature show change log per user

## v2.22.1 - 02 Maret 2023
-Hotfix
    - Majoo Lite
        - Data Verification
            - update payload on filter by date data verification majoo lite
        - Transaction Report
            - Settlement Request
                - fix failed update upload settlement request
    - Report
        - Settlement
            - Reconciliation
                - update wording max upload data from 3000 to 5000
    - Settings
        - Non Cash Payment
            - Wallet Submission
                - add button print QR static Xendit

## v2.22.0 - 28 Februari 2023
-Feature
    - Majoo Lite
        - new Feature Wallet Submission

## v2.21.0 - 24 Februari 2023
-Feature
    - Sales
        - Support
            - Pembelian
                - Add Invoice CRM

## v2.20.2 - 22 Februari 2023
-Hotfix
    - User
        - Report
            - Weborder Report
                - Weborder Transaction Details
                    - Change select outlet to autocomplete
                - Weborder Transaction Summary
                    - Change select outlet to autocomplete
                - Weborder Settlement Request
                    - Change select outlet to autocomplete

## v2.20.1 - 21 Februari 2023
-Hotfix
    - Sales
        - Support
            -Pembelian
                - Fixing auto refresh when update pembelian status
                
## v2.20.0 - 17 Februari 2023
-Feature
    - User
        - Profile
            - Outlet Profile
                - Add New filter for sources Majoo or Majoolite

## v2.19.0 - 9 Februari 2023
-Feature
    - Support
        - Change Backup DB User to Backup/Sync
            - Add new Feature to Sync at BackUp Databases
    - Settings
        - Klopos Admin
            - User List
                - add new Column Last Login

## v2.18.2 - 6 Februari 2023
-Hotfix
    - Report 
        - Settlement
            - Ringkasan Settlement
                - Add filter dropdown majoo and majoolite
            - Reconciliation
                - Add filter dropdown majoo and majoolite

## v2.18.1 - 6 Februari 2023
-Hotfix
    - User 
        - Report 
            - Settelement
                - Add filter dropdown majoo and majoolite

## v2.18.0 - 4 Februari 2023
-Feature
    - Sales 
        - Support 
            - Feedback
                - Add new Coloumn Busniness Type and Source at Support Feedback

## v2.17.4 - 26 January 2023
-Hotfix
    - Vacancy Management
        - Fixing Validation for Vacancy Management

## v2.17.3 - 18 January 2023
-Hotfix
    - Majoo Lite
        - Fixing Filter add All report Majoo Lite by Changeing client_id: 97 to channel: 6

## v2.17.2 - 10 January 2023
-Hotfix
    - Support
        - Pembelian
            - Fixing bug at Filter Outler Popup

## v2.17.1 - 3 January 2023
-Hotfix
    - Support
        - Pembelian
            - Fixing Data Range Filter

## v2.17.0 - 29 Desember 2022
-Feature
    - Logout hit endpoint for remove user token 

## v2.16.0 - 23 Desember 2022
-Feature
    - Advertisement
        - Add new Field Upload Image for Popup Advertisement

## v2.15.0 - 23 Desember 2022
-Feature
    - Page Banner add new Field for Dekstop Retina and Mobile Retina

## v2.14.1 - 22 Desember 2022
-Hotfix
    - Support
        - Pembelian
            - Add Show Progress when save or update data
            - Reset State when open a new form
    - Content Review
        - Fixing Date Format and Eslint
    - Manage Klopos Users
        - Watch List
            - Fixing Date Format and Eslint

## v2.14.0 - 29 November 2022
- Feature
    - Majoo Lite
        - Transaction Detail
        - Transaction Summary
        - Settlement Request

## v2.13.2 - 15 November 2022
- Hotfix:
    - Add jenkins file & docker file for deployment

## v2.13.1 - 9 November 2022
- Hotfix:
    - Enhancment Detail Usaha Cockpit
    - Majoo Lite
        - Data Verification fixing data address field

## v2.13.0 - 2 November 2022
- Release:
    - Portal Post
        - Add new Field Redirect URL 

## v2.12.0 - 26 Oktober 2022
- Release:
    - Majoo Lite
        - Data Verification

## v2.11.1 - 26 Oktober 2022
- Hotfix:
    - Fixing responsive CustomMarker with small resolution

## v2.11.0 - 21 Oktober 2022
- Release:
    - Majoopay Enhancement Simplifikasi Provider

## v2.10.3 - 20 Oktober 2022
- Hotfix:
    - Fixing wording in pengajuan EDC
    - Fixing mdr value in pengajuan EDC
    - Fixing css pagination in Table V2
    - Fixing Download Dropdown CSS at Weborder Settlement Request

## v2.10.2 - 17 Oktober 2022
- Hotfix:
    - Fix blank screen on open rejected request with no reason value
    - Remove and change custom right buttons props in SidePopup

## v2.10.1 - 23 September 2022
- Hotfix:
    - Fixing filter page settlement

## v2.10.0 - 21 September 2022
- Release:
    - Menu General Question, Demo Invitation
        - New Field
            - Submit Date
            - Type
            - Page Source
            - Company Name
            - City
            - Address
            - Qty of Outlet
            - Old POS
    - New Menu at Website Report => Collaboration

## v2.9.2 - 19 September 2022
- Hotfix:
    - Fixing Text Editor bug scroll at Vacancy Management Menu
    - Suppot Exp Notif not show when success update data fix
    - Show Success Message Copy at Support Access Login
    - Fixing Status change bug at Help Category
    - Added Loading Screen
        - Live Support
        - Live Subscriptions

## v2.9.1 - 12 September 2022
- Hotfix:
    - Fixing upload image on category & master supplies

## v2.9.0 - 9 September 2022
- Feature:
    - Menambah channel BTN KUR ke modal wirausaha
    - Menambah halaman auto reconciliation
    - Enhancement Telkomsel Point
        - Menambah halaman history pemakaian telkomsel point
        - Menambah kemampuan untuk memasukan lebih dari 1 outlet saat pengajuan
  
## v2.8.5 - 9 September 2022
- Hotfix:
    - Report
        - Ringkasan Settlement
            - Fixing Number Column Wording
    - Add Loading Screen for Some Cockpit Menu
        - Onboarding
            - Mitra
            - Community
        - User
            - Live
                - Active User
                - Sales
                - New User
                - Revenue

## v2.8.4 - 31 Agustus 2022
- Hotfix:
    - Change Apikey Google
    - Fixing Wording Date Range Filter at Profile Account Information
    - Render Not Found Data Component when open the details when the data is not exist

## v2.8.3 - 26 Agustus 2022
- Hotfix:
    - Fixing Profile User Active Range Date Wording

## v2.8.2 - 23 Agustus 2022
- Hotfix:
    - Loading Screen for Operations
        - Feedback
        - Backup DB
        - Inquiry
        - Mitra
        - Community
        - Web Check-out Services

## v2.8.1 - 19 Agustus 2022
- Hotfix:
    - Outlet Map
        - Reset page to default 1 when change filter

## v2.8.0 - 18 Agustus 2022
- Feature:
    - Menambah channel BNI KUR ke modal wirausaha

## v2.7.3 - 18 Agustus 2022
- Hotfix:
    - Fixing nilai MDR tidak tersimpan 
    - Fixing meta data total page di halaman Settlement
    - Menambah kondisi tampilan QR Code yang menggunakan base64

## v2.7.2 - 12 Agustus 2022
- Hotfix:
    - Add Show Progress when page not full loaded
        - New User
        - User Active

## v2.7.1 - 5 Agustus 2022
- Hotfix:
    - Cockpit landing scroll fix

## v2.7.0 - 4 Agustus 2022
- Feature:
    - Consumer App Activation Request

## v2.6.1 - 3 Agustus 2022
- Hotfix:
    - Ads Multiple Save Data Fix
    - Add Show Progress
        - User Growth
        - User Activity


## v2.6.0 - 1 Agustus 2022
- Feature:
    - Setup Unitest dan UI test
    - landing: add example ui test

## v2.5.1 - 29 Juli 2022
- Hotfix:
    - add field discount in detail pembelian page

## v2.5.0 - 22 Juli 2022
- Release:
    - New Field Page Source at Website Report Menu : 
        - General Question
        - Demo Invitation
        - Mau Majoo
        - Demo Majoo

## v2.4.4 - 22 Juli 2022
- Hotfix:
    - fix biller loading

## v2.4.3 - 21 Juli 2022
- Hotfix:
    - Fixing pagination summary biller daily

## v2.4.2 - 21 Juli 2022
- Hotfix:
    - Supplies :
      - add product coverage
      - add mapping outlet type
    - Shipment Report: fix payload & column channel 

## v2.4.1 - 18 Juli 2022
- Hotfix:
    - Remove all current state at delete transaction when user change Client
    - Fixing notification at Grab Food Integration

## v2.4.0 - 15 Juli 2022
- Feature:
    - Shipment Report

## v2.3.4 - 13 Juli 2022
- Hotfix:
    - Fixing cockpit can't upload Image 

## v2.3.3 - 12 Juli 2022
- Hotfix:
    - Fixing cockpit upload Image bug
        - Croping
        - Filesize Validation

## v2.3.2 - 12 Juli 2022
- Hotfix:
    - remove max width

## v2.3.1 - 11 Juli 2022
- Hotfix:
    - Add Filesize validation for Blog and Solusi
    - Add spesial character validation for Userlist Name

## v2.3.0 - 8 Juli 2022
- Hotfix:
    - responsive main layout

## v2.2.2 - 17 June 2022
- Hotfix:
    - Funnel Detail: enhance table v2

## v2.2.1 - 13 June 2022
- Hotfix:
    - Content Review Download Fixing
    - Fixing UI Date Filter
      - GrabFood Integrations
      - GrabFood Submissions

## v2.2.0 - 10 June 2022
- Feature:
    - Supplies Whitelist

## v2.1.5 - 9 June 2022
- Hotfix :
    - Fixing Dropdown Text Editor for setting paragraph and other
    - Fix filters table supplies
    - Fix filter date page General Question
    - Fix filter date page Demo Majoo
    - Fix filter date page Mau Majoo
    - Fix filter date page Demo Invitation
    - Fix Calendar Pick on Bussiness Loan
    - Fix Loading Screen on View Add TSEL Poin

## v2.1.4 - 8 June 2022
- Hotfix :
    - Fix Black Page after update Support Exp

## v2.1.3 - 6 June 2022
- Hotfix :
    - Hide Legend at Report User Unsubscribe
    - Fix payload page Settlement
    - Hide Legend at Report Inactive User

## v2.1.2 - 2 June 2022
- Hotfix :
    - Fix Open Support Delete Transaction

## v2.1.1 - 1 June 2022
- Hotfix :
    - Fix Open Setting Support Exp
    
## v2.1.0 - 31 May 2022
- Feature:
    - Grabmart Integration

## v2.0.0 - 30 May 2022
- Feature:
    - Upgrade react version (v17)

## v1.59.5 - 26 May 2022
- Hotfix:
    - Fixing Bug for Open Detail Data List
        - Profile - User Active
        - Profile - User Devices

## v1.59.4 - 24 May 2022
- Hotfix:
    - Enhance table v2 pages:
        - Profile - User Active
        - Profile - Outlet Profile
        - Profile - User Devices

## v1.59.3 - 18 May 2022
- Hotfix:
    - Fix commbank save button not triggered

## v1.59.2 - 12 May 2022
- Hotfix:
    - Fix Notification Campaing Pakage
    - Fix Open Menu Deposit 
    - Add FAQ Field to Solusi Form

## v1.59.1 - 10 May 2022
- Hotfix:
    - Penyesuaian warna dan message notif subscription
    - Autoclose Side Popup when update data
    - Set MDR value can be 0
    - MID bisa diedit saat sudah di approve

## v1.59.0 - 10 May 2022
- Feature:
    - Campaign Package - Change Exp on (Month) from DropDown to Input Number Field 

## v1.58.3 - 9 May 2022
- Hotfix:
    - Enhance table v2 pages:
        - Kategori Bantuan
    - BLOG FAQ Remove all FAQ fixing
    
## v1.58.2 - 28 April 2022
- Hotfix:
    - Enhance table v2 pages:
        - User - OnBoarding - Mitra
        - User - OnBoarding - Community
        - Setting - Manage Content - Advertisement

## v1.58.1 - 25 April 2022
- Hotfix:
    - Enhance table v2 pages:
        - Privilege - Internal Privilege
    - Remove parameter limit and page for download weborder transaction details

## v1.58.0 - 22 April 2022
- Feature:
    - User List add new Field NIK Identifier:

## v1.57.5 - 22 April 2022
- Hotfix:
    - Enhance table v2 pages:
        - Sales - Sales Report Agent
        - Privilege - CMS Privilege
        - Master - Support Master - Menu Location Price
        - Support - Message - Broadcast Message

## v1.57.4 - 18 April 2022
- Hotfix:
    - Enhance table v2 pages:
        - Content Review
        - Marketplace - Tokopedia Integration
        - Web Portal - Blog
        - Bantuan - Daftar Bantuan

## v1.57.3 - 14 April 2022
- Hotfix:
    - Enhance table v2 pages:
        - Master - Support Master - Master Support
        - Deposit
        - Non Cash Payment - Non Cash Payment

## v1.57.2 - 12 April 2022
- Hotfix:
    - Fixing Validation Wallet Submission  

## v1.57.1 - 11 April 2022
- Hotfix:
    - Enhance table v2 pages:
      - Report - User Unsubscribe
      - Sales - Sales Report
      - Web Portal - Event

## v1.57.0 - 8 April 2022
- Feature:
    - Add alert validation to buy support page

## v1.56.1 - 7 April 2022
- Hotfix:
    - Enhance table v2 pages:
        - Report - Ringkasan Settlement
        - Setting - Watch List
                  - Block List
                  - Exception List
        - Support - Feedback
        - Web Portal - Solusi

## v1.56.0 - 5 April 2022
- Feature:
    - Blog:
        - Add FAQ for BLOG

      
## v1.55.5 - 4 April 2022
- Hotfix:
    - Enhance table v2 pages:
        - Master - Support Master - Category
        - Sales - Pipeline
        - Profile - Order Profile
        - Web Portal - News & Promo

## v1.55.4 - 1 April 2022
- Hotfix:
    - Enhance table v2 pages:
      - Report - Settlement

## v1.55.3 - 1 April 2022
- Hotfix:
    -GrabFood Detail: add param outlet id

## v1.55.2 - 28 Maret 2022
- Hotfix:
    - Enhance table v2 pages:
        - Support - Claim
        - Website Report - Mau Majoo
        - Website Report - Demo Majoo
        - Website Report - Demo Invitation
        - Website Report - General Question
        - Support - Backup DB User
        - Business Developmenet - Business Coaching
        - Support - Support Access Login
        - Web Portal - Privasi & Policy
        - Web Portal - Terms & Conditions

## v1.55.1 - 25 Maret 2022
- Hotfix:
    - Remove parameter limit and page for download weborder report

## v1.55.0 - 16 Maret 2022
- Refactor code support pembelian
- Location Profile: fixing table column NaN

## v1.54.1 - 15 Maret 2022
- Hotfix:
    - Update table campaign to Table V2
    - Grab Submission - Remove Provider because it no longer uses

## v1.54.0 - 7 Maret 2022
- Feature:
    - Added Landing Pages to avoid Slow Loading when first login
  
## v1.53.0 - 25 Februari 2022
- Feature:
    - Added sales code and nominal approval fields on Business Loan

## v1.52.0 - 25 Februari 2022
- Enhancement:
    - Grab: enable edit mid

## v1.51.0 - 11 Februari 2022
- Enhancement:
    - OVO wording
- New Feature:
    - Laporan dan Download Grab Summary

## v1.50.5 - 11 Februari 2022
- Hotfix:
    - fix wording grabfood integration

## v1.50.4 - 11 Februari 2022
- Hotfix:
    - fixing maxima auto renewal
    - fixing approval wallet
    
## v1.50.3 - 10 Februari 2022
- Hotfix:
    - update API alpha in webpack.dev.js

## v1.50.2 - 8 Februari 2022
- Hotfix:
    - Table user unsubscribe fixing server side bug

## v1.50.1 - 3 Februari 2022
- Hotfix:
    - added padding on QR code - Wallet approval

## v1.50.0 - 2 Februari 2022
- Release:
    - New Feature:
        - Wallet Enhancement
        - Submission History
        - Download list view pengajuan wallet

## v1.49.2 - 27 Januari 2022
- Hotfix:
    - Fix table paginate after input search
    - Update table location profile to Table V2

## v1.49.1 - 21 Januari 2022
- Hotfix:
    - Page Banner Sorting & Label Banner Resolution & Size

## v1.49.0 - 15 Januari 2022
- Release:
    - New Feature:
        - Page Banner 

## v1.48.0 - 7 Januari 2022
- Release:
    - New Feature:
        - Outlet Profile : Change UI for Widget Setting from Dropdown to Popup
        - Suppor Pembelian : Add Auto Monthly Auto Renewal for Support Maxima Stater and Maxima Advance
        - New menu for Terminate support
        - Fixing UI for Support Grant Access menu 

## v1.47.0 - 3 Januari 2022
- Release:
    - New Feature: Operations -> Status Produk dan Bahan

## v1.46.4 - 31 Desember 2021
- Hotfix:
    - Fix author name missing on Solusi Page (REVAMP)

## v1.46.3 - 30 Desember 2021
- Hotfix:
    - Fix author name missing on Solusi Page

## v1.46.2 - 20 Desember 2021
- Hotfix:
    - Add fields Grab merchant id on campaign leads template
    - Update template to v3

## v1.46.1 - 13 Desember 2021
- Hotfix:
    - Grabfood Submission: adding more space on table coloumn email

## v1.46.0 - 9 Desember 2021
- Release:
    - Instagram Campaign

## v1.45.0 - 8 Desember 2021
- Release:
    - Weborder Settlement Report

## v1.44.1 - 8 Desember 2021
- Hotfix:
    - Adjust vacancy API (change option for level and work type)

## v1.44.0 - 8 Desember 2021
- Release:
    - Grab Submission p2

## v1.43.0 - 2 Desember 2021
- Release:
    - Enhance Fitur Vacancy (add new fields and logic)

## v1.42.4 - 1 Oktober 2021
- Hotfix:
    - Fix email validation

## v1.42.3 - 1 Oktober 2021
- Hotfix:
    - Fixing Settlement and Summary Settlement Report Search bug

## v1.42.2 - 30 September 2021
- Hotfix:
    - Fix tiny but strong error when trying to export campaign leads data

## v1.42.1 - 29 September 2021
- Hotfix:
    - Fixing whatsapp message content on Content Review Page
    - Change API for Campaign Leads to v2
    - Small Adjustment campaign lead download key

## v1.42.0 - 28 September 2021
- Feature:
    - User Profile 1.2:
        - enhance page Account Information

## v1.41.1 - 23 September 2021
- Hotfix:
    - Remove console.log
    - Enable downloading data on Page Campaign Leads

## v1.41.0 - 22 September 2021
- Feature:
    - Move Routes from Sales > Portal Report to Sales > Website Report (/portal/ to /website-report/).
    - Add new Feature "Campaign Leads" under /website-report/campaign-leads
    - Update table from 6.10.0 to ^6.10.0 on package.json

## v1.40.2 - 20 September 2021
- Hotfix:
    - Redirect to 'Sales > Portal Report > General Question' when login for role 14

## v1.40.1 - 7 September 2021
- Hotfix:
    - Lending fixing typo word

## v1.40.0 - 6 September 2021
- Feature:
    - Lending BRI KUR Approval Submission:

## v1.39.2 - 4 September 2021
- Hotfix:
    - Google maps apis:
        - change api key
        - implement to component

## v1.39.1 - 31 Agustus 2021
- Hotfix:
    - Grab submission approval: fixing payload

## v1.39.0 - 30 Agustus 2021
- Feature:
    - Enhance grab submission

## v1.38.6 - 18 Agustus 2021
- Hotfix:
    - Fixing loading wallet submission when change filter status & provider 

## v1.38.5 - 18 Agustus 2021
- Hotfix:
    - Fixing loading wallet submission

## v1.38.4 - 13 Agustus 2021
- Hotfix:
    - EDC Submission upgrade table to V2

## v1.38.3 - 10 Agustus 2021
- Hotfix:
    - set react-table to version 6.10.0

## v1.38.2 - 10 Agustus 2021
- Hotfix:
    - Settings
        - Bantuan 
            - Kategori Bantuan
                - Fixing Status
            - Daftar Bantuan
                - Fixing Status
        - Manage Klopos User
            - Block List
                - Block List use empty value with "-"
            - Exeption List
                - Exeption List use empty value with "-"
        - Non-cash Payment
            - EDC Submission
                - Fixing Status wording
        - Manage Content
            - Privacy And Policy 
                - Update wording Privacy and Policy
            - Web Portal
                - Blog
                    - BLog Author Fixing
                    - Fix blog preview
        - Advertisement: 
                - Add validation for subscription type
                - Add validation for Button Text and Action
    - User
        - Report
            - New User List
                - Fixing Dropdown Fillter
    - Profile
        - Informasi Akun
            - Eslint Fixing
    - Portal
        - - Update wording Demo majoo

## v1.38.1 - 1 Agustus 2021
- Hotfix:
    - Fixing reset password nge-freeze
    - push package-lock.json

## v1.38.0 - 28 Juli 2021
Feature:
    - Fix filter selection colliding (settlement, settlement report)
    - Fix missing minutes format on chart
    - Fix assignButton bugs on some page
    - Restyling the 'no data' rows on table v2
    - Add page ready state on user activity
## v1.37.3 - 29 Juli 2021
- Hotfix:
    - Select Box CSS Fixing

## v1.37.2 - 28 Juli 2021
- Hotfix:
    - Fixing Send Email Commbank Cockpit

## v1.37.1 - 22 Juli 2021
- Hotfix:
    - Lending using single route

## v1.37.0 - 19 Juli 2021
Feature:
    - Enhance User Cockpit

## v1.36.6 - 14 Juli 2021
Hotfix:
    - enhance filters table v2
    - Blog: use slug instead of title on blog preview

## v1.36.5 - 13 Juli 2021
Hotfix: 
    - Revamp table User Inactive
    - Update sidebar on Content Review

## v1.36.4 - 12 Juli 2021
Hotfix:
    - Fixing API Portal (event): remove bearer, add Token

## v1.36.3 - 7 Juli 2021
Hotfix:
- Profile Outlet
    - Fix Parameter Owner Email

## v1.36.2 - 5 Juli 2021
Hotfix:
- Wallet Submission
    - Email Validation
    - Add Default Outlet List

## v1.36.1 - 5 Juli 2021
Hotfix:
- Enhance fetchApi

## v1.36.0 - 21 Juni 2021
Feature:
- Job Vacancy

## v1.35.6 - 21 Juni 2021
Hotfix:
- Wallet Submission
    - Fixing Create cannot save for Wallet submission
    - Fixing Detail EDC Submission

## v1.35.5 - 17 Juni 2021
Hotfix:
- Wallet Submission
    - Fixing Wallet Provider Fillter
    - Takout provider Go Pay from Summary Billter Report  
## v1.35.4 - 8 Juni 2021
Hotfix:
- Wallet Submission
    - Adding Wallet Submission from Cockpit now avilable

## v1.35.3 - 8 Juni 2021
Hotfix:
- Supplies
    - Adding shipment date

## v1.35.2 - 3 Juni 2021
Hotfix:
    - Grab Submission cannot save submission fixing
## v1.35.1 - 28 Mei 2021
Hotfix:
    - Grab Merchant: add new param profile picture

## v1.35.0 - 25 Mei 2021
Feature
    - Wallet Approval new filter and endpoint
## v1.34.2 - 18 Mei 2021
Hotfix:
    - Enhance Supplies
    - Allow input number for custom slug in solusi usaha and blogpost

## v1.34.1 - 6 Mei 2021
Hotfix:
    - Commbank Download Detail
    - TselPoin Coupon Editable

## v1.34.0 - 6 Mei 2021
Feature:
    - Supplies v1.1

## v1.33.0 - 4 Mei 2021
Feature:
    - Add fitur custom slug di blogpost dan solusi-usaha

## v1.32.2 - 27 April 2021
Hotfix:
    - Page Setting News, Solusi, Blog & News dan Promo
        - Enhance filter date
## v1.32.1 - 26 April 2021
Hotfix:
    - Lending V2
        - Commbank show notification when this submission already sent email
## v1.32.0 - 23 April 2021
Feature:
    - User profile

## v1.31.1 - 23 April 2021
- fixing error build

## v1.31.0 - 23 April 2021
Feature:
    - refactor export default, based on latest requirement of eslint
    - update content review to v2
    - update Paket Kampanye, added LBA Telkomsel and WhatsApp
    - update API & parameter buy support (post & put)

## v1.30.3 - 16 April 2021
Hotfix:
    - Settlement update status bug no rek fixing

## v1.30.2 - 15 April 2021
Hotfix:
    - Input Sidebar Supplies Master

## v1.30.1 - 14 April 2021
Hotfix:
    - Replace Static URL

## v1.30.0 - 13 April 2021
Feature:
    - Approval Tokopedia Cockpit

## v1.29.2 - 13 April 2021
Hotfix:
    - Laporan Settlement fixing delimiter for import CSV
    - Laporan Settlement UI fillter
## v1.29.1 - 8 April 2021
Hotfix:
    - Solusi & Blog:
        - add validation when creating new category

## v1.29.0 - 7 April 2021
Feature:
    - Solusi

## v1.28.2 - 5 April 2021
Hotfix:
    - EDC Submission
        - changing endpoint 'Upload berkas' EDC from mayang to santan
        
## v1.28.1 - 5 April 2021
Hotfix:
    - Fixing Subtotal price in detail supplies

## v1.28.0 - 5 April 2021
Feature:
    - Improvement Wizard 1.1
        - Tambah field Lama Beroperasi
        - Atur Widget Kolom
        
## v1.27.0 - 30 Maret 2021
Feature:
    - setting env

## v1.26.1 - 30 Maret 2021
Hotfix:
    - Update status Settlement Report Fixing

## v1.26.0 - 29 Maret 2021
Feature:
    - Leading new provider Commbank Bank

## v1.25.0 - 26 Maret 2021
Feature:
    - Link Advertisement
    - Supplies

## v1.24.1 - 4 Maret 2021
Bugfix:
    - Laporan Demo Invitation (portal) : menambahkan location / kota

## v1.24.0 - 23 Februari 2021
Hotfix:
    - Laporan Settlement
        - Laporan Settlement
        - Laporan Ringkasan Settlement

## v1.23.4 - 22 Februari 2021
Hotfix:
    - Advertisement: add validation for small ad
    - Wallet Fixing Multi Outlet and Id Cabang at MID for ShopeePay


## v1.23.3 - 17 Februari 2021
Hotfix:
    - Grab Food Integration add new status and Number of Products
## v1.23.2 - 9 Februari 2021
Hotfix:
    - EDC Submission: fix error submit data

## v1.23.1 - 8 Februari 2021
Hotfix:
    - News & Promo: Change image ratio

## v1.23.0 - 5 Februari 2021
Feature:
    - News & Promo

## v1.22.3 - 5 Februari 2021
- pemindahan penulisan changelog
- penambahan tutorial first running project

## v1.22.2 - 1 Februari 2021
Hotfix
    - update react-tooltip version from 3.4.0 to 3.10.0
    - fix-management-content-event (image blink)
    
## v1.22.1 - 27 Januari 2021
Hotfix
    - Handel Outlet Profile Verfication Phone API not UP yet
## v1.22.0 - 27 Januari 2021
Feature
    - activity outlet profile 
    - show status revenue in web checkout

## v1.21.0 - 25 Januari 2021
Feature
    - Support access login 

## v1.20.1 - 19 Januari 2021
Hotfix
    - Wallet Biller
        - Fileupload still uses api_santan, not mayang
        - Outlet not found fixing
## v1.20.0 - 13 Januari 2021
Feature
    - Grab Food Integration
    - Wallet Submission V2.0
## v1.19.1 - 3 December 2020
Hotfix
    - Wallet Submission
        -  Jika Provider ShopeePay maka MID nya menjadi 'MERCHANT_{M_User_id_user}:STORE_{userCabang_user}'
        -  User Dropdown diambil menggunakan Email

## v1.19.0 - 24 November 2020
Feature 
    - Grabfood Submission

## v1.18.0 - 23 November 2020
Feature
    - kolom baru untuk backupDB user (APK version dan OS)

## v1.17.5 - 23 November 2020
Hotfix
    - create new componenet inputPhone for event.

## v1.17.4 - 06 November 2020
Hotfix
    - remove mayang klopos

## v1.17.3 - 06 November 2020
Hotfix
    - upload file wallet: change API version
    - wallet provider default terisi

## v1.17.2 - 30 Oktober 2020
Hotfix
    - Deposit add Summary
    - Add payment by column at Outlet Profile

## v1.17.1 - 6 Oktober 2020
Hotfix
    - Event Upload Image fixing

## v1.17.0 - 6 Oktober 2020
Feature 
    - Event Management for Portal and CMS
    - Core Marketplace Approval Grab Food
    - New Advertisement (with small inbox)

## v1.16.0 - 8 September 2020
Feature 
- New Web Check out Service
    - Manual Activation
- Business Loan
    - New Data Rekening Usaha
- Tsel Poin
    - Data Coupon
    - Download Voucher

## v1.15.9 - 7 September 2020
Hotfix
- Report User Unsubscribe
    - Add serverside Sorting and Desc
- Report Inactive User
    - Add serverside Sorting and Desc
- Profile Outlet Profile
    - Add limit Data Filter
- Support Pembelian
    - Add 15 character Limit for Phone Input
- Advertisement
    - Add new Component HTMLReturn for Tabel and fixing wrong notification
- Optimize the size for the barchart

## v1.15.8 - 4 September 2020
Hotfix
- Report User Unsubscribe
    - Fixing Date Range Format Filter
- Report Inactive User
    - Fixing Version API Fetch and Server Side

## v1.15.7 - 2 September 2020
Hotfix
- Report User Unsubscribe
    - Fixing Date Range Filter

## v1.15.6 - 31 Agustus 2020
Hotfix
- Profile Location: 
    - Fixing UI/UX
- Support Tambah Cockpit
    - Fixing save buying Support
- Report User Unsubscribe
    - Fixing Version API Fetch and Server Side

## v1.15.5 - 21 Agustus 2020
Hotfix
- Global Uploader: 
    - Change endpoint for Upload File in Live

## v1.15.4 - 19 Agustus 2020
Hotfix
- Advertisement: 
    - Fixing block by Adblock Extension
    - Icon Upload Fixing (Create, Edit, and Delete)

## v1.15.3 - 05 Agustus 2020
Hotfix:
- Perpanjangan Outlet: set default qty 1
- Add Filter for Starter and Advance
    - Profile : Outlet Profile
    - Report : Inactive User
    - Report : User Unsubscribe
- Advertisement :
	- Change input content from TextArea to TextEditor

## v1.15.2 - 29 Juli 2020
Hotfix:
- Master Support: set default support qty 1

## v1.15.1 - 29 Juli 2020
Hotfix:
- Buying Support show Notification when save with 0 qty support

## v1.14.0 - 02 Juli 2020
Enhance:
- Hak Akses

## v1.13.3 - 26 Juni 2020
Hotfix : 
- Wallet Submission Filter Not Working Fixing.
- Buying Support when user is trial can't input 0 qty.

## v1.13.2 - 03 Juni 2020
Bugfix : 
- add payload user_id at global_uploader

## v1.13.1 - 03 Juni 2020
Bugfix : 
- Advertisement
  - Fixing Global Uploader
  - Rename path Ads to Spanduk

## v1.13.0 - 08 Mei 2020
Feature:
    - enhance sales leads form on mobile
    - wallet telkomsel poin

## v1.12.1 -  27 April 2020
Hotfix:
    - change API_BASE from /mayang to /api_santan

## v1.12.0 -  23 April 2020
Feature: 
    - Approval pengajuan pinjaman
    - Blog
        - Added Highlight and Seq Features
    - No Cash Payment
        - Active and Inactive Settings
## v1.11.5 -  26 April 2020
Hotfix:
    - config server:
        - typo on resolved file path
        - update host

## v1.11.4 -  23 April 2020
Hotfix:
    - Sales
        - Support Pembelian
            -  Pembelian Support Reguler can't add Outlet installations even before they already have outlets but can still extend the outlets expiration
    - Advertisment
        - Fixing menu Group

## v1.11.3 -  22 April 2020
Bugfix:
    - Advertisement
        - Fixing Edit Icon for Advertisement

## v1.11.2 -  21 April 2020
Hotfix:
    - Sales
        - Support Pembelian
            - Pembelian Support Reguler can't add Outlet installations even before they already have outlets

## v1.11.1 -  14 April 2020
Hotfix:
    - Sales
        - Support Pembelian
            - Pembelian Support Reguler can't add Outlet fixing 

## v1.11.0 -  3 April 2020
feature:
    - Advertisement

## v1.10.1 -  1 April 2020
hotfix:
    - Outlet Profile:
        - Optimizing Menu

## v1.10.0 -  1 April 2020
feature:
    - Wallet Payment:
        - Search Filter

## v1.9.2 -  24 Maret 2020
hotfix:
    - Change Dokumen Wajib at EDC Submission
    - Change Select Placeholder From Choose One to Pilih....

## v1.9.1 -  24 Maret 2020
hotfix:
    - Wallet Submission page : display approval notification

## v1.9.0 -  10 Maret 2020
feature:
    - Portal Report:
        - Demo Invitation download Excel
        - General Question download Excel
        - Mau Majoo download Excel
        - Demo Mobile download Excel

enhancement:
    - community: preview detail information
    - Set delimiter for Y Index:
        - Live:
            - Growth
            - Revenue
            - Transaction
    - sales funnnel:
        - hide (simplify) beberapa inputan
        - update location: auto fill province, city, kecamatan
        - update wording: agar lebih mudah dimengerti sales
        - re-arrange urutan input
        - implement pagination
        - add pilihan aplikasi kasir lainnya
        - add pilihan hasil meeting by category leads
## v1.8.2 -  17 Maret 2020
enhancement:
    - Outlet Profile:
        - add Label TMBS at Column Company
        - Download Excel now Available

## v1.8.1 -  11 Maret 2020
enhancement:
    - rename MDR to MDR + Biaya Layanan

## v1.8.0 -  23 Februari 2020
feature:
    - operations:
        - mitra
        - community
        - backupdb
        - feedback
        - inquiry
        - new user

enhancement:
    - web checkout service:
        - penambahan TMBS
        - dipindah ke menu operations
    - profile business: update ui
    - mitra & community: preview detail information

bugfix:
    - user device: filter device type & app version, ekspektasinya bisa multiple
## v1.7.3 -  24 Februari 2020
bugfix:
    - wallet submission: add serverside tabel

## v1.7.2 -  24 Februari 2020
bugfix:
    - buy support: cannot add pic and email

## v1.7.1 -  21 Februari 2020
bugfix:
    - cannot find superviser due to limitation by pagination

## v1.7.0 -  15 Februari 2020
enhancement:
    - sales funnel
        - re-wording
        - hide unnecessary input field on form while create new  

## v1.6.0 -  15 Februari 2020
enhancement:
    - filtering outlet map dari server

## v1.5.1 - 17 Februari 2020
enhancement:
    - component Input Percent now can add Decimal Input

## v1.5.0 - 04 Februari 2020
- auto format NPWP

## v1.4.0 - 04 Februari 2020
enhancement:
    - upload image: add option whether to crop or not

## v1.4.0 - 04 Februari 2020
enhancement:
    - laporan user active: reword user active to outlet active
    - outlet map: by default 3 hari terakhir
    - outlet profile: add ditambahkan email owner / akun utama
    - fixing reset password: error on life cycle

## v1.4.0 - 28 Januari 2020
feature:
    - web check out service
    - add new list mitra and community

bugfix:
    - outlet map ngrefresh secara berkala ketika load data dari api
    - detail outlet map error (tidak ada respon ketika di klik)

hotfix:
    - set limit outlet map to 500 record per request
    - disable approveemnt from internal if use have choose midtrans payment
    - update logic master package

## v1.2.6 - 24 Januari 2020
- backupdb: bug fix autocomplete user email tidak muncul

## v1.2.5 - 27 Desember 2019
- wallet submission: add param **user_id** when saving data

## v1.2.4 - 19 Desember 2019
- wallet submission: add column **Business Name**

## v1.2.3 - 18 Desember 2019
- wallet submission: float button

## v1.2.2 - 17 Desember 2019
- wallet submission: tidak boleh edit ketika status sudah **Approved**

## v1.2.1 - 17 Desember 2019
- wallet submission: fixing null value settlement buku kas

## v1.2.0 - 14 Desember 2019
- wallet submission

## v1.1.1 - 04 Desember 2019
- bug fix wallet user active daily

## v1.1.0 - 28 November 2019
- wallet user active
- summary biller daily: bug fix filter outlet
- summary biller daily: table footer hitung total transaction dan mdr
- summary biller daily: bugfix format date

## v1.0.5 - 26 November 2019
- update version API biller
- summary biller daily: bug fix filter outlet 

## v1.0.4 - 19 November 2019
- fixing URL API biller

## v1.0.3 - 25 Oktober 2019
- laporan summary biller

## v1.0.2 - 17 Oktober 2019
- fixing layout sales invoice

## v1.0.1 - 16 Oktober 2019
- bug fix popup atur perpanjangan outlet
