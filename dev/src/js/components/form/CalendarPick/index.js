import React from 'react';
import PropTypes from 'prop-types';
import MonthView from 'react-calendar';
import { convertToDate, convertDateToText } from './utils';
import { formatDate, ishasProperty, setNativeValue } from '../../../utils/helper';

import './style.css';

export default class CalendarPick extends React.Component {
    static propTypes = {
        startDate: PropTypes.string,
        endDate: PropTypes.string,
        defaultDate: PropTypes.string,
        type: PropTypes.string,
        className: PropTypes.string,
        changeEvent: PropTypes.func,
        disabled: PropTypes.bool,
        popoverAlign: PropTypes.string,
        minDate: PropTypes.instanceOf(Date),
        maxDate: PropTypes.instanceOf(Date),
        parentHeight: PropTypes.number,
        isScrolling: PropTypes.bool,
        name: PropTypes.string,
        required: PropTypes.bool,
    }

    static defaultProps = {
        startDate: '',
        endDate: '',
        defaultDate: '',
        type: '',
        className: '',
        disabled: false,
        popoverAlign: '',
        minDate: null,
        maxDate: null,
        parentHeight: undefined,
        isScrolling: false,
        name: undefined,
        required: false,
        changeEvent: () => {},
    }

    constructor(props) {
        super(props);

        this.state = {
            showPopover: false,
            startDate: '',
            endDate: '',
            savedDate: '',
            msg: '',
            customStyle: {},
        };
    }

    componentDidMount = () => {
        const {
            startDate, endDate, type, defaultDate,
        } = this.props;

        this.setState({
            startDate,
            endDate,
            savedDate: type === 'range' ? `${formatDate(startDate, 'dd mmm yyyy')} - ${formatDate(endDate, 'dd mmm yyyy')}` : formatDate(defaultDate, 'dd mmm yyyy'),
        });
    }

    static getDerivedStateFromProps(nextProps, prevState) {
        if (nextProps.type === 'range') {
            if (prevState.savedDate !== `${formatDate(nextProps.startDate, 'dd mmm yyyy')} - ${formatDate(nextProps.endDate, 'dd mmm yyyy')}`) {
                return {
                    startDate: nextProps.startDate,
                    endDate: nextProps.endDate,
                    savedDate: `${formatDate(nextProps.startDate, 'dd mmm yyyy')} - ${formatDate(nextProps.endDate, 'dd mmm yyyy')}`,
                };
            }
        } else if (prevState.savedDate !== formatDate(nextProps.defaultDate, 'dd mmm yyyy') && nextProps.defaultDate !== '') {
            return {
                startDate: nextProps.startDate,
                endDate: nextProps.endDate,
                savedDate: formatDate(nextProps.defaultDate, 'dd mmm yyyy'),
            };
        } else if (nextProps.defaultDate === '') {
            return {
                startDate: '',
                endDate: '',
                savedDate: '',
            };
        }

        return null;
    }

    shouldComponentUpdate = (nextProps, nextState) => {
        const { disabled } = this.props;

        if (this.state !== nextState) return true;
        if (disabled !== nextProps.disabled) return true;

        return false;
    };

    componentDidUpdate = () => {
        const { showPopover } = this.state;
        const { isScrolling } = this.props;

        if (!isScrolling && showPopover) this.calendarBox.focus();
    }

    handleBlur = (e) => {
        const { currentTarget } = e;
        if (!currentTarget.contains(e.relatedTarget)) {
            setTimeout(() => {
                const { isScrolling } = this.props;

                if (!isScrolling) {
                    this.setState({
                        showPopover: false,
                    });
                }
            }, 100);
        }
    }

    handleRangeChange = (range) => {
        if (!range) return;
        if (range && range.length === 0) return;

        const [startDateObj, endDateObj] = range;

        const startDate = convertDateToText(startDateObj);
        const endDate = convertDateToText(endDateObj);
        this.setState({ startDate, endDate });
    }

    handleChange = (dateObj) => {
        const date = convertDateToText(dateObj);
        this.setState({
            savedDate: formatDate(date, 'dd mmm yyyy'),
            showPopover: false,
        });

        this.handleTriggerInputChange(date);
    }

    handleApplyChange = () => {
        const { changeEvent } = this.props;
        const { startDate, endDate } = this.state;

        if (startDate === undefined && endDate === undefined) {
            this.setState({
                msg: '*Pilih rentang waktu tanggal. Jika ingin menampilkan hanya 1 hari saja, klik 2x pada tanggal tersebut.',
            });

            return;
        }

        this.setState({
            msg: '',
            savedDate: (startDate === endDate) ? formatDate(startDate, 'dd mmm yyyy') : `${formatDate(startDate, 'dd mmm yyyy')} - ${formatDate(endDate, 'dd mmm yyyy')}`,
            showPopover: false,
        });

        changeEvent(startDate, endDate);
    }

    handleCancelChange = () => {
        const {
            startDate, endDate, type, defaultDate,
        } = this.props;

        this.setState({
            startDate,
            endDate,
            savedDate: type === 'range' ? `${formatDate(startDate, 'dd mmm yyyy')} - ${formatDate(endDate, 'dd mmm yyyy')}` : formatDate(defaultDate, 'dd mmm yyyy'),
            msg: '',
            showPopover: false,
        });
    }

    handleTogglePopover = () => {
        const { disabled, parentHeight } = this.props;
        const { showPopover } = this.state;

        if ((ishasProperty(this.props, 'disabled') && !disabled) || !ishasProperty(this.props, 'disabled')) {
            this.setState({ showPopover: !showPopover }, () => {
                const {
                    top, bottom, height, x,
                } = this.popupCalendar.getBoundingClientRect();
                const zIndex = 11;
                if (parentHeight <= bottom || top - height > 120) {
                    this.setState({
                        customStyle: {
                            bottom: 45,
                            left: x + 500 > window.innerWidth ? -70 : 0,
                            zIndex,
                        },
                    });
                } else {
                    x + 500 > window.innerWidth
                        ? this.setState({ customStyle: { left: -70, zIndex } })
                        : this.setState({ customStyle: { zIndex } });
                }
            });
        }
    }

    handleTriggerInputChange = (value = '') => {
        const e = new Event('input', { bubbles: true });
        const { input } = this;
        setNativeValue(input, value);
        input.dispatchEvent(e);
    }

    handleChangeSelected = (e) => {
        const { changeEvent } = this.props;
        const { target: { value } } = e;

        changeEvent(value, e);
    }

    _changeDateStyle = ({ date, view }) => {
        const today = new Date();
        const convertDateFormatted = date.toLocaleString('id-ID', { day: '2-digit', month: '2-digit', year: 'numeric' });
        const todayDateFormatted = today.toLocaleString('id-ID', { day: '2-digit', month: '2-digit', year: 'numeric' });
        if (view === 'month' && convertDateFormatted === todayDateFormatted) {
            return 'today-date';
        }

        return '';
    }

    _changeSingleDateStyle = ({ date, view }) => {
        const { startDate } = this.state;
        const convertDateFormatted = date.toLocaleString('id-ID', { day: '2-digit', month: '2-digit', year: 'numeric' });
        if (view === 'month' && convertDateFormatted === startDate) {
            return 'selected-single-date';
        }

        return '';
    }

    render() {
        const {
            popoverAlign, type, className, disabled,
            name, required, startDate, endDate, minDate, maxDate, defaultDate,
        } = this.props;
        const {
            msg, savedDate, showPopover, customStyle,
        } = this.state;

        return (
            <div
                className={`calendar-box clearfix${
                    popoverAlign ? ` align-${popoverAlign}` : ''
                    }${type === 'range' ? ' isRange' : ''
                    }${className ? ` ${className}` : ''
                    }${(ishasProperty(this.props, 'disabled') && disabled) ? ' input-disabled' : ''}`
                }
                tabIndex="-1"
                ref={(div) => { this.calendarBox = div; }}
                onBlur={e => this.handleBlur(e)}
            >
                {/* validation */}
                <input
                    name={name}
                    type="text"
                    required={required}
                    ref={(c) => { this.input = c; }}
                    onChange={this.handleChangeSelected}
                    readOnly
                    style={{
                        position: 'absolute', opacity: 0, left: '-300px', width: '0px', height: '0px',
                    }}
                    value={savedDate}
                />
                {/* end validation */}
                <div
                    className="calendar-bar"
                    onClick={this.handleTogglePopover}
                    role="presentation"
                >
                    <div className="calendar-icon"><i className="fa fa-calendar-o" /></div>
                    <div className="calendar-content">{savedDate}</div>
                </div>
                <div
                    className={`calendar-popover${!showPopover ? ' hidden' : ''}`}
                    ref={(div) => { this.popupCalendar = div; }}
                    style={customStyle}
                >
                    {
                        type === 'range' && (
                        <MonthView
                            nextLabel={<i className="fa fa-chevron-right" />}
                            prevLabel={<i className="fa fa-chevron-left" />}
                            tileClassName={this._changeDateStyle}
                            next2Label={null}
                            prev2Label={null}
                            onChange={this.handleRangeChange}
                            defaultValue={[startDate ? convertToDate(startDate) : '', endDate ? convertToDate(endDate) : '']}
                            showWeekNumbers={false}
                            returnValue="range"
                            locale="id-ID"
                            selectRange
                            {...!minDate ? {} : { minDate }}
                        />
                      )}
                    {
                        type !== 'range' && (
                        <MonthView
                            nextLabel={<i className="fa fa-chevron-right" />}
                            prevLabel={<i className="fa fa-chevron-left" />}
                            tileClassName={this._changeSingleDateStyle}
                            next2Label={null}
                            prev2Label={null}
                            onChange={this.handleChange}
                            value={defaultDate === '' ? null : convertToDate(defaultDate)}
                            locale="id-ID"
                            showWeekNumbers={false}
                            {...!minDate ? {} : { minDate }}
                            {...!maxDate ? {} : { maxDate }}
                        />
                      )}
                    <div className="calendar-action clearfix mt-xs">
                        <button type="button" className="btn btn-default pull-left" onClick={this.handleCancelChange}>Cancel</button>
                        <button type="button" className="btn btn-primary pull-right" onClick={this.handleApplyChange}>Apply</button>
                    </div>
                    <div className="calendar-action clearfix">
                        <font color="red" style={{ fontSize: 12 }}>{msg}</font>
                    </div>
                </div>
            </div>
        );
    }
}
