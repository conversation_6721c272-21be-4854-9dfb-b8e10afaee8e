import { exact } from 'prop-types';
import asyncRoute from '../components/asyncRoute';

const USER = [
    {
        path: 'live',
        name: 'Live',
        childRoutes: [
            {
                breadcrumbIgnore: true,
                exact: true,
                redirect: '/live/growth',
            },
            {
                path: 'growth',
                name: 'Growth',
                component: asyncRoute(() => import('../pages/User/Live/Growth')),
            },
            {
                path: 'newuser',
                name: 'New User',
                component: asyncRoute(() => import('../pages/User/Live/NewUser')),
            },
            {
                path: 'activeuser',
                name: 'Active User',
                component: asyncRoute(() => import('../pages/User/Live/Activeuser')),
            },
            {
                path: 'transaction',
                name: 'Transaction',
                component: asyncRoute(() => import('../pages/User/Live/Transaction')),
            },
            {
                path: 'support',
                name: 'Support',
                component: asyncRoute(() => import('../pages/User/Live/Support')),
            },
            {
                path: 'subscription',
                name: 'Subscription',
                component: asyncRoute(() => import('../pages/User/Live/Subscription')),
            },
            {
                path: 'revenue',
                name: 'Revenue',
                component: asyncRoute(() => import('../pages/User/Live/Revenue')),
            },
            {
                path: 'sales',
                name: 'Sales',
                component: asyncRoute(() => import('../pages/User/Live/Sales')),
            },
        ],
    },
    {
        path: 'profile',
        name: 'Profile',
        childRoutes: [
            {
                breadcrumbIgnore: true,
                exact: true,
                redirect: '/profile/active',
            },
            {
                path: 'active',
                name: 'User Active',
                component: asyncRoute(() => import('../pages/User/Profile/Active')),
            },
            {
                path: 'device',
                name: 'User Devices',
                component: asyncRoute(() => import('../pages/User/Profile/UserDevice')),
            },
            {
                path: 'map',
                name: 'User Maps',
                component: asyncRoute(() => import('../pages/User/Profile/Maps')),
            },
            {
                path: 'loc',
                name: 'Location',
                component: asyncRoute(() => import('../pages/User/Profile/Location')),
            },
            {
                path: 'business',
                name: 'Business Profile',
                component: asyncRoute(() => import('../pages/User/Profile/Business')),
            },
            {
                path: 'order',
                name: 'Order Profile',
                component: asyncRoute(() => import('../pages/User/Profile/Order')),
            },
            {
                path: 'payment',
                name: 'Payment Profile',
                component: asyncRoute(() => import('../pages/User/Profile/Payment')),
            },
            {
                path: 'feature',
                name: 'Feature Profile',
                component: asyncRoute(() => import('../pages/User/Profile/Feature')),
            },
            {
                path: 'outletProfile',
                name: 'Outlet Profile',
                component: asyncRoute(() => import('../pages/User/Profile/OutletProfile')),
            },
            {
                path: 'activity',
                name: 'User Activity',
                component: asyncRoute(() => import('../pages/User/Profile/Activity')),
            },
            {
                path: 'account-information',
                name: 'Account Information',
                component: asyncRoute(() => import('../pages/User/Profile/AccountInformation')),
            },
        ],
    },
    {
        path: 'report',
        name: 'Report',
        childRoutes: [
            {
                breadcrumbIgnore: true,
                exact: true,
                redirect: '/report/user-growth',
            },
            {
                path: 'user-growth',
                name: 'User Growth',
                component: asyncRoute(() => import('../pages/User/Report/UserGrowth')),
            },
            {
                path: 'outlet-active',
                name: 'Outlet Active',
                component: asyncRoute(() => import('../pages/User/Report/OutletActive')),
            },
            {
                path: 'wallet-active',
                name: 'Wallet Active',
                component: asyncRoute(() => import('../pages/User/Report/UserWalletActive')),
            },
            {
                path: 'telkomsel-point',
                name: 'Telkomsel Point Report',
                component: asyncRoute(() => import('../pages/User/Report/TelkomselPointReport')),
            },
            {
                path: 'user-inactive',
                name: 'User Inactive',
                component: asyncRoute(() => import('../pages/User/Report/Inactive')),
            },
            {
                path: 'user-unsubscribe',
                name: 'User Unsubscribe',
                component: asyncRoute(() => import('../pages/User/Report/Unsubscribe')),
            },
            {
                path: 'transaction-growth',
                name: 'Transaction Growth',
                component: asyncRoute(() => import('../pages/User/Report/TransactionGrowth')),
            },
            {
                path: 'summary-grab-transaction',
                name: 'Summary Grab Transaction',
                component: asyncRoute(() => import('../pages/User/Report/Grab/SummaryGrabTransaction')),
            },
            {
                path: 'new-user',
                name: 'New User',
                component: asyncRoute(() => import('../pages/User/Report/NewUser')),
            },
            {
                path: 'shipment-report',
                name: 'Shipment Report',
                component: asyncRoute(() => import('../pages/User/Report/ShipmentReport')),
            },
            {
                path: 'biller',
                name: 'Biller',
                childRoutes: [
                    {
                        breadcrumbIgnore: true,
                        exact: true,
                        redirect: '/report/biller/summary-daily',
                    },
                    {
                        path: 'summary-daily',
                        name: 'Summary Biller Daily',
                        childRoutes: [
                            {
                                exact: true,
                                component: asyncRoute(() => import('../pages/User/Report/Biller/SummaryBillerDaily')),
                            },
                            {
                                path: 'detail-biller/:id',
                                name: 'Summary Biller Daily',
                                noCheckRole: true,
                                component: asyncRoute(() => import('../pages/User/Report/Biller/DetailBillerDailyReport')),
                            },
                        ],
                    },
                    {
                        path: 'summary-per-merchant',
                        name: 'Summary Biller Per Merchant',
                        component: asyncRoute(() => import('../pages/User/Report/Biller/SummaryBillerMerchant')),
                    },
                ],
            },
            {
                path: 'settlement',
                name: 'Settlement',
                childRoutes: [
                    {
                        breadcrumbIgnore: true,
                        exact: true,
                        redirect: '/report/settlement/settlement',
                    },
                    {
                        path: 'settlement',
                        name: 'Laporan Settlement',
                        component: asyncRoute(() => import('../pages/User/Report/Settlement/SettlementReport')),
                    },
                    {
                        path: 'ringkasan-settlement',
                        name: 'Laporan Ringkasan Settlement',
                        component: asyncRoute(() => import('../pages/User/Report/Settlement/SettlementSummaryReport')),
                    },
                    {
                        path: 'reconciliation',
                        name: 'Reconciliation',
                        component: asyncRoute(() => import('../pages/User/Report/Settlement/AutoReconciliation')),
                    },
                ],
            },
            {
                path: 'weborder',
                name: 'Weborder Report',
                childRoutes: [
                    {
                        breadcrumbIgnore: true,
                        exact: true,
                        redirect: '/report/weborder/transaction-details-report',
                    },
                    {
                        path: 'transaction-details-report',
                        name: 'Weborder Transaction Details',
                        component: asyncRoute(() => import('../pages/User/Report/Weborder/TransactionDetailsReport')),
                    },
                    {
                        path: 'transaction-summary-report',
                        name: 'Weborder Transaction Summary',
                        component: asyncRoute(() => import('../pages/User/Report/Weborder/TransactionSummaryReport')),
                    },
                    {
                        path: 'settlement-request',
                        name: 'Weborder Settlement Request',
                        component: asyncRoute(() => import('../pages/User/Report/Weborder/SettlementRequest')),
                    },
                ],
            },
            {
                path: 'transaction-online-payment',
                name: 'Transaction Online Payment',
                childRoutes: [
                    {
                        breadcrumbIgnore: true,
                        exact: true,
                        redirect: '/report/transaction-online-payment/payment-method',
                    },
                    {
                        path: 'payment-method',
                        name: 'Manage Metode Pembayaran',
                        component: asyncRoute(() => import('../pages/Settings/ManagePaymentMethod')),
                    },
                    {
                        path: 'payment-report',
                        name: 'Laporan Pembayaran',
                        component: asyncRoute(() => import('../pages/Settings/PaymentReport')),
                    },
                    {
                        path: 'report-va',
                        name: 'Laporan Virtual Account',
                        component: asyncRoute(() => import('../pages/Settings/Biller/ReportVa')),
                    },
                ],
            },
        ],
    },
    {
        path: 'onboarding',
        name: 'Onboarding',
        childRoutes: [
            {
                breadcrumbIgnore: true,
                exact: true,
                redirect: '/onboarding/mitra',
            },
            {
                path: 'mitra',
                name: 'Mitra',
                component: asyncRoute(() => import('../pages/User/Onboarding/Mitra')),
            },
            {
                path: 'community',
                name: 'Community',
                component: asyncRoute(() => import('../pages/User/Onboarding/Community')),
            },
        ],
    },
    {
        path: 'operations',
        name: 'Operations',
        childRoutes: [
            {
                path: 'feedback',
                name: 'Feed Back',
                component: asyncRoute(() => import('../pages/User/Operations/Feedback')),
            },
            {
                path: 'backupdb',
                name: 'Backup DB',
                component: asyncRoute(() => import('../pages/User/Operations/Backupdb')),
            },
            {
                path: 'inquiry',
                name: 'Inquiry',
                component: asyncRoute(() => import('../pages/User/Operations/Inquiry')),
            },
            {
                path: 'mitra',
                name: 'Mitra',
                component: asyncRoute(() => import('../pages/User/Operations/Mitra')),
            },
            {
                path: 'community',
                name: 'Community',
                component: asyncRoute(() => import('../pages/User/Operations/Community')),
            },
            {
                path: 'webcheckout',
                name: 'Web Check Out Service',
                component: asyncRoute(() => import('../pages/User/Operations/WebCheckout')),
            },
            {
                path: 'statusproduct',
                name: 'Status Produk dan Bahan',
                component: asyncRoute(() => import('../pages/User/Operations/StatusProduct')),
            },
            {
                path: 'item-used-transactions',
                name: 'Items Used For Transactions',
                component: asyncRoute(() => import('../pages/User/Operations/ItemUsedTransactions')),
            },
            {
                path: 'consumerapp-activerequest',
                name: 'Consumer App Activation Request',
                component: asyncRoute(() => import('../pages/User/Operations/ConsumerAppActivation')),
            },
            {
                path: 'user-transaction-biller-log',
                name: 'User Transaction Biller Log',
                component: asyncRoute(() => import('../pages/User/Operations/UserTransactionBillerLog')),
            },
            {
                path: 'productRecipe',
                name: 'User Product Recipe',
                component: asyncRoute(() => import('../pages/User/Operations/ProductRecipe')),
            },
        ],
    },
    {
        path: 'majoo-lite',
        name: 'Majoo Lite',
        childRoutes: [
            {
                path: 'data-verification',
                name: 'Data Verification',
                component: asyncRoute(() => import('../pages/User/MajooLite/DataVerification')),
            },
            {
                path: 'wallet-submission',
                name: 'Wallet Submission',
                childRoutes: [
                    {
                        exact: true,
                        component: asyncRoute(() => import('../pages/User/MajooLite/WalletSubmmision/Wallet')),
                    },
                    {
                        path: ':type/:id/:idOutlet',
                        name: 'Detail',
                        noCheckRole: true,
                        component: asyncRoute(() => import('../pages/User/MajooLite/WalletSubmmision/AddWallet')),
                    },
                ],
            },
            {
                path: 'transaction-report',
                name: 'Transaction Report',
                childRoutes: [
                    {
                        exact: true,
                        redirect: '/majoo-lite/transaction-report/transaction-details',
                    },
                    {
                        path: 'transaction-details',
                        name: 'Transaction Details',
                        component: asyncRoute(() => import('../pages/User/MajooLite/TransactionReport/TransactionDetails')),
                    },
                    {
                        path: 'transaction-summary',
                        name: 'Transaction Summary',
                        component: asyncRoute(() => import('../pages/User/MajooLite/TransactionReport/TransactionSummary')),
                    },
                    {
                        path: 'settlement-request',
                        name: 'Settlement Request',
                        component: asyncRoute(() => import('../pages/User/MajooLite/TransactionReport/SettlementRequest')),
                    },
                ],
            },
            {
                path: 'big-pop-up',
                name: 'Big Pop Up',
                childRoutes: [
                    {
                        exact: true,
                        component: asyncRoute(() => import('../pages/User/MajooLite/BigPopUp')),
                    },
                    {
                        path: 'add-popup',
                        name: 'Add Big Pop Up',
                        noCheckRole: true,
                        exact: true,
                        component: asyncRoute(() => import('../pages/User/MajooLite/BigPopUp/addForm')),
                    },
                    {
                        path: 'edit-popup/:id',
                        name: 'Add Big Pop Up',
                        noCheckRole: true,
                        component: asyncRoute(() => import('../pages/User/MajooLite/BigPopUp/addForm')),
                    },
                ],
            },
            {
                path: 'notifications',
                name: 'Notification',
                childRoutes: [
                    {
                        exact: true,
                        component: asyncRoute(() => import('../pages/User/MajooLite/Notifications')),
                    },
                    {
                        path: 'create',
                        name: 'Notification',
                        noCheckRole: true,
                        exact: true,
                        component: asyncRoute(() => import('../pages/User/MajooLite/Notifications/form')),
                    },
                    {
                        path: ':type/:id',
                        name: 'Notification',
                        noCheckRole: true,
                        component: asyncRoute(() => import('../pages/User/MajooLite/Notifications/form')),
                    },
                ],
            },
        ],
    },
    {
        path: 'kasbon',
        name: 'Kasbon',
        childRoutes: [
            {
                breadcrumbIgnore: true,
                exact: true,
                redirect: '/kasbon/outlet',
            },
            {
                path: 'outlet',
                name: 'Daftar Outlet',
                childRoutes: [
                    {
                        exact: true,
                        component: asyncRoute(() => import('../pages/User/Kasbon/Outlet')),
                    },
                    {
                        path: 'create',
                        name: 'Detail Outlet',
                        component: asyncRoute(() => import('../pages/User/Kasbon/Outlet/form')),
                    },
                ],
            },
            {
                path: 'request-list',
                name: 'Daftar Request Kasbon',
                component: asyncRoute(() => import('../pages/User/Cashbon')),
            },
        ],
    },
    {
        path: 'konsultan',
        name: 'Konsultan',
        childRoutes: [
            {
                exact: true,
                component: asyncRoute(() => import('../pages/User/Consultant')),
            },
            {
                path: ':id',
                name: 'Upload Berkas',
                noCheckRole: true,
                exact: true,
                component: asyncRoute(() => import('../pages/User/Consultant/detail')),
            },
            {
                path: ':id/:type',
                name: 'Upload Berkas',
                noCheckRole: true,
                exact: true,
                component: asyncRoute(() => import('../pages/User/Consultant/form')),
            },
            {
                path: ':id/:type/:id_laporan',
                name: 'Upload Berkas',
                exact: true,
                noCheckRole: true,
                component: asyncRoute(() => import('../pages/User/Consultant/form')),
            },
        ],
    },
];

const SALES = [
    {
        path: 'support',
        name: 'Support',
        breadcrumbIgnore: true,
        childRoutes: [
            {
                exact: true,
                redirect: '/support/pembelian',
            },
            {
                path: 'pembelian',
                name: 'Pembelian',
                component: asyncRoute(() => import('../pages/Sales/Support/Pembelian')),
            },
            {
                path: 'Claim',
                name: 'Claim',
                component: asyncRoute(() => import('../pages/Sales/Support/Claim')),
            },
            {
                path: 'feedback',
                name: 'Feedback',
                component: asyncRoute(() => import('../pages/Sales/Support/Feedback')),
            },
            {
                path: 'price-location',
                name: 'Menu Location Price',
                component: asyncRoute(() => import('../pages/Sales/Support/supportLocation')),
            },
            {
                path: 'voucher',
                name: 'Voucher',
                component: asyncRoute(() => import('../pages/Sales/Promo/Voucher')),
            },
            {
                path: 'terminate',
                name: 'Termination',
                component: asyncRoute(() => import('../pages/Sales/Support/Terminate')),
            },
        ],
    },
    {
        path: 'supplies',
        name: 'Supplies',
        childRoutes: [
            {
                exact: true,
                component: asyncRoute(() => import('../pages/Sales/Supplies/Supplies')),
            },
            {
                path: 'whitelist',
                name: 'Whitelist Outlet',
                component: asyncRoute(() => import('../pages/Sales/Supplies/WhitelistOutlets')),
            },
            {
                path: 'supplies-products',
                name: 'Supplies Products',
                childRoutes: [
                    {
                        exact: true,
                        redirect: '/supplies',
                    },
                    {
                        path: 'terlaris',
                        name: 'Terlaris',
                        component: asyncRoute(() => import('../pages/Sales/Supplies/SuppliesProduct/BestSeller')),
                        noCheckRole: true,
                    },
                    {
                        path: 'termurah',
                        name: 'Termurah',
                        component: asyncRoute(() => import('../pages/Sales/Supplies/SuppliesProduct/Cheapest')),
                        noCheckRole: true,
                    },
                    {
                        path: 'terbaru',
                        name: 'Terbaru',
                        component: asyncRoute(() => import('../pages/Sales/Supplies/SuppliesProduct/Newest')),
                        noCheckRole: true,
                    },
                ],
            },
        ],
    },
    {
        path: 'master',
        name: 'Master',
        breadcrumbIgnore: true,
        childRoutes: [
            {
                exact: true,
                redirect: '/master/mastersupport',
            },
            {
                path: 'mastersupport',
                name: 'Support Master',
                component: asyncRoute(() => import('../pages/Sales/Support/supportmaster')),
            },
            {
                path: 'category',
                name: 'Support Category',
                component: asyncRoute(() => import('../pages/Sales/Support/category')),
            },
            {
                path: 'categorysupplies',
                name: 'Category Supplies',
                component: asyncRoute(() => import('../pages/Sales/Supplies/category')),
            },
            {
                path: 'supplysetup',
                name: 'Setup Supply Trans',
                component: asyncRoute(() => import('../pages/Sales/Supplies/Setup')),
            },
            {
                path: 'mastersupplies',
                name: 'Supplies Master',
                component: asyncRoute(() => import('../pages/Sales/Supplies/master')),
            },
            {
                path: 'smsprice',
                name: 'SMS Price',
                component: asyncRoute(() => import('../pages/Sales/Sms/master')),
            },
        ],
    },
    {
        path: 'campaign',
        name: 'Campaign',
        breadcrumbIgnore: true,
        childRoutes: [
            {
                path: 'campaign',
                name: 'Campaign',
                component: asyncRoute(() => import('../pages/Sales/Campaign/campaign')),
            },
            {
                path: 'package',
                name: 'Campaign Package',
                component: asyncRoute(() => import('../pages/Sales/Campaign/package')),
            },
        ],
    },
    {
        path: 'funnel',
        name: 'Sales Funnel',
        breadcrumbIgnore: true,
        childRoutes: [
            {
                exact: true,
                component: asyncRoute(() => import('../pages/Sales/Funnel/Redirect')),
            },
            {
                path: 'funnel-list',
                name: 'Sales Funnel',
                childRoutes: [
                    {
                        path: 'detail',
                        name: 'Sales Funnel Detail',
                        exact: true,
                        component: asyncRoute(() => import('../pages/Sales/Funnel/DetailFunnel')),
                    },
                    {
                        exact: true,
                        component: asyncRoute(() => import('../pages/Sales/Funnel/funnel')),
                    },
                ],
            },
            {
                path: 'sales-report',
                name: 'Sales Report',
                childRoutes: [
                    {
                        exact: true,
                        component: asyncRoute(() => import('../pages/Sales/Funnel/Salesreport')),
                    },
                    {
                        path: 'salesinvoice',
                        name: 'Sales Invoice',
                        component: asyncRoute(() => import('../pages/Sales/Funnel/Component/Funnelinvoice')),
                    },
                    {
                        path: 'salesinvoice/:id',
                        name: 'Sales Invoice',
                        component: asyncRoute(() => import('../pages/Sales/Funnel/Component/Funnelinvoice')),
                    },
                    {
                        path: 'salesclient',
                        name: 'Sales Client',
                        component: asyncRoute(() => import('../pages/Sales/Funnel/Component/AddFunnel')),
                    },
                    {
                        path: 'salesclient/:id',
                        name: 'Sales Client',
                        component: asyncRoute(() => import('../pages/Sales/Funnel/Component/AddFunnel')),
                    },
                ],
            },
            {
                path: 'report-agent',
                name: 'Sales Report Agent',
                component: asyncRoute(() => import('../pages/Sales/Funnel/Funnelreport')),
            },
        ],
    },
    {
        path: 'website-report',
        name: 'Website Report',
        breadcrumbIgnore: true,
        childRoutes: [
            {
                exact: true,
                redirect: '/website-report/demo-invitation',
            },
            {
                path: 'demo-invitation',
                name: 'Demo Invitation',
                component: asyncRoute(() => import('../pages/Sales/Portal/Demo')),
            },
            {
                path: 'general-question',
                name: 'General Question',
                component: asyncRoute(() => import('../pages/Sales/Portal/GeneralQuestion')),
            },
            {
                path: 'collaboration',
                name: 'Collaboration',
                component: asyncRoute(() => import('../pages/Sales/Portal/Collaboration')),
            },
            {
                path: 'mau-majoo',
                name: 'Mau Majoo',
                component: asyncRoute(() => import('../pages/Sales/Portal/MauMajoo')),
            },
            {
                path: 'demo-mobile',
                name: 'Demo Mobile',
                component: asyncRoute(() => import('../pages/Sales/Portal/DemoMobile')),
            },
            {
                path: 'campaign-leads',
                name: 'Campaign Leads',
                component: asyncRoute(() => import('../pages/Sales/Portal/CampaignLeads')),
            },
        ],
    },
    {
        path: 'deposit',
        name: 'Deposit',
        component: asyncRoute(() => import('../pages/Sales/Deposit/Deposit')),
    },
    {
        path: 'domain-premium',
        name: 'Domain Premium',
        childRoutes: [
            {
                exact: true,
                component: asyncRoute(() => import('../pages/Sales/DomainPremium')),
            },
            {
                path: 'detail/:id',
                name: 'Detail',
                noCheckRole: true,
                component: asyncRoute(() => import('../pages/Sales/DomainPremium/Detail')),
            },
        ],
    },
];

const SUPPORT = [
    {
        path: 'supportUser',
        name: 'Support',
        breadcrumbIgnore: true,
        childRoutes: [
            {
                exact: true,
                name: 'Backup DB User',
                component: asyncRoute(() => import('../pages/Support/Backupdb/Backup')),
            },
            {
                path: 'backupSync',
                name: 'Backup/Sync',
                component: asyncRoute(() => import('../pages/Support/Backupdb/BackupDatabase')),
            },
            {
                path: 'deleteUserTrans',
                name: 'Delete User Trans',
                component: asyncRoute(() => import('../pages/Support/Usertransaction/Deletetransaction')),
            },
            {
                path: 'accessLogin',
                name: 'Support Access Login',
                component: asyncRoute(() => import('../pages/Support/AccessLogin/AccessLogin')),
            },
            {
                path: 'tools',
                name: 'Supporting Tools',
                childRoutes: [
                    {
                        path: 'delete-transaction',
                        name: 'Delete Transaction (per nota)',
                        component: asyncRoute(() => import('../pages/Support/Tools/DeleteTransaction')),
                    },
                    {
                        path: 'recovery-tutup-kasir',
                        name: 'Recovery Tutup Kasir',
                        component: asyncRoute(() => import('../pages/Support/Tools/RecoveryTutupKasir')),
                    },
                    {
                        path: 'get-metode-pembayaran-transaksi',
                        name: 'Get Metode Pembayaran Transaksi',
                        component: asyncRoute(() => import('../pages/Support/Tools/GetMetodePembayaran')),
                    },
                    {
                        path: 'delete-tutup-kasir',
                        name: 'Delete Tutup Kasir',
                        component: asyncRoute(() => import('../pages/Support/Tools/DeleteTutupKasir')),
                    },
                    {
                        path: 'update-tutup-kasir',
                        name: 'Update Tutup Kasir',
                        component: asyncRoute(() => import('../pages/Support/Tools/UpdateTutupKasir')),
                    },
                    {
                        path: 'update-email-merchant',
                        name: 'Update Email/Outlet Merchant',
                        component: asyncRoute(() => import('../pages/Support/Tools/UpdateEmailMerchant')),
                    },
                ],
            },
        ],
    },
    {
        path: 'message',
        name: 'Message',
        breadcrumbIgnore: true,
        childRoutes: [
            {
                exact: true,
                redirect: '/message/broadcast',
            },
            {
                path: 'broadcast',
                name: 'Broadcast Message',
                component: asyncRoute(() => import('../pages/Support/Message/BroadcastMessage')),
            },
        ],
    },
    {
        path: 'business-dev',
        name: 'Business Development',
        breadcrumbIgnore: true,
        childRoutes: [
            {
                exact: true,
                redirect: '/business-dev/coaching',
            },
            {
                path: 'coaching',
                name: 'Business Coaching',
                component: asyncRoute(() => import('../pages/Support/BusinessDevelopment/BusinessCoaching')),
            },
            {
                path: 'loan',
                name: 'Business Loan',
                childRoutes: [
                    {
                        exact: true,
                        component: asyncRoute(() => import('../pages/Support/BusinessDevelopment/BusinessLoaning')),
                    },
                    {
                        path: ':type/:id',
                        name: 'Detail',
                        noCheckRole: true,
                        component: asyncRoute(() => import('../pages/Support/BusinessDevelopment/BusinessLoaningDetail')),
                    },
                ],
            },
        ],
    },
];

const SETTING = [
    {
        path: 'help',
        name: 'Bantuan',
        breadcrumbIgnore: true,
        childRoutes: [
            {
                exact: true,
                redirect: '/help/help-list',
            },
            {
                path: 'help-list',
                name: 'Daftar Bantuan',
                component: asyncRoute(() => import('../pages/Settings/Help/HelpList')),
            },
            {
                path: 'help-category',
                name: 'Kategori Bantuan',
                component: asyncRoute(() => import('../pages/Settings/Help/HelpCategory')),
            },
        ],
    },
    {
        path: 'user',
        name: 'Majoo Admin',
        breadcrumbIgnore: true,
        childRoutes: [
            {
                exact: true,
                redirect: '/user/user-list',
            },
            {
                path: 'user-list',
                name: 'User List',
                component: asyncRoute(() => import('../pages/Settings/User/UserList')),
            },
            {
                path: 'informasi-akun',
                name: 'Account Info',
                noCheckRole: true,
                component: asyncRoute(() => import('../pages/Settings/User/AccountInfo')),
            },
            {
                path: 'privilege',
                name: 'Access Privilege',
                component: asyncRoute(() => import('../pages/Settings/User/Privilege')),
            },
        ],
    },
    {
        path: 'manage-user',
        name: 'Manage majoo User',
        breadcrumbIgnore: true,
        childRoutes: [
            {
                exact: true,
                redirect: '/manage-user/watch',
            },
            {
                path: 'watch',
                name: 'Watch List',
                component: asyncRoute(() => import('../pages/Settings/ManageUser/WatchList')),
            },
            {
                path: 'exception',
                name: 'Exception List',
                component: asyncRoute(() => import('../pages/Settings/ManageUser/ExceptionList')),
            },
            {
                path: 'block',
                name: 'Block List',
                component: asyncRoute(() => import('../pages/Settings/ManageUser/BlockList')),
            },
        ],
    },
    {
        path: 'privilage',
        name: 'Manage Privilege',
        breadcrumbIgnore: true,
        childRoutes: [
            {
                exact: true,
                redirect: '/privilage/menuinternal',
            },
            {
                path: 'menuinternal',
                name: 'Internal Menu List',
                component: asyncRoute(() => import('../pages/Settings/Priviliege/MenuInternal')),
            },
            {
                path: 'menucms',
                name: 'CMS Menu List',
                component: asyncRoute(() => import('../pages/Settings/Priviliege/MenuCms')),
            },
            {
                path: 'menuretina',
                name: 'Retina Menu List',
                component: asyncRoute(() => import('../pages/Settings/Priviliege/MenuRetina')),
            },
            {
                path: 'cmsrole',
                name: 'CMS Privilege List',
                childRoutes: [
                    {
                        exact: true,
                        component: asyncRoute(() => import('../pages/Settings/Priviliege/CmsRole')),
                    },
                    {
                        path: ':type/:id',
                        name: 'Detail CMS User Role',
                        noCheckRole: true,
                        component: asyncRoute(() => import('../pages/Settings/Priviliege/MenuRetina')),
                    },
                ],
            },
            {
                path: 'internalrole',
                name: 'Internal Privilege List',
                childRoutes: [
                    {
                        exact: true,
                        component: asyncRoute(() => import('../pages/Settings/Priviliege/InternalRole')),
                    },
                    {
                        path: ':type/:id',
                        name: 'Detail Internal User Role',
                        noCheckRole: true,
                        component: asyncRoute(() => import('../pages/Settings/Priviliege/InternalRoleDetail')),
                    },
                ],
            },
            {
                path: 'retinarole',
                name: 'Retina Privilege List',
                childRoutes: [
                    {
                        exact: true,
                        component: asyncRoute(() => import('../pages/Settings/Priviliege/MenuRetina/RetinaRole')),
                    },
                    {
                        path: ':type/:id',
                        name: 'Detail CMS User Role',
                        noCheckRole: true,
                        component: asyncRoute(() => import('../pages/Settings/Priviliege/MenuRetina/RetinaRoleDetail')),
                    },
                ],
            },
        ],
    },
    {
        path: 'web-portal',
        name: 'Web Portal',
        breadcrumbIgnore: true,
        childRoutes: [
            {
                exact: true,
                redirect: '/web-portal/privacy-policy',
            },
            {
                path: 'privacy-policy',
                name: 'Privacy And Policy',
                component: asyncRoute(() => import('../pages/Settings/ManageContent/Privacyandpolicy')),
            },
            {
                path: 'term-and-condition',
                name: 'Terms And Condition',
                component: asyncRoute(() => import('../pages/Settings/ManageContent/Termandcondition')),
            },
            {
                path: 'blog',
                name: 'Blog',
                childRoutes: [
                    {
                        exact: true,
                        component: asyncRoute(() => import('../pages/Settings/ManageContent/Blog')),
                    },
                    {
                        path: 'create',
                        name: 'New Post',
                        exact: true,
                        component: asyncRoute(() => import('../pages/Settings/ManageContent/NewPost')),
                    },
                    {
                        path: ':type/:id',
                        name: 'Edit Post',
                        noCheckRole: true,
                        component: asyncRoute(() => import('../pages/Settings/ManageContent/NewPost')),
                    },
                ],
            },
            {
                path: 'solusi',
                name: 'Solusi',
                childRoutes: [
                    {
                        exact: true,
                        component: asyncRoute(() => import('../pages/Settings/ManageContent/Solusi')),
                    },
                    {
                        path: 'create',
                        name: 'New Post',
                        component: asyncRoute(() => import('../pages/Settings/ManageContent/NewPost')),
                    },
                    {
                        path: 'edit/:id',
                        name: 'Edit Post',
                        noCheckRole: true,
                        component: asyncRoute(() => import('../pages/Settings/ManageContent/NewPost')),
                    },
                ],
            },
            {
                path: 'news-portal',
                name: 'News',
                childRoutes: [
                    {
                        exact: true,
                        component: asyncRoute(() => import('../pages/Settings/ManageContent/NewsPortal')),
                    },
                    {
                        path: 'create',
                        name: 'New Post',
                        component: asyncRoute(() => import('../pages/Settings/ManageContent/NewPost')),
                    },
                    {
                        path: 'edit/:id',
                        name: 'Edit Post',
                        noCheckRole: true,
                        component: asyncRoute(() => import('../pages/Settings/ManageContent/NewPost')),
                    },
                ],
            },
            {
                path: 'news-and-promo',
                name: 'News & Promo',
                childRoutes: [
                    {
                        exact: true,
                        component: asyncRoute(() => import('../pages/Settings/ManageContent/NewsAndPromo')),
                    },
                    {
                        path: 'add',
                        name: 'New Post',
                        component: asyncRoute(() => import('../pages/Settings/ManageContent/NewsAndPromoForm')),
                    },
                    {
                        path: 'edit/:id',
                        name: 'Edit Post',
                        noCheckRole: true,
                        component: asyncRoute(() => import('../pages/Settings/ManageContent/NewsAndPromoForm')),
                    },
                ],
            },
            {
                path: 'news',
                name: 'News',
                childRoutes: [
                    {
                        exact: true,
                        component: asyncRoute(() => import('../pages/Settings/ManageContent/News')),
                    },
                    {
                        path: 'add',
                        name: 'New Post',
                        component: asyncRoute(() => import('../pages/Settings/ManageContent/NewPost')),
                    },
                    {
                        path: 'edit/:id',
                        name: 'Edit Post',
                        noCheckRole: true,
                        component: asyncRoute(() => import('../pages/Settings/ManageContent/NewPost')),
                    },
                ],
            },
            {
                path: 'event',
                name: 'Event',
                childRoutes: [
                    {
                        exact: true,
                        component: asyncRoute(() => import('../pages/Settings/ManageContent/Event')),
                    },
                    // {
                    //     path: ':type',
                    //     name: 'Event',
                    //     exact: true,
                    //     noCheckRole: true,
                    //     component: asyncRoute(() => import('../pages/Settings/ManageContent/Event/EventForm')),
                    // },
                    {
                        path: ':type/:id?',
                        name: 'Event',
                        noCheckRole: true,
                        component: asyncRoute(() => import('../pages/Settings/ManageContent/Event/EventForm')),
                    },
                ],
            },
            {
                path: 'vacancy',
                name: 'Vacancy',
                childRoutes: [
                    {
                        exact: true,
                        redirect: 'vacancy/vacancy-list',
                    },
                    {
                        path: 'vacancy-list',
                        name: 'Vacancy',
                        component: asyncRoute(() => import('../pages/Settings/ManageContent/Vacancy/VacancyList')),
                    },
                ],
            },
            {
                path: 'consumer-app',
                name: 'Consumer App',
                component: asyncRoute(() => import('../pages/Settings/ManageContent/ConsumerApp')),
            },
            {
                path: 'supplies-form-portal',
                name: 'Supplies Form Portal',
                component: asyncRoute(() => import('../pages/Settings/ManageContent/SuppliesFormPortal')),
            },
            {
                path: 'promo',
                name: 'promo',
                noCheckRole: true,
                component: asyncRoute(() => import('../pages/Settings/ManageContent/Promo')),
            },
        ],
    },
    {
        path: 'settings',
        name: 'Settings',
        breadcrumbIgnore: true,
        childRoutes: [
            {
                exact: true,
                redirect: '/settings/advertisement',
            },
            {
                path: 'advertisement',
                name: 'Advertisement',
                component: asyncRoute(() => import('../pages/Settings/ManageContent/Advertisement')),
            },
        ],
    },
    {
        path: 'manage-content',
        name: 'Manage Content',
        breadcrumbIgnore: true,
        childRoutes: [
            {
                exact: true,
                redirect: '/manage-content/page-banner',
            },
            {
                path: 'manage-image',
                name: 'Manage Image',
                component: asyncRoute(() => import('../pages/Settings/ManageContent/ManageImage')),
            },
            {
                path: 'page-banner',
                name: 'Page Banner',
                childRoutes: [
                    {
                        exact: true,
                        component: asyncRoute(() => import('../pages/Settings/ManageContent/PageBanner')),
                    },
                    {
                        path: 'add-banner',
                        name: 'Add Page Banner',
                        component: asyncRoute(() => import('../pages/Settings/ManageContent/PageBanner/FormPageBanner')),
                    },
                    {
                        path: 'edit-banner/:id',
                        name: 'Edit Page Banner',
                        noCheckRole: true,
                        component: asyncRoute(() => import('../pages/Settings/ManageContent/PageBanner/FormPageBanner')),
                    },
                ],
            },
            {
                path: 'text-banner',
                name: 'Banner Text',
                childRoutes: [
                    {
                        exact: true,
                        component: asyncRoute(() => import('../pages/Settings/ManageContent/TextBanner')),
                    },
                    {
                        path: 'add-banner',
                        name: 'Add Banner Text',
                        component: asyncRoute(() => import('../pages/Settings/ManageContent/TextBanner/Form')),
                    },
                    {
                        path: 'edit-banner/:id',
                        name: 'Edit Banner Text',
                        noCheckRole: true,
                        component: asyncRoute(() => import('../pages/Settings/ManageContent/TextBanner/Form')),
                    },
                ],
            },
            {
                path: 'tooltips-guidance',
                name: 'Tooltips Guidance',
                childRoutes: [
                    {
                        exact: true,
                        component: asyncRoute(() => import('../pages/Settings/ManageContent/TooltipsGuidance')),
                    },
                    {
                        path: 'add-tooltips',
                        name: 'Add Tooltips Guidance',
                        component: asyncRoute(() => import('../pages/Settings/ManageContent/TooltipsGuidance/FormTooltips')),
                    },
                    {
                        path: 'edit-tooltips',
                        name: 'Edit Tooltips Guidance',
                        component: asyncRoute(() => import('../pages/Settings/ManageContent/TooltipsGuidance/FormTooltips')),
                    },
                ],
            },
            {
                path: 'multilanguage',
                name: 'Multilanguage',
                childRoutes: [
                    {
                        exact: true,
                        component: asyncRoute(() => import('../pages/Settings/ManageContent/Multilanguage')),
                    },
                    {
                        path: 'add',
                        name: 'Add Multilanguage',
                        component: asyncRoute(() => import('../pages/Settings/ManageContent/Multilanguage/Form')),
                    },
                    {
                        path: 'edit',
                        name: 'Edit Multilanguage',
                        component: asyncRoute(() => import('../pages/Settings/ManageContent/Multilanguage/Form')),
                    },
                ],
            },
        ],
    },
    {
        path: 'non-cash-setting',
        name: 'Non-Cash Payment',
        breadcrumbIgnore: true,
        childRoutes: [
            {
                exact: true,
                redirect: '/non-cash-setting/wallet-payment',
            },
            {
                path: 'non-cash-setting',
                name: 'Non Cash Payment',
                component: asyncRoute(() => import('../pages/Settings/Biller/NonCashSetting')),
            },
            {
                path: 'dana-nmid',
                name: 'Dana NMID',
                component: asyncRoute(() => import('../pages/Settings/DanaNmid')),
            },
            {
                path: 'wallet-payment',
                name: 'Wallet Payment',
                childRoutes: [
                    {
                        exact: true,
                        component: asyncRoute(() => import('../pages/Settings/Biller/Wallet')),
                    },
                    {
                        path: ':type/:id',
                        name: 'Detail',
                        noCheckRole: true,
                        exact: true,
                        component: asyncRoute(() => import('../pages/Settings/Biller/AddWallet')),
                    },
                    {
                        path: ':type',
                        name: 'Create',
                        noCheckRole: true,
                        exact: true,
                        component: asyncRoute(() => import('../pages/Settings/Biller/AddWallet')),
                    },
                ],
            },
            {
                path: 'tsel-poin',
                name: 'Telkomsel Poin',
                childRoutes: [
                    {
                        exact: true,
                        component: asyncRoute(() => import('../pages/Settings/Biller/TSelPoin')),
                    },
                    {
                        path: ':type/:id',
                        name: 'Detail',
                        noCheckRole: true,
                        exact: true,
                        component: asyncRoute(() => import('../pages/Settings/Biller/AddTSelPoin')),
                    },
                    {
                        path: ':type',
                        name: 'Create',
                        noCheckRole: true,
                        exact: true,
                        component: asyncRoute(() => import('../pages/Settings/Biller/AddTSelPoin')),
                    },
                ],
            },
            {
                path: 'wallet-edc',
                name: 'Edc',
                childRoutes: [
                    {
                        exact: true,
                        component: asyncRoute(() => import('../pages/Settings/Biller/Edc')),
                    },
                    {
                        path: ':type/:id',
                        name: 'Detail',
                        noCheckRole: true,
                        exact: true,
                        component: asyncRoute(() => import('../pages/Settings/Biller/EdcDetail')),
                    },
                    {
                        path: ':type',
                        name: 'Create',
                        noCheckRole: true,
                        exact: true,
                        component: asyncRoute(() => import('../pages/Settings/Biller/EdcDetail')),
                    },
                ],
            },
        ],
    },
    {
        path: 'marketplace',
        name: 'Marketplace',
        breadcrumbIgnore: true,
        childRoutes: [
            {
                path: 'grabfood',
                name: 'Grab Food',
                childRoutes: [
                    {
                        exact: true,
                        component: asyncRoute(() => import('../pages/Support/MarketPlace/GrabFood/Index')),
                    },
                    {
                        path: 'detail/:id',
                        name: 'Detail',
                        noCheckRole: true,
                        component: asyncRoute(() => import('../pages/Support/MarketPlace/GrabFood/Detail')),
                    },
                ],
            },
            {
                path: 'grabfoodSubmission',
                name: 'Grab Food Submission',
                childRoutes: [
                    {
                        exact: true,
                        component: asyncRoute(() => import('../pages/Support/MarketPlace/Submission/GrabfoodSubmission')),
                    },
                    {
                        path: 'detail-grabfood/:id',
                        name: 'Detail',
                        noCheckRole: true,
                        component: asyncRoute(() => import('../pages/Support/MarketPlace/Submission/AddGrabfood')),
                    },
                ],
            },
            {
                path: 'tokopedia',
                name: 'Tokopedia',
                childRoutes: [
                    {
                        exact: true,
                        component: asyncRoute(() => import('../pages/Support/MarketPlace/Tokopedia/Index')),
                    },
                    {
                        path: 'detail/:id',
                        name: 'Detail',
                        noCheckRole: true,
                        component: asyncRoute(() => import('../pages/Support/MarketPlace/Tokopedia/Detail')),
                    },
                ],
            },
            {
                path: 'grabmart',
                name: 'Grabmart',
                childRoutes: [
                    {
                        exact: true,
                        component: asyncRoute(() => import('../pages/Support/MarketPlace/Grabmart/Index')),
                    },
                    {
                        path: 'detail/:id',
                        name: 'Detail',
                        noCheckRole: true,
                        component: asyncRoute(() => import('../pages/Support/MarketPlace/Grabmart/Detail')),
                    },
                ],
            },
        ],
    },
    {
        path: 'content-review',
        name: 'Content Review',
        component: asyncRoute(() => import('../pages/Settings/ContentReview/ContentReview')),
    },
    {
        path: 'setting-support-exp',
        name: 'Support Exp',
        component: asyncRoute(() => import('../pages/Settings/User/SupportExp')),
    },
];

const mainRoutes = {
    path: '/',
    breadcrumbIgnore: true,
    childRoutes: [
        {
            path: 'landing',
            name: 'landing',
            breadcrumbIgnore: true,
            component: asyncRoute(() => import('../pages/LandingPages/index')),
        },
        ...USER,
        ...SALES,
        ...SUPPORT,
        ...SETTING,
    ],
};

const authRoutes = {
    path: 'auth',
    breadcrumbIgnore: true,
    childRoutes: [
        {
            path: 'login',
            name: 'login',
            component: asyncRoute(() => import('../pages/Auth/Login')),
        },
        {
            path: 'logout',
            name: 'logout',
            component: asyncRoute(() => import('../pages/Auth/Login')),
        },
        {
            path: 'forgot-password',
            name: 'Forgot Password',
            component: asyncRoute(() => import('../pages/Auth/ForgotPassword')),
        },
        {
            path: 'reset-password',
            name: 'Reset Password',
            component: asyncRoute(() => import('../pages/Auth/ResetPassword')),
        },
    ],
};

const routes = [
    ...[{ ...authRoutes }],
    ...[{ ...mainRoutes }],
];

export default routes;
