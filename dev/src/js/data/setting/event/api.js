import { fetchApi } from '../../../services/api';

const endPoints = {
  getEvent: 'api/v2/event/list',
  saveEvent: 'api/v2/event',
  updateEvent: 'api/v2/event/update',
  getEventDetail: 'api/v2/event',
};

export const getEvent = payload => fetchApi(endPoints.getEvent, payload, 'get', {
    serviceDomainType: 'portal',
});

export const getEventDetail = payload => fetchApi(endPoints.getEventDetail, payload, 'get', {
  serviceDomainType: 'portal',
  slashId: payload.id,
});

export const deleteEvent = slashId => fetchApi(endPoints.saveEvent, {}, 'delete', {
  serviceDomainType: 'portal',
  slashId,
});

export const saveEvent = (payload) => {
    const formData = new FormData();
    Object.keys(payload).forEach((key) => {
        formData.append(key, payload[key]);
    });

    let endpoint = endPoints.saveEvent;
    let slashId;

    if (payload.id) {
      slashId = payload.id;
      endpoint = endPoints.updateEvent;
    }

    return fetchApi(
        endpoint,
        formData,
        'post',
        {
            serviceDomainType: 'portal',
            slashId,
        },
    );
};
