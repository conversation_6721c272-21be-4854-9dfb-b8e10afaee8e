import { fetchApi } from '../../../services/api';

const endPoints = {
  privacyPolicy: 'portal/privacy_and_policy',
  termAndCondition: 'portal/term_and_condition',
  contentCategory: 'news/category',
  contentTag: 'news/tag',
  contentPost: 'news/post',
  contentPostBulk: '/news/post_bulk',
  campaignLeads: 'api/campaign/lead',
  campaignList: 'api/campaign',
  portalConsumerApp: 'api/v0/consumer-app/list',
  registerAuthor: 'api/v0/author/register',
  privacyPolicyPortal: 'api/privacy-policy',
};

export const getTermsAndCondition = payload => fetchApi(endPoints.termAndCondition, payload);
export const getContentCategory = payload => fetchApi(endPoints.contentCategory, payload);
export const getContentTag = payload => fetchApi(endPoints.contentTag, payload);
export const getPost = payload => fetchApi(endPoints.contentPost, payload);

export const saveTermsAndCondition = payload => fetchApi(endPoints.termAndCondition, payload, 'POST');
export const savePost = payload => fetchApi(endPoints.contentPost, payload, 'POST');
export const saveContentCategory = payload => fetchApi(endPoints.contentCategory, payload, 'POST');
export const saveContentTag = payload => fetchApi(endPoints.contentTag, payload, 'POST');

export const updatePost = payload => fetchApi(endPoints.contentPost, payload, 'PUT');
export const updatePostBulk = payload => fetchApi(endPoints.contentPostBulk, payload, 'PUT');

export const getPortalCampaignLeads = payload => fetchApi(endPoints.campaignLeads, payload, 'get', {
  serviceDomainType: 'portal',
});

export const getPortalCampaign = payload => fetchApi(endPoints.campaignList, payload, 'get', {
  serviceDomainType: 'portal',
});

export const getPortalConsumerApp = payload => fetchApi(endPoints.portalConsumerApp, payload, 'get', {
  serviceDomainType: 'portal',
});

export const registerAuthor = payload => fetchApi(endPoints.registerAuthor, payload, 'POST', {
  serviceDomainType: 'portal',
});

export const getListPrivacyPolicyPortal = payload => fetchApi(`${endPoints.privacyPolicyPortal}/list`, payload, 'get', {
  serviceDomainType: 'portal',
});

export const getPrivacyPolicyPortal = slashId => fetchApi(endPoints.privacyPolicyPortal, {}, 'get', {
  serviceDomainType: 'portal',
  slashId,
});

export const createPrivacyPolicyPortal = payload => fetchApi(endPoints.privacyPolicyPortal, payload, 'POST', {
  serviceDomainType: 'portal',
});

export const updatePrivacyPolicyPortal = ({ id, ...payload }) => fetchApi(endPoints.privacyPolicyPortal, payload, 'PUT', {
  serviceDomainType: 'portal',
  slashId: id,
});

export const deletePrivacyPolicyPortal = slashId => fetchApi(endPoints.privacyPolicyPortal, {}, 'DELETE', {
  serviceDomainType: 'portal',
  slashId,
});
