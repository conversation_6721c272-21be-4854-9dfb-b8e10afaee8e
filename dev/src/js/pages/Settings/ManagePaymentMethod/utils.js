/* eslint-disable react/prop-types */
import React from 'react';
import moment from 'moment';
import { numSeparator } from '../../../utils/helper';

export const TableColumn = (allData, showPopup) => [
    {
        Header: () => null,
        id: 'expander',
        unsortable: true,
        Cell: (props) => {
            const {
                row: {
                    depth, getToggleRowExpandedProps, isExpanded, canExpand,
                },
            } = props;

            return (
                <div
                    style={{
                        display: 'flex',
                        alignItems: 'center',
                        gap: '0.5rem',
                        paddingLeft: `${depth * 1}rem`,
                        minWidth: 30,
                    }}
                >
                    {canExpand ? (
                        <div
                            {...getToggleRowExpandedProps()}
                        >
                            {isExpanded ? <i className="fa fa-chevron-up" /> : <i className="fa fa-chevron-down" />}
                        </div>
                    ) : null}
                </div>
            );
        },
    },
    {
        Header: 'Nama Metode Bayar',
        accessor: 'name',
        Cell: ({ value, row: { depth } }) => (
            <span style={{ paddingLeft: 15 * depth }}>
                {value}
            </span>
        ),
    },
    {
        Header: 'Group',
        accessor: 'group',
    },
    {
        Header: 'Type',
        accessor: 'type',
        Cell: ({ value }) => (value === 'va'
                ? value.toUpperCase()
                : value
                    .split('_')
                    .map(str => `${str[0].toUpperCase()}${str.slice(1)}`)
                    .join(' ')),
    },
    {
        Header: 'Last Update',
        accessor: 'updated_at',
        width: 150,
        Cell: ({ value, row: { original } }) => (
                <span>
                    {value
                        ? `${moment(value)
                            .locale('id')
                            .format('DD MMMM YYYY HH:mm')} WIB`
                        : '-'}
                    <br />
                    {original.updated_by}
                </span>
            ),
    },
    {
        Header: 'Admin Fees',
        id: 'admin_flat_fee',
        accessor: 'flat_fee',
        Cell: ({ value }) => `Rp ${numSeparator(value)}`,
    },
    {
        Header: 'Varias',
        id: 'varias_flat_fee',
        accessor: 'flat_fee',
        Cell: ({ value, row: { original } }) => {
            const flatFee = value > 0 ? `Rp ${numSeparator(value)}` : '';
            const percentageFee = original.percentage_fee > 0
                    ? `${original.percentage_fee}%`
                    : '';

            const plus = flatFee && percentageFee && ' + ';

            const final = `${flatFee}${plus}${percentageFee}` || '-';

            return final;
        },
    },
    {
        Header: 'Action',
        accessor: 'status',
        Cell: (props) => {
            if (!props) return null;
            const { row: { original }, rowsById } = props;
            return Array.isArray(original.sub_payment_method) && original.sub_payment_method.length > 0 ? '' : (
                <button
                    type="button"
                    onClick={() => {
                        const { parent_id: parentId, ...data } = original;
                        const parentRow = parentId && rowsById[parentId];

                        showPopup({
                            data: { ...data, prev_name: data.name },
                            parentData: parentRow ? parentRow.original : null,
                            isHaveChild: parentRow ? parentRow.original.sub_payment_method.length > 0 : false,
                        });
                    }}
                    style={{
                        background: 'none',
                        border: 'none',
                        color: 'inherit',
                        padding: 0,
                        cursor: 'pointer',
                    }}
                >
                    Ubah
                </button>
            );
        },
    },
];

export const FilterStatus = [
    { id: '', name: 'Semua Status' },
    { id: true, name: 'Aktif' },
    { id: false, name: 'Tidak Aktif' },
];

export const INITIAL_FORM_DATA = {
    name: '',
    group: '',
    type: 'va',
    flat_fee: 0,
    percentage_fee: 0,
    updated_at: '',
    updated_by: '',
    channel: [
        {
            name: 'Dashboard',
            is_active: false,
        },
        {
            name: 'CRM',
            is_active: false,
        },
        {
            name: 'Supplies',
            is_active: false,
        },
    ],
    additional_data: {
        constant_provider: '',
    },
};
