import React, { useRef, useState } from 'react';
import { connect } from 'react-redux';
import omit from 'lodash/omit';
import { Pagination } from '@majoo-ui/react';
import Table from '../../../components/retina/table/Table';
import InputSelect from '../../../components/form/Select';
import { FilterStatus, INITIAL_FORM_DATA, TableColumn } from './utils';
import usePaymentMethodList from './hooks/usePaymentMethodList';
import useUpdateStatus from './hooks/useUpdateStatus';
import SelectMultiple from '../../../components/form/SelectMultiple';
import SidePopup from '../../../components/sidepopup/ContainerV2';
import EditForm from './EditForm';
import InputText from '../../../components/form/InputText';

const ManagePaymentMethod = (props) => {
    const popupRef = useRef();
    const {
        data, setQuery, query, tempData, tableMeta, refetch, onFetch, isFetch,
    } = usePaymentMethodList(props);
    const { onUpdateStatus } = useUpdateStatus({ ...props, refetch });
    const [detail, setDetail] = useState(INITIAL_FORM_DATA);
    const [additionalData, setAdditionalData] = useState({ isHaveChild: false, parentData: [] });

    const showPopup = ({ data, parentData, isHaveChild }) => { popupRef.current.showPopup(); setDetail(data); setAdditionalData({ parentData, isHaveChild }); };
    const hidePopup = () => { popupRef.current.hidePopup(); setDetail(INITIAL_FORM_DATA); setAdditionalData({ parentData: [], isHaveChild: false }); };

    return (
        <div>
            <section className="panel">
                <div className="panel-heading table-header">
                    <h4 className="panel-title" style={{ paddingTop: '10px' }}>Manage Metode Pembayaran</h4>
                    <div className="panel-heading" style={{ marginBottom: '25px', marginTop: '10px' }} />
                    <div className="d-flex" style={{ justifyContent: 'space-between' }}>
                        <div className="d-flex" style={{ gap: '10px' }}>
                            <div style={{ width: '300px' }}>
                                <SelectMultiple
                                    style={{ width: '300px' }}
                                    selector="label"
                                    options={tempData.map(d => ({ value: d.id, label: d.name }))}
                                    changeEvent={(e) => {
                                        setQuery(prev => ({ ...prev, bank: e }));
                                    }}
                                    value={query.bank}
                                />
                            </div>
                            <div style={{ width: '200px' }}>
                                <InputSelect
                                    style={{ width: '200px' }}
                                    data={[{ id: '', name: 'Semua tipe' }, { id: 'va', name: 'Virtual Account' }, { id: 'payment_link', name: 'Payment Link' }]}
                                    changeEvent={(e) => {
                                        setQuery(prev => ({ ...prev, type: e }));
                                    }}
                                />
                            </div>
                            <div style={{ width: '200px' }}>
                                <InputSelect
                                    style={{ width: '200px' }}
                                    data={FilterStatus}
                                    changeEvent={(e) => {
                                        setQuery(prev => ({ ...prev, is_active: e }));
                                    }}
                                />
                            </div>
                        </div>
                        <div className="d-flex">
                            <InputText
                                placeholder="Search keyword"
                                changeEvent={val => setQuery(prev => ({ ...prev, search: val }))}
                                wait={500}
                            />
                        </div>
                    </div>
                </div>
                <div className="panel-body">
                    <Table
                        data={data}
                        totalData={data.length}
                        columns={TableColumn(data, showPopup)}
                        subRows="sub_payment_method"
                        searchQuery={query.search}
                        isLoading={isFetch}
                        isExpandableTree
                        hideDataInfo
                    />
                    <Pagination
                        currentPage={tableMeta.page}
                        limit={tableMeta.pageSize}
                        totalData={tableMeta.total}
                        onLimitChange={val => onFetch({ ...tableMeta, pageSize: val })}
                        onPageChange={page => onFetch({ ...tableMeta, page })}
                    />
                </div>
            </section>

            <SidePopup
                ref={(c) => { popupRef.current = c; }}
                width={560}
                type="edit"
                saveHandle={async () => {
                    const payload = {};

                    if (additionalData.isHaveChild) {
                        const final = {
                            ...additionalData.parentData,
                            sub_payment_method: additionalData.parentData.sub_payment_method.map((sub) => {
                                if (sub.name === detail.prev_name) return omit(detail, 'prev_name');

                                return sub;
                            }),
                        };


                        Object.assign(payload, final);
                    } else {
                        Object.assign(payload, detail);
                    }

                    await onUpdateStatus(omit(payload, 'id'), payload.id).finally(() => hidePopup());
                }}
                render={() => (
                    <EditForm data={detail} setValue={setDetail} additionalData={additionalData} />
                )}
            />
        </div>
    );
};

const mapStateToProps = state => ({
    userName: state.user.profile.name,
});

export default connect(mapStateToProps)(ManagePaymentMethod);
