import React, { Component } from 'react';
import update from 'immutability-helper';
import PropTypes from 'prop-types';
import { connect } from 'react-redux';
import moment from 'moment';
import {
    getFilterValue,
    getUpdatedFilterValue,
} from '../../../components/table/v.2/Table';
import InputSelect from '../../../components/form/InputSelect';

import SidePopup from '../../../components/sidepopup/ContainerV2';
import ModalPopup from '../../../components/modalpopup/v.2/Container';

import UserDetail from './sidebar/UserDetail';

import CoreHOC from '../../../core/CoreHOC';
import { catchError } from '../../../utils/helper';

import {
    getUserList,
    getUserDetail,
    updateUser,
    createUser,
    getBranchCompany,
    getJobTitle,
    getChannel,
    getCoverageProvince,
    getCoverageCity,
    getCoverageDistrict,
    getRole,
    getChangeLog,
} from '../../../data/setting/kloposUser';

import {
    TABLE_META,
    FILTERTYPE,
    STATUS_LIST,
    GENERATE_ERROR_MESSAGE,
    STATUS_ENUM,
} from './config/UserList';
import { FORM_TYPES } from '../../../enum/form';

import './style.less';
import InputText from '../../../components/form/InputText';
import Table from '../../../components/retina/table/Table';

class UserList extends Component {
    constructor(props) {
        super(props);

        this.state = {
            type: FORM_TYPES.CREATE,
            detail: {
                id: '',
                name: '',
                nik: '',
                email: '',
                notlp: '',
                status: true,
                idRole: '',
                password: '',
                password2: '',
                spv: '',
                branch: [],
                idJabatan: '',
                referralCode: '',
                channelId: '',
                provinceId: [],
                cityId: [],
                districtId: [],
            },
            userLogs: {
                accountCreatedAt: '',
                accountCreatedBy: '',
                logs: [
                    {
                        changed: '',
                        changedDate: '',
                        changedBy: '',
                        oldData: '',
                        newData: '',
                    },
                ],
            },
            tableFilter: [
                { id: FILTERTYPE.KEYWORD, value: '' },
                { id: FILTERTYPE.ROLE, value: '' },
                { id: FILTERTYPE.CHANNEL, value: '' },
                { id: FILTERTYPE.STATUS, value: STATUS_ENUM.ACTIVE },
            ],
            roleList: [],
            branchList: [],
            spvList: [],
            jabatanList: [],
            channelList: [],
            provinceList: [],
            cityList: [],
            districtList: [],
            tableData: [],
            tableMeta: {
                pageIndex: 1,
                limit: 10,
                totalData: 0,
                hasMoreItems: true,
            },
            tableLoading: false,
        };
    }

    componentWillMount = async () => {
        const {
            assignFilterColoumn,
            assignRangeDate,
            assignCalendar,
            assignButtons,
        } = this.props;
        assignFilterColoumn([]);
        assignRangeDate([]);
        assignCalendar(null, null, null);
        assignButtons([
            {
                type: 'primary',
                content: (
                    <span>
                        <i className="fa fa-plus" />
                        Add User
                    </span>
                ),
                action: () => {
                    this.callCreateHandler();
                },
            },
        ]);

        await this.loadRole();
    };

    componentDidMount = async () => {
        await this._onFetchs({ pageIndex: 0 });
    };

    componentDidUpdate(prevProps, prevState) {
        const { tableFilter } = this.state;
        if (prevState.tableFilter !== tableFilter) {
            this._onFetchs({ pageIndex: 0 });
        }
    }

    loadRole = async () => {
        const { notificationSystem } = this.props;
        try {
            const res = await getRole();
            const { data } = res;
            const roleList = data.map(x => [String(x.id), x.name]);

            this.setState({
                roleList,
            });
        } catch (err) {
            notificationSystem.addNotification({
                title: 'Gagal mendapatkan data Role',
                message: catchError(err),
                level: 'error',
            });
        } finally {
            await this.loadBranchCompany();
        }
    };

    loadBranchCompany = async () => {
        const { notificationSystem } = this.props;
        try {
            const res = await getBranchCompany();
            const { data } = res;
            const branchList = data.map(x => ({
                label: x.name,
                value: String(x.id),
            }));

            this.setState({
                branchList,
            });
        } catch (err) {
            notificationSystem.addNotification({
                title: 'Gagal mendapatkan data Employee Branch',
                message: catchError(err),
                level: 'error',
            });
        } finally {
            await this.loadJobTitle();
        }
    };

    loadJobTitle = async () => {
        const { notificationSystem } = this.props;
        try {
            const res = await getJobTitle();
            const { data } = res;
            const jabatanList = data.map((x) => {
                const { id, name, alias } = x;
                return { ...x, id: String(id), name: `${name} (${alias})` };
            });

            this.setState({
                jabatanList,
            });
        } catch (err) {
            notificationSystem.addNotification({
                title: 'Gagal mendapatkan data Job Title',
                message: catchError(err),
                level: 'error',
            });
        } finally {
            await this.loadChannel();
        }
    };

    loadChannel = async () => {
        const { notificationSystem } = this.props;
        try {
            const res = await getChannel();
            const { data } = res;
            const channelList = data.map(x => [String(x.id), x.name]);

            this.setState({
                channelList,
            });
        } catch (err) {
            notificationSystem.addNotification({
                title: 'Gagal mendapatkan data Channel',
                message: catchError(err),
                level: 'error',
            });
        } finally {
            await this.loadCoverageProvince();
        }
    };

    loadCoverageProvince = async () => {
        const { notificationSystem } = this.props;
        try {
            const res = await getCoverageProvince({ limit: 999 });
            const { data } = res;
            const provinceList = data.map(x => ({
                label: x.name,
                value: String(x.id),
            }));

            this.setState({
                provinceList,
            });
        } catch (err) {
            notificationSystem.addNotification({
                title: 'Gagal mendapatkan data Coverage Province',
                message: catchError(err),
                level: 'error',
            });
        }
    };

    _onFetch = async (state) => {
        const { notificationSystem } = this.props;
        const {
            page, pages, sorted, filtered, pageSize,
        } = state;
        const { length } = sorted;

        const filterKeyword = getFilterValue(filtered, FILTERTYPE.KEYWORD);
        const filterStatus = getFilterValue(filtered, FILTERTYPE.STATUS);
        const filterRole = getFilterValue(filtered, FILTERTYPE.ROLE);
        const filterChannel = getFilterValue(filtered, FILTERTYPE.CHANNEL);

        let payload = {
            limit: pageSize,
            page: Number(page) + 1,
            ...(filterKeyword && {
                keyword: filterKeyword,
            }),
            ...(filterStatus && {
                filter_status: filterStatus,
            }),
            ...(filterRole && {
                filter_role: filterRole,
            }),
            ...(filterChannel && {
                filter_channel: filterChannel,
            }),
        };

        // get sort params
        if (length > 0) {
            const {
                [length - 1]: { id, desc },
            } = sorted;
            payload = {
                ...payload,
                ...{ sorting_column: id, sorting: desc ? 'DESC' : 'ASC' },
            };
        }

        let err = null,
            retval = {
                data: [],
                pageCount: pages > -1 ? pages : -1,
            };

        try {
            const res = await getUserList(payload);
            const {
                data,
                meta: {
                    current_page: currentPage,
                    last_page: lastPage,
                    per_page: perPage,
                    last_page: pageCount,
                    total,
                },
            } = res;

            if (data) {
                this.setState({
                    tableData: data,
                    tableMeta: {
                        pageIndex: currentPage,
                        limit: perPage,
                        totalData: total,
                        hasMoreItems: currentPage < lastPage,
                    },
                });
            } else {
                this.setState({
                    tableData: [],
                    tableMeta: {
                        pageIndex: 1,
                        limit: 10,
                        totalData: 0,
                        hasMoreItems: false,
                    },
                });
            }

            retval = { data, pageCount };
        } catch (e) {
            err = e;
            notificationSystem.addNotification({
                title: 'Gagal mendapatkan data',
                message: catchError(err),
                level: 'error',
            });
            this.setState({
                tableData: [],
                tableMeta: {
                    pageIndex: 1,
                    limit: 10,
                    totalData: 0,
                    hasMoreItems: false,
                },
            });
        }
        retval = { ...retval, ...{ err } };

        return retval;
    };

    _onFetchs = async (params) => {
        const { notificationSystem } = this.props;
        const { tableFilter } = this.state;
        const {
            pageIndex, pageSize, sortAccessor, sortDirection,
        } = params;

        this.setState({
            tableLoading: true,
        });
        const filterKeyword = getFilterValue(tableFilter, FILTERTYPE.KEYWORD);
        const filterStatus = getFilterValue(tableFilter, FILTERTYPE.STATUS);
        const filterRole = getFilterValue(tableFilter, FILTERTYPE.ROLE);
        const filterChannel = getFilterValue(tableFilter, FILTERTYPE.CHANNEL);

        let payload = {
            limit: pageSize || 10,
            page: Number(pageIndex) + 1,
            ...(filterKeyword && {
                keyword: filterKeyword,
            }),
            ...(filterStatus && {
                filter_status: filterStatus,
            }),
            ...(filterRole && {
                filter_role: filterRole,
            }),
            ...(filterChannel && {
                filter_channel: filterChannel,
            }),
        };

        // get sort params
        if (sortAccessor && sortDirection) {
            payload = {
                ...payload,
                ...{ sorting_column: sortAccessor, sorting: sortDirection },
            };
        }

        let err = null,
            retval = {
                data: [],
                pageCount: pageIndex > -1 ? pageIndex : -1,
            };

        try {
            const res = await getUserList(payload);
            const {
                data,
                meta: {
                    current_page: currentPage,
                    last_page: lastPage,
                    per_page: perPage,
                    last_page: pageCount,
                    total,
                },
            } = res;

            if (data) {
                this.setState({
                    tableData: data,
                    tableMeta: {
                        pageIndex: currentPage,
                        limit: perPage,
                        totalData: total,
                        hasMoreItems: currentPage < lastPage,
                        sortAccessor,
                        sortDirection,
                    },
                });
            } else {
                this.setState({
                    tableData: [],
                    tableMeta: {
                        pageIndex: 1,
                        limit: 10,
                        totalData: 0,
                        hasMoreItems: false,
                    },
                });
            }

            retval = { data, pageCount };
        } catch (e) {
            err = e;
            notificationSystem.addNotification({
                title: 'Gagal mendapatkan data',
                message: catchError(err),
                level: 'error',
            });
            this.setState({
                tableData: [],
                tableMeta: {
                    pageIndex: 1,
                    limit: 10,
                    totalData: 0,
                    hasMoreItems: false,
                },
            });
        } finally {
            this.setState({
                tableLoading: false,
            });
        }
        retval = { ...retval, ...{ err } };

        return retval;
    };

    callEditDetailHandler = async ({ original: value }) => {
        const { notificationSystem, showProgress, hideProgress } = this.props;

        try {
            showProgress();
            const res = await getUserDetail(value.id);
            const {
                data: {
                    branches,
                    coverage_provinces: coverageProvinces,
                    coverage_cities: coverageCities,
                    coverage_districts: coverageDistricts,
                    job_title_id: jobTitleId,
                    spv_id: spvId,
                    id,
                    name,
                    nik,
                    email,
                    phone: notlp,
                    status,
                    role_id: roleId,
                    referal_code: referralCode,
                    channel_id: channelId,
                },
            } = res;

            const filteredBranch = branches
                .filter(x => x !== 0)
                .map(x => String(x));
            const filteredProvince = coverageProvinces
                .filter(x => x !== 0)
                .map(x => String(x));
            const filteredCity = coverageCities
                .filter(x => x !== 0)
                .map(x => String(x));
            const filteredDistrict = coverageDistricts
                .filter(x => x !== 0)
                .map(x => String(x));

            const spvList = await this.cariSpv(String(jobTitleId));
            let spv = spvId > 0 ? String(spvId) : '';
            if (spvList.length === 0) spv = '0';

            if (filteredProvince.length > 0) await this.reloadCity(filteredProvince);
            if (filteredCity.length > 0) await this.reloadDistrict(filteredCity);

            this.setState(
                {
                    detail: {
                        id,
                        name,
                        nik,
                        email,
                        notlp,
                        status: status === STATUS_ENUM.ACTIVE,
                        idRole: String(roleId),
                        password: null,
                        password2: null,
                        spv,
                        branch: filteredBranch,
                        idJabatan: String(jobTitleId),
                        referralCode,
                        channelId: String(channelId),
                        provinceId: filteredProvince,
                        cityId: filteredCity,
                        districtId: filteredDistrict,
                    },
                    type: FORM_TYPES.EDIT,
                    spvList,
                },
                () => {
                    this.sidePop.showPopup();
                },
            );
        } catch (error) {
            notificationSystem.addNotification({
                title: 'Failed get data detail',
                message: catchError(error),
                level: 'error',
            });
        } finally {
            hideProgress();
        }
    };

    cariSpv = async (idJob) => {
        const { notificationSystem } = this.props;
        const { jabatanList } = this.state;

        let spvList = [];

        const dataJob = jabatanList.find(x => x.id === idJob);

        if (dataJob) {
            try {
                const payload = {
                    limit: 999,
                    job_title_depth: dataJob.depth,
                    job_title_depth_operand: 'upper',
                };
                const res = await getUserList(payload);
                const { data } = res;

                spvList = data.map((x) => {
                    const {
                        id, name, email, job_title: jobTitle,
                    } = x;

                    return {
                        ...x,
                        id: String(id),
                        name: `${name} (${email} - ${jobTitle})`,
                    };
                });
            } catch (error) {
                notificationSystem.addNotification({
                    title: 'Gagal mendapatkan data SPV',
                    message: catchError(error),
                    level: 'error',
                });
            }
        }

        return spvList;
    };

    callCreateHandler = () => {
        this.setState(
            {
                detail: {
                    id: '',
                    name: '',
                    nik: '',
                    email: '',
                    notlp: '',
                    status: true,
                    idRole: '',
                    password: '',
                    password2: '',
                    spv: '',
                    branch: [],
                    idJabatan: '',
                    referralCode: '',
                    channelId: '',
                    provinceId: [],
                    cityId: [],
                    districtId: [],
                },
                type: FORM_TYPES.CREATE,
                spvList: [],
                cityList: [],
                districtList: [],
            },
            () => {
                this.sidePop.showPopup();
            },
        );
    };

    saveHandler = async (saveType, errorCallback) => {
        const {
            notificationSystem,
            profile: { id: updatedBy },
        } = this.props;

        const {
            detail: {
                id,
                name,
                nik,
                spv,
                idJabatan,
                idRole,
                email,
                status,
                channelId,
                referralCode,
                branch: branches,
                notlp,
                districtId,
                cityId,
                provinceId,
                password,
                password2,
            },
            type,
        } = this.state;

        try {
            const payload = {
                name,
                nik,
                spv_id: spv,
                job_title_id: idJabatan,
                role_id: idRole,
                email,
                status: status ? STATUS_ENUM.ACTIVE : STATUS_ENUM.DEACTIVE,
                channel_id: channelId,
                referral_code: referralCode,
                branches,
                phone: notlp,
                coverage_provinces: provinceId,
                coverage_cites: cityId,
                coverage_districts: districtId,
                update_by: updatedBy,
            };

            if (password !== password2) throw new Error('Password is not equal');

            if (type === FORM_TYPES.CREATE) {
                await this.createUserHandle(payload, password, password2);
            } else {
                await this.editUserHandle(payload, id);
            }

            this.refetchTable();
        } catch (err) {
            notificationSystem.addNotification({
                title: 'Failed create user',
                message: catchError(err),
                level: 'error',
            });
            errorCallback();
        }
    };

    refetchTable = () => {
        this._onFetchs({ pageIndex: 0 });
    };

    createUserHandle = async (payload, password, password2) => {
        const { showProgress, hideProgress, notificationSystem } = this.props;

        const newPayload = {
            ...payload,
            password,
            confirm_password: password2,
        };

        try {
            showProgress();
            await createUser(newPayload);

            this.sidePop.hidePopup();
            notificationSystem.addNotification({
                title: 'Success',
                message: 'Success create user',
                level: 'success',
            });
        } catch (error) {
            notificationSystem.addNotification({
                title: 'Failed create user',
                message: catchError(error),
                level: 'error',
            });
        } finally {
            hideProgress();
        }
    };

    editUserHandle = async (payload, id) => {
        const { showProgress, hideProgress, notificationSystem } = this.props;

        const newPayload = {
            ...payload,
            id,
        };

        try {
            showProgress();
            await updateUser(newPayload);
            this.sidePop.hidePopup();
            notificationSystem.addNotification({
                title: 'Success',
                message: 'Success update user',
                level: 'success',
            });
        } catch (error) {
            notificationSystem.addNotification({
                title: 'Failed update user',
                message: catchError(error),
                level: 'error',
            });
        } finally {
            hideProgress();
        }
    };

    _onInputChangeValidate = ({ target }) => {
        this.form.validateInput(target);

        if (this.inputTimeout) {
            clearTimeout(this.inputTimeout);
        }
    };

    changeEvent = (key, value) => new Promise((resolve) => {
        const { detail } = this.state;
        this.setState(
            {
                detail: {
                    ...detail,
                    [key]: value,
                },
            },
            () => {
                resolve();
            },
        );
    });

    changeEventBranch = async (val) => {
        const { detail } = this.state;

        const newDetail = update(detail, {
            branch: { $set: val.map(x => x.value.toString()) },
        });

        this.setState({ detail: newDetail });
    };

    changeEventJabatan = async (val) => {
        const { detail } = this.state;
        const spvList = await this.cariSpv(val);

        const newValue = update(detail, {
            spv: { $set: spvList.length === 0 ? '0' : '' },
        });

        this.setState({
            detail: newValue,
            spvList,
        });
    };

    changeProvince = async (val) => {
        const { detail } = this.state;
        const provinceId = val.map(x => x.value);

        this.setState(
            {
                detail: update(detail, {
                    provinceId: { $set: provinceId },
                    cityId: { $set: [] },
                    districtId: { $set: [] },
                }),
                districtList: [],
            },
            () => {
                this.reloadCity(provinceId);
            },
        );
    };

    changeCity = async (val) => {
        const { detail } = this.state;
        const cityId = val.map(x => x.value);
        this.setState(
            {
                detail: update(detail, {
                    cityId: { $set: cityId },
                    districtId: { $set: [] },
                }),
            },
            () => {
                this.reloadDistrict(cityId);
            },
        );
    };

    reloadCity = async (idProvince) => {
        const { notificationSystem } = this.props;
        const payload = {
            limit: 999,
            province_list: idProvince.join(','),
        };

        try {
            const res = await getCoverageCity(payload);
            const { data } = res;
            const cityList = data.map(x => ({
                label: x.name,
                value: String(x.id),
            }));

            this.setState({
                cityList,
            });
        } catch (error) {
            notificationSystem.addNotification({
                title: 'Failed get Cities',
                message: catchError(error),
                level: 'error',
            });
        }
    };

    reloadDistrict = async (idCity) => {
        const { notificationSystem } = this.props;
        const payload = {
            limit: 999,
            city_list: idCity.join(','),
        };

        try {
            const res = await getCoverageDistrict(payload);
            const { data } = res;
            const districtList = data.map(x => ({
                label: x.name,
                value: String(x.id),
            }));

            this.setState({
                districtList,
            });
        } catch (error) {
            notificationSystem.addNotification({
                title: 'Failed get District',
                message: catchError(error),
                level: 'error',
            });
        }
    };

    changeTableFilter = (val, type) => {
        const { tableFilter } = this.state;
        const updatedFilterValue = getUpdatedFilterValue(
            tableFilter,
            type,
            val,
        );

        this.setState({ tableFilter: updatedFilterValue, [type]: val });
    };

    openChangeLog = async (id) => {
        const { showProgress, hideProgress, notificationSystem } = this.props;

        showProgress();
        try {
            const {
                data: {
                    log_edit: logEdit,
                    account_created_at: accountCreatedAt,
                    account_created_by: accountCreatedBy,
                },
            } = await getChangeLog({ id_pengguna: id });

            const logs = logEdit.map(
                ({
                    changed,
                    changed_date: changedDate,
                    changed_by: changedBy,
                    old_data: oldData,
                    new_data: newData,
                }) => ({
                    changed,
                    changedDate,
                    changedBy,
                    oldData,
                    newData,
                }),
            );

            this.setState(
                {
                    userLogs: {
                        accountCreatedAt,
                        accountCreatedBy,
                        logs,
                    },
                },
                () => {
                    this.modalChangeLog.showPopup();
                },
            );
        } catch (error) {
            notificationSystem.addNotification({
                title: 'Failed get User Logs',
                message: catchError(error),
                level: 'error',
            });
        } finally {
            hideProgress();
        }
    };

    render() {
        const {
            detail,
            type,
            userLogs: { logs, accountCreatedAt, accountCreatedBy },
            roleList,
            branchList,
            channelList,
            spvList,
            jabatanList,
            tableLoading,
            provinceList,
            cityList,
            districtList,
            tableFilter,
            tableMeta,
            tableData,
        } = this.state;
        const { id } = detail;
        const {
            pageIndex, limit, totalData, hasMoreItems,
        } = tableMeta;

        const filterKeyword = getFilterValue(tableFilter, FILTERTYPE.KEYWORD);

        return (
            <div className="section">
                <section className="panel">
                    <div className="panel-heading table-header">
                        <div className="row">
                            <div className="col-md-8">
                                <h4
                                    className="panel-title"
                                    style={{ margin: '8px' }}
                                >
                                    User List
                                </h4>
                            </div>
                        </div>
                    </div>
                    <div className="panel-heading table-header">
                        <div className="row">
                            <div className="col-md-3">
                                <InputSelect
                                    data={STATUS_LIST}
                                    placeholder="All Status"
                                    changeEvent={value => this.changeTableFilter(
                                        value,
                                        FILTERTYPE.STATUS,
                                    )}
                                />
                            </div>
                            <div className="col-md-2">
                                <InputSelect
                                    data={channelList}
                                    placeholder="All Channel"
                                    changeEvent={value => this.changeTableFilter(
                                        value,
                                        FILTERTYPE.CHANNEL,
                                    )}
                                />
                            </div>
                            <div className="col-md-3">
                                <InputSelect
                                    data={roleList}
                                    placeholder="All Role"
                                    changeEvent={value => this.changeTableFilter(
                                        value,
                                        FILTERTYPE.ROLE,
                                    )}
                                />
                            </div>
                            <div className="col-md-3">
                                <InputText
                                    classes="filter"
                                    placeholder="Cari ..."
                                    changeEvent={value => this.changeTableFilter(
                                        value,
                                        FILTERTYPE.KEYWORD,
                                    )}
                                    wait={500}
                                />
                            </div>
                        </div>
                    </div>
                    <div className="panel-body">
                        <Table
                            columns={TABLE_META}
                            data={tableData}
                            pageIndex={pageIndex - 1}
                            rowLimit={limit}
                            totalData={totalData}
                            hasMoreItems={hasMoreItems}
                            fetchData={this._onFetchs}
                            isLoading={tableLoading}
                            searchQuery={filterKeyword}
                            onRowClick={this.callEditDetailHandler}
                        />
                    </div>
                </section>

                <SidePopup
                    ref={(c) => {
                        this.sidePop = c;
                    }}
                    width={600}
                    type={type}
                    saveHandle={this.saveHandler}
                    customRightButtons={
                        type !== FORM_TYPES.EDIT
                            ? false
                            : [
                                <button
                                    key="1"
                                    type="button"
                                    className="btn"
                                    onClick={() => {
                                        this.openChangeLog(id);
                                    }}
                                >
                                    Log
                                </button>,
                                <button
                                    key="2"
                                    type="button"
                                    className="btn"
                                    onClick={() => {
                                        this.sidePop.hidePopup();
                                    }}
                                >
                                    Cancel
                                </button>,
                            ]
                    }
                    render={({ validateInput }) => (
                        <UserDetail
                            type={type}
                            data={detail}
                            roleList={roleList}
                            jabatanList={jabatanList}
                            branchList={branchList}
                            channelList={channelList}
                            spvList={spvList}
                            provinceList={provinceList}
                            cityList={cityList}
                            districtList={districtList}
                            changeEvent={this.changeEvent}
                            changeBranch={this.changeEventBranch}
                            changeEventJabatan={this.changeEventJabatan}
                            changeProvince={this.changeProvince}
                            changeCity={this.changeCity}
                            validateInput={validateInput}
                            errorMessage={GENERATE_ERROR_MESSAGE}
                        />
                    )}
                />

                <ModalPopup
                    title="Account Log History"
                    ref={(c) => {
                        this.modalChangeLog = c;
                    }}
                    confirmHandle={false}
                    cancelText="Close"
                >
                    <div className="row mb-sm">
                        <ul className="timeline">
                            {logs.map(
                                ({
                                    changed,
                                    changedDate,
                                    changedBy,
                                    oldData,
                                    newData,
                                }) => (
                                    <li className="timeline-item">
                                        <div>
                                            Change
                                            {' '}
                                            {changed}
                                            {' '}
                                            at
                                            {''}
                                            {moment(
                                                changedDate,
                                                'YYYY-MM-DD HH:mm',
                                            ).format('DD/MM/YYYY HH:mm')}
                                            {' '}
                                            | Change_by
                                            {' '}
                                            {changedBy}
                                            {' '}
                                            | Old:
                                            <red>{oldData}</red>
                                            {' '}
                                            | New:
                                            {' '}
                                            <blue>{newData}</blue>
                                        </div>
                                    </li>
                                ),
                            )}
                            <li className="timeline-item">
                                <div>
                                    Created account_at
                                    {' '}
                                    {moment(
                                        accountCreatedAt,
                                        'YYYY-MM-DD HH:mm',
                                    ).format('DD/MM/YYYY HH:mm')}
                                    {' '}
                                    | Created by
                                    {' '}
                                    {accountCreatedBy}
                                </div>
                            </li>
                        </ul>
                    </div>
                </ModalPopup>
            </div>
        );
    }
}

UserList.propTypes = {
    assignCalendar: PropTypes.func,
    assignButtons: PropTypes.func,
    notificationSystem: PropTypes.shape({
        addNotification: PropTypes.func,
    }),
    assignFilterColoumn: PropTypes.func,
    assignRangeDate: PropTypes.func,
    profile: PropTypes.shape({
        id: PropTypes.oneOfType([PropTypes.number, PropTypes.string]),
    }),
    showProgress: PropTypes.func,
    hideProgress: PropTypes.func,
};

UserList.defaultProps = {
    assignCalendar: () => { },
    assignButtons: () => { },
    notificationSystem: null,
    assignFilterColoumn: () => { },
    assignRangeDate: () => { },
    profile: { id: undefined },
    showProgress: () => { },
    hideProgress: () => { },
};

const mapStateToProps = state => ({
    profile: state.user.profile,
});

export default CoreHOC(connect(mapStateToProps)(UserList));
