import React, { Component } from 'react';
import { Pagination } from '@majoo-ui/react';
import PropTypes from 'prop-types';

import { menuListRetina } from '../../../../../data/setting/privilage';

import Table from '../../../../../components/retina/table/Table';
import InputText from '../../../../../components/form/InputText';
import Select from '../../../../../components/form/Select';

import { buildColumns, FILTER_OPTIONS } from '../config/config';

class TreeMenuCms extends Component {
    constructor(props) {
        super(props);

        this.timeoutCounter = null;
        this.state = {
            rawMenus: [],
            rawTreeMenus: [],
            filteredTreeMenus: [],
            filterKeyword: '',
            filterByDisplay: 'All Data',
            page: 1,
            pageSize: 10,
        };

        this.columns = buildColumns(props.openFormHandler);
    }

    componentWillMount() {
        this.buildMenu();
    }

    getterRawMenus = () => {
        const { rawMenus } = this.state;
        return rawMenus;
    };

    refreshData = () => {
        this.buildMenu(false);
    };

    buildMenu = async (init = true) => {
        const { showProgress, hideProgress } = this.props;
        const { filterKeyword, filterByDisplay } = this.state;

        showProgress();
        const data = await this.fetchDashboardMenu();
        const rawTreeMenus = this.buildTreeMenu(data, [], 1);

        /* handle refresh when filtered */
        let filtered = rawTreeMenus;
        if (!init && (filterKeyword || filterByDisplay !== 'All Data')) {
            filtered = this.buildFilteredData(
                filterKeyword,
                filterByDisplay,
                data,
            );
        }

        this.setState(
            {
                rawMenus: data,
                rawTreeMenus,
                filteredTreeMenus: filtered,
            },
            () => {
                hideProgress();
            },
        );
    };

    buildTreeMenu = (raw, arrSubject, level) => {
        let arrSubjectForTree = arrSubject;

        // for very first level
        if (level === 1) {
            arrSubjectForTree = raw.filter(menu => menu.parent_id === null);
        }

        const newTree = arrSubjectForTree.map((currentMenu) => {
            let childrens = raw.filter(
                otherMenu => String(currentMenu.id) === String(otherMenu.parent_id),
            );

            const excludedFromRaw = [currentMenu]
                .concat(childrens)
                .map(curr => ({ ...curr, id: String(curr.id) }));
            const updatedRaw = raw.filter(
                menu => !excludedFromRaw.includes(String(menu.id)),
            );

            if (childrens.length > 0) {
                childrens = this.buildTreeMenu(
                    updatedRaw,
                    childrens,
                    level + 1,
                );
            }

            const tobeAttachedChildren = {};

            if (childrens.length > 0) Object.assign(tobeAttachedChildren, { childrens });

            return Object.assign({}, currentMenu, {
                level,
                ...tobeAttachedChildren,
            });
        });

        return newTree;
    };

    fetchDashboardMenu = async () => {
        try {
            const response = await menuListRetina();
            if (!response) return null;
            if (response.status) {
                return response.data;
            }

            throw new Error(response.msg);
        } catch (e) {
            return null;
        }
    };

    buildFilteredData = (filterKeyword, filterByDisplay, rawData) => {
        const { rawMenus, rawTreeMenus } = this.state;
        let filteredTreeMenus = rawTreeMenus;

        if (filterKeyword !== '' || filterByDisplay !== 'All Data') {
            const findWord = (val) => {
                if (filterKeyword !== '') {
                    return val
                        .toLowerCase()
                        .includes(filterKeyword.toLowerCase());
                }

                return true;
            };

            const findByDisplay = (val) => {
                if (filterByDisplay !== 'All Data') {
                    if (filterByDisplay === 'Display Menu') {
                        return String(val) === '1';
                    }

                    // tidak tampil di menu
                    return String(val) === '0';
                }

                return true;
            };

            filteredTreeMenus = (rawData || rawMenus).filter(
                menu => findWord(menu.name) && findByDisplay(menu.menu_is_display),
            );
        }

        return filteredTreeMenus;
    };

    filterDisplayChangeHandler = (value) => {
        const { filterKeyword } = this.state;

        this.setState({ filterByDisplay: value }, () => {
            const filteredTreeMenus = this.buildFilteredData(
                filterKeyword,
                value,
            );
            this.setState({ filteredTreeMenus });
        });
    };

    filterChangeHandler = (value) => {
        const { filterByDisplay } = this.state;
        this.setState({ filterKeyword: value }, () => {
            const filteredTreeMenus = this.buildFilteredData(
                value,
                filterByDisplay,
            );
            this.setState({ filteredTreeMenus });
        });
    };

    render() {
        const {
            filteredTreeMenus,
            filterKeyword,
            filterByDisplay,
            page,
            pageSize,
        } = this.state;

        const paginatedData = filteredTreeMenus.slice(
            (page - 1) * pageSize,
            page * pageSize,
        );

        return (
            <section className="panel">
                <div className="panel-heading table-header">
                    <div className="row">
                        <div className="col-md-12">
                            <div style={{ width: '100%' }}>
                                <div className="row">
                                    <div
                                        className="col-md-2"
                                        style={{
                                            paddingRight: '0px',
                                            float: 'right',
                                        }}
                                    >
                                        <InputText
                                            classes="filter"
                                            placeholder="Cari ..."
                                            changeEvent={
                                                this.filterChangeHandler
                                            }
                                            value={filterKeyword}
                                            wait={500}
                                        />
                                    </div>
                                    <div
                                        className="col-md-3"
                                        style={{
                                            paddingRight: '0px',
                                            float: 'right',
                                        }}
                                    >
                                        <Select
                                            data={FILTER_OPTIONS}
                                            value={filterByDisplay}
                                            changeEvent={
                                                this.filterDisplayChangeHandler
                                            }
                                        />
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div className="panel-body">
                    <Table
                        columns={this.columns}
                        data={paginatedData}
                        subRows="childrens"
                        isExpandableTree
                        hideDataInfo
                        totalData={filteredTreeMenus.length}
                        searchQuery={filterKeyword}
                    />
                    <Pagination
                        currentPage={page}
                        limit={pageSize}
                        totalData={filteredTreeMenus.length}
                        onLimitChange={val => this.setState({ pageSize: val, page: 1 })
                        }
                        onPageChange={val => this.setState({ page: val })}
                    />
                </div>
            </section>
        );
    }
}

TreeMenuCms.propTypes = {
    openFormHandler: PropTypes.func,
    showProgress: PropTypes.func.isRequired,
    hideProgress: PropTypes.func.isRequired,
};

TreeMenuCms.defaultProps = {
    openFormHandler: undefined,
};

export default TreeMenuCms;
