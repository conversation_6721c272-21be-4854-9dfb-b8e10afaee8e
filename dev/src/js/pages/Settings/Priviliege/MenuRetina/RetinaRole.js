import React, { Component } from 'react';
import PropTypes from 'prop-types';
import { FieldFeedbacks, FieldFeedback } from 'react-form-with-constraints';
import CoreHOC from '../../../../core/CoreHOC';
import Table from '../../../../components/retina/table/Table';
import InputText from '../../../../components/form/InputText';
import SidePopup from '../../../../components/sidepopup/ContainerV2';
import { getAllRoleCms, addCmsRole } from '../../../../data/setting/privilage';
import { catchError } from '../../../../utils/helper';
import { TABLE_META } from './config/RetinaRole';

class RetinaRole extends Component {
  static propTypes = {
    assignCalendar: PropTypes.func,
    assignButtons: PropTypes.func,
    notificationSystem: PropTypes.shape({ addNotification: PropTypes.func }),
    router: PropTypes.shape({ push: PropTypes.func }),
    assignFilterColoumn: PropTypes.func,
    assignRangeDate: PropTypes.func,
  }

  static defaultProps = {
    assignCalendar: () => { },
    assignButtons: () => { },
    notificationSystem: null,
    router: null,
    assignFilterColoumn: () => { },
    assignRangeDate: () => { },
  }

  constructor(props) {
    super(props);

    this.state = {
      tableData: [],
      searchQuery: '',
      tableLoading: false,
      name: '',
    };
  }

  componentDidMount() {
    const {
      assignFilterColoumn, assignRangeDate, assignCalendar, assignButtons,
    } = this.props;
    assignFilterColoumn([]);
    assignRangeDate([]);
    assignCalendar(null, null, null);
    assignButtons([{ type: 'primary', content: <span> Add New Role </span>, action: this.addRole }]);
    this.fetchData();
  }

  fetchData = async () => {
    const { notificationSystem } = this.props;
    this.setState({ tableLoading: true });
    try {
      const response = await getAllRoleCms();
      this.setState({
        tableData: response.data,
      });
    } catch (err) {
      notificationSystem.addNotification({
        title: 'Gagal mendapatkan data',
        message: catchError(err),
        level: 'error',
      });
    } finally {
      this.setState({ tableLoading: false });
    }
  }

  callEditDetailHandler = (val) => {
    const { router } = this.props;
    router.push(`/privilage/retinarole/detail/${val.id}`);
  }

  addRole = () => {
    this.setState({ name: '' });
    this.sidePop.showPopup();
  }

  saveHandler = async () => {
    const { notificationSystem } = this.props;
    const { name } = this.state;
    const param = { name };
    try {
      await addCmsRole(param);
      this.fetchData();
      this.sidePop.hidePopup();
    } catch (err) {
      notificationSystem.addNotification({
        title: 'Add data failed',
        message: catchError(err),
        level: 'error',
      });
      if (this.sidePop) this.sidePop.failedCallback();
    }
  }

  handleChange = (val, type, e = null) => {
    this.setState({ [type]: val });
    if (e && this.sidePop) {
      this.sidePop.validateInput(e.target);
    }
  }

  handleSearchChange = (value) => {
    this.setState({ searchQuery: value });
  }

  render() {
    const {
      tableData, name, tableLoading, searchQuery,
    } = this.state;

    const filteredData = tableData.filter(item => item.name && item.name.toLowerCase().includes(searchQuery.toLowerCase()));

    return (
      <div>
        <section className="panel">
          <div className="panel-heading table-header">
            <div className="row">
              <div className="col-md-9">
                <h4 className="panel-title" style={{ paddingTop: '8px' }}>
                  Retina Role
                </h4>
              </div>
              <div className="col-md-3">
                <InputText
                  classes="filter"
                  placeholder="Cari..."
                  changeEvent={this.handleSearchChange}
                  wait={500}
                />
              </div>
            </div>
          </div>
          <div className="panel-body">
            <Table
              columns={TABLE_META}
              data={filteredData}
              isLoading={tableLoading}
              onRowClick={({ original }) => this.callEditDetailHandler(original)}
              totalData={filteredData.length}
              searchQuery={searchQuery}
            />
          </div>
        </section>
        <SidePopup
          ref={(c) => { this.sidePop = c; }}
          width={520}
          saveHandle={this.saveHandler}
          render={({ show }) => {
            if (show) {
              return (
                <div>
                  <h4 className="side-popup-title">
                    Add User Role
                  </h4>
                  <div className="row mb-sm">
                    <div className="col-sm-12">
                      <InputText
                        label="Name"
                        placeholder="Name"
                        value={name}
                        changeEvent={(val, e) => this.handleChange(val, 'name', e)}
                        name="name_form"
                      />
                      <FieldFeedbacks for="name_form">
                        <FieldFeedback when={val => val === ''}>Name tidak boleh kosong</FieldFeedback>
                      </FieldFeedbacks>
                    </div>
                  </div>
                </div>
              );
            }
            return null;
          }}
        />
      </div>
    );
  }
}

export default CoreHOC(RetinaRole);
