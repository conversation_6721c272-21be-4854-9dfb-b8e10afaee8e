import React, { Component } from 'react';
import PropTypes from 'prop-types';
import CoreHOC from '../../../../core/CoreHOC';

import SidePopup from '../../../../components/sidepopup/ContainerV2';
import TreeMenu from './component/TreeMenuRetina';
import MenuRetinaForm from './Form';

import { addMenuRetina, updateMenuRetina } from '../../../../data/setting/privilage';
import { getSupportMaster } from '../../../../data/sales/master';

import { FORM_TYPES } from '../../../../enum/form';
import { catchError } from '../../../../utils/helper';

class MenuRetina extends Component {
    constructor(props) {
        super(props);

        this.state = {
            supportOptions: [], // keep here instead of in form, so no need to fetch on every open form
        };
    }

    componentDidMount = () => {
        const {
            assignFilterColoumn, assignRangeDate, assignCalendar, assignButtons,
        } = this.props;
        assignFilterColoumn([]);
        assignRangeDate([]);
        assignCalendar(null, null, null);
        assignButtons([
            {
                type: 'primary',
                content: <span> Add New Menu </span>,
                action: () => {
                    this.addMenu();
                },
            },
        ]);
        this.buildSupportOptions();
    }

    buildSupportOptions = async () => {
        const data = await this.fetchSupportList();
        let supportOptions = [];

        if (data.length > 0) {
            supportOptions = data.map(item => [item.id, item.name]);
        }

        this.setState({
            supportOptions,
        });
    }

    fetchSupportList = async () => {
        try {
            const param = {
                is_premium: 1,
            };

            const response = await getSupportMaster(param);

            if (!response) throw new Error('Failed to get support list');

            if (response.status) {
                return response.data;
            }

            throw new Error(response.msg);
        } catch (e) {
            const { notificationSystem } = this.props;

            notificationSystem.addNotification({
                title: 'Gagal mendapatkan master support',
                message: e.message,
                level: 'error',
            });

            return [];
        }
    }

    callDetail = (formData) => {
        this.sidePop.showPopup(() => { this.innerForm.setterFormState({ formData, rawMenus: this.treeMenu.getterRawMenus() }); });
    }

    addMenu = () => {
        this.sidePop.showPopup(() => { this.innerForm.setterFormState({ rawMenus: this.treeMenu.getterRawMenus() }); });
    }

    commitSave = async (form) => {
        const { notificationSystem } = this.props;
        const {
            name, englishName, activeStatus, isDisplay, parentId, content, menuController, menuFunction, seq,
            icon, id, checkedSupport, activeStatusBySupport, type,
            isMenuMobile, isAndroidTransactional, settingType, methods: method,
        } = form;

        const params = {
            name,
            english_name: englishName,
            active_status: activeStatus,
            is_display: isDisplay,
            parent_id: parentId,
            content,
            menu_controller: menuController,
            menu_function: menuFunction,
            seq,
            icon,
            id,
            checkedSupport,
            active_status_by_support: activeStatusBySupport,
            is_menu_mobile: isMenuMobile,
            is_android_transactional: isAndroidTransactional === true ? 1 : 0,
            type: settingType,
            method,
        };

        try {
            let res;

            if (type === FORM_TYPES.CREATE) {
                res = await addMenuRetina(params);
            }

            if (type === FORM_TYPES.EDIT) {
                res = await updateMenuRetina(params);
            }

            if (res && 'status' in res && !res.status) throw new Error(res.msg);

            this.sidePop.hidePopup();
            notificationSystem.addNotification({
                title: 'Success!',
                message: res ? res.msg : 'Data berhasil disimpan',
                level: 'success',
            });
            return true;
        } catch (err) {
            notificationSystem.addNotification({
                title: 'Gagal menyimpan data',
                message: catchError(err),
                level: 'error',
            });
            return false;
        }
    }

    saveHandler = async () => {
        const param = await this.innerForm.getterFormState();

        const isSaved = await this.commitSave(param);

        if (isSaved) {
            await this.treeMenu.refreshData();
        }
    }

    render() {
        const { showProgress, hideProgress } = this.props;
        const { supportOptions } = this.state;

        return (
            <div>
                <section className="panel">
                    <div className="panel-heading">
                        <h4 className="panel-title">Menu List</h4>
                    </div>
                    <TreeMenu
                        ref={(c) => { this.treeMenu = c; }}
                        openFormHandler={this.callDetail}
                        showProgress={showProgress}
                        hideProgress={hideProgress}
                    />
                </section>

                <SidePopup
                    ref={(c) => { this.sidePop = c; }}
                    width={520}
                    saveHandle={() => this.saveHandler()}
                    render={({ show }) => {
                        if (show) {
                            return (
                                <MenuRetinaForm
                                    ref={(c) => { this.innerForm = c; }}
                                    supportOptions={supportOptions}
                                />
                            );
                        }

                        return null;
                    }}
                />
            </div>
        );
    }
}

MenuRetina.propTypes = {
    assignCalendar: PropTypes.func,
    assignButtons: PropTypes.func,
    notificationSystem: PropTypes.shape({
        addNotification: PropTypes.func,
    }),
    assignFilterColoumn: PropTypes.func,
    assignRangeDate: PropTypes.func,
    showProgress: PropTypes.func,
    hideProgress: PropTypes.func,
};

MenuRetina.defaultProps = {
    assignCalendar: () => {},
    assignButtons: () => {},
    notificationSystem: { addNotification: () => {} },
    assignFilterColoumn: () => {},
    assignRangeDate: () => {},
    showProgress: () => {},
    hideProgress: () => {},
};

export default CoreHOC(MenuRetina);
