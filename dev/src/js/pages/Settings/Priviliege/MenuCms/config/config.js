/* eslint-disable react/prop-types */
import React from 'react';
import IndentLevel from '../../components/CellIndentLevel';
import IsDisplay from '../../components/CellIsDisplay';
import ControllerFunction from '../../components/CellControllerFunction';
import Detail from '../../components/CellDetail';

const buildColumns = openFormEventHandler => ([
    {
        Header: () => null,
        id: 'expander',
        unsortable: true,
        Cell: (props) => {
            const {
                row: {
                    depth, getToggleRowExpandedProps, isExpanded, canExpand,
                },
            } = props;

            return (
                <div
                    style={{
                        display: 'flex',
                        alignItems: 'center',
                        gap: '0.5rem',
                        paddingLeft: `${depth * 1}rem`,
                        minWidth: 30,
                    }}
                >
                    {canExpand ? (
                        <div
                            {...getToggleRowExpandedProps()}
                        >
                            {isExpanded ? <i className="fa fa-chevron-up" /> : <i className="fa fa-chevron-down" />}
                        </div>
                    ) : null}
                </div>
            );
        },
    },
    {
        colWidth: 300,
        Header: 'Name',
        accessor: 'name',
        Cell: IndentLevel,
    },
    {
        Header: 'Parent',
        accessor: 'parent_name',
    },
    {
        Header: 'Seq',
        accessor: 'urutan',
    },
    {
        Header: 'Is Display',
        accessor: 'menu_is_display',
        Cell: IsDisplay,
    },
    {
        Header: 'Controller/Function',
        accessor: 'menu_crontroller',
        Cell: ControllerFunction,
    },
    {
        Header: 'Content',
        accessor: 'content',
    },
    {
        Header: 'Detail',
        Cell: props => <Detail {...props} clickHandler={openFormEventHandler} />,
    },
]);

const FILTER_OPTIONS = [
    'All Data',
    'Display Menu',
    'Not Display As Menu',
];

export { buildColumns, FILTER_OPTIONS };
