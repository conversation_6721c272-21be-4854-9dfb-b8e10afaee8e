import React from 'react';
import PropTypes from 'prop-types';
import Table from '../../../../components/retina/table/Table';
import { tableColumnHistory } from '../config/wallet';

const TableHistorySubmission = ({
 onFetch, list, tableMeta, isLoading,
}) => (
    <section className="panel">
        <div className="panel-heading table-header">
            <div className="row">
                <div className="col-md-12">
                    <h4
                        className="panel-title"
                        style={{ paddingTop: '8px' }}
                    >
                        Submission History
                    </h4>
                </div>
            </div>
        </div>
        <div className="panel-body">
            <Table
                columns={tableColumnHistory}
                data={list}
                totalData={tableMeta.total}
                rowLimit={tableMeta.pageSize}
                pageIndex={tableMeta.pageIndex}
                onRowClick={({ original: { submission_no: submissionNo } }) => window.open(`/non-cash-setting/wallet-payment/view/${submissionNo}`, '_blank')}
                fetchData={onFetch}
                isLoading={isLoading}
                hideDataInfo
            />
        </div>
    </section>
);

TableHistorySubmission.propTypes = {
    onFetch: PropTypes.func.isRequired,
    list: PropTypes.arrayOf(PropTypes.shape({ })),
    tableMeta: PropTypes.shape({
        total: PropTypes.number.isRequired,
        pageSize: PropTypes.number.isRequired,
        pageIndex: PropTypes.number.isRequired,
    }),
    isLoading: PropTypes.bool,
};

TableHistorySubmission.defaultProps = {
    list: [],
    tableMeta: {
        total: 0,
        pageSize: 10,
        pageIndex: 0,
    },
    isLoading: false,
};

export default TableHistorySubmission;
