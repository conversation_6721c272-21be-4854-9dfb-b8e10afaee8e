import React, { Component } from 'react';
import { PropTypes } from 'prop-types';
import moment from 'moment';

import CoreHOC from '../../../core/CoreHOC';
import Table from '../../../components/retina/table/Table';
import Select from '../../../components/form/Select';
import { catchError } from '../../../utils/helper';

import * as walletApi from '../../../data/wallet';
import { columns, statusFilterList } from './config/tselPoin';

import { SUB_TYPE_SUBMISSIONS } from '../../../enum/submissionType';
import InputText from '../../../components/form/InputText';

class TSelPoin extends Component {
  static propTypes = {
    calendar: PropTypes.shape({
      start: PropTypes.string,
      end: PropTypes.string,
    }),
    assignCalendar: PropTypes.func,
    assignButtons: PropTypes.func,
    router: PropTypes.shape({
      push: PropTypes.func,
    }),
    notificationSystem: PropTypes.shape({
      addNotification: PropTypes.func,
    }),
    showProgress: PropTypes.func,
    hideProgress: PropTypes.func,
  }

  static defaultProps = {
    calendar: {
      start: '',
      end: '',
    },
    assignCalendar: null,
    assignButtons: null,
    router: {
      push: null,
    },
    notificationSystem: ({
      addNotification: () => {
        // do nothing
      },
    }),
    showProgress: () => {
      // do nothing
    },
    hideProgress: () => {
      // do nothing
    },
  }

  constructor(props) {
    super(props);
    const { calendar } = this.props;
    this.state = {
      calendar,
      statusFilter: '0',
      search: '',
      isFetch: false,
      tableData: [],
      tableMeta: {
        pageIndex: 0,
        pageSize: 10,
        total: 0,
      },
    };
  }

  componentWillMount() {
    const {
      assignCalendar, assignButtons,
    } = this.props;
    assignCalendar(null, null, (startDate, endDate) => {
      this.changeDateHandler(startDate, endDate);
    });
    assignButtons([{
      type: 'primary',
      content: (
        <span>
          <i className="fa fa-plus" />
          {' '}
          Tambah Pengajuan Telkomsel Poin
        </span>
      ),
      action: this.callCreateHandler,
    }]);
    this.refetchTable();
  }

  callCreateHandler = () => {
    const { router } = this.props;
    router.push('/non-cash-setting/tsel-poin/create');
  }

  callEditDetailHandler = ({ original: data }) => {
    const { router } = this.props;
    router.push(`/non-cash-setting/tsel-poin/view/${data.submission_no}`);
  }

  changeDateHandler = (startDate, endDate) => {
    const { assignCalendar } = this.props;
    assignCalendar(startDate, endDate);
    this.setState({
      calendar: {
          start: startDate,
          end: endDate,
      },
    }, () => {
      this.refetchTable();
    });
  }

  fetchDataHandler = async (state) => {
    const {
      showProgress, hideProgress, notificationSystem,
    } = this.props;
    const {
      pageIndex, pageSize, sortAccessor, sortDirection,
    } = state;
    const { statusFilter, calendar, search } = this.state;

    const payload = {
      start_date: moment(calendar.start, 'DD-MM-YYYY').format('YYYY-MM-DD'),
      end_date: moment(calendar.end, 'DD-MM-YYYY').format('YYYY-MM-DD'),
      page: parseInt(pageIndex, 10) + 1,
      limit: pageSize,
      type: SUB_TYPE_SUBMISSIONS.TSEL_POIN,
      status: statusFilter,
      ...search && { search },
      ...(sortAccessor && sortDirection && { sort: `${sortAccessor} ${sortDirection}` }),
    };

    showProgress();
    this.setState({ isFetch: true });
    try {
      const res = await walletApi.getMerchantList(payload);

      if (!res) throw new Error('Gagal Mendapatkan Data');
      this.setState({
        tableData: res.data,
        tableMeta: {
          pageIndex,
          pageSize,
          total: res.meta.total,
        ...(sortAccessor && sortDirection && { sortAccessor, sortDirection }),
        },
      });
    } catch (e) {
      notificationSystem.addNotification({
        title: 'Gagal Mendapatkan Data Akun',
        message: catchError(e),
        level: 'error',
      });
    } finally {
      this.setState({ isFetch: false });
      hideProgress();
    }
  }

  changeSearchFilter = val => this.setState({ search: val }, () => this.refetchTable())

  changeStatusFilterListFilterHandler = (val) => {
    this.setState({ statusFilter: val }, () => {
      this.refetchTable();
    });
  }

  refetchTable = () => {
    const { tableMeta } = this.state;
    this.fetchDataHandler({ ...tableMeta, pageIndex: 0 });
  }

  render() {
    const {
      statusFilter, search, isFetch, tableData, tableMeta,
    } = this.state;

    return (
      <div>
        <section className="panel">
          <div className="panel-heading table-header">
            <div className="row">
              <div className="col-md-6">
                <h4 className="title">Telkomsel Poin</h4>
              </div>
              <div className="col-md-3">
                <Select
                  data={statusFilterList}
                  value={statusFilter}
                  changeEvent={this.changeStatusFilterListFilterHandler}
                />
              </div>
              <div className="col-md-3">
                <div className="filter-container">
                  <InputText
                    placeholder="Search keyword"
                    changeEvent={this.changeSearchFilter}
                    wait={500}
                  />
                </div>
              </div>
            </div>
          </div>
          <div className="panel-body">
            <Table
              columns={columns}
              data={tableData}
              totalData={tableMeta.total}
              pageIndex={tableMeta.pageIndex}
              rowLimit={tableMeta.pageSize}
              isLoading={isFetch}
              searchQuery={search}
              onRowClick={this.callEditDetailHandler}
              fetchData={this.fetchDataHandler}
            />
          </div>
        </section>
      </div>
    );
  }
}

export default CoreHOC(TSelPoin);
