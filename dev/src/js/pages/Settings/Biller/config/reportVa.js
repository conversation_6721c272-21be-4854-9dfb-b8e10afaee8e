import React from 'react';
import StatusApproval from '../components/VaStatus';
import NumberDateTimeColumn from '../../../../components/table/components/NumberDateTimeColumn';
import { currency } from '../../../../utils/helper';

export const tableColumn = onCheckTransaction => ([
    {
        Header: 'Nama Customer',
        accessor: 'customer_name',
        unsortable: true,
    },
    {
        Header: 'Nomor Va',
        accessor: 'va_number',
        unsortable: true,
    },
    {
        Header: 'Bank',
        accessor: 'bank_name',
        unsortable: true,
    },
    {
        Header: 'Amount',
        accessor: 'amount',
        Cell: ({ value }) => currency({ value }),
        unsortable: true,
    },
    {
        Header: 'Created Date',
        accessor: 'created_date',
        Cell: NumberDateTimeColumn,
        unsortableunsortable: true,
    },
    {
        Header: 'Updated Date',
        accessor: 'updated_date',
        Cell: NumberDateTimeColumn,
    },
    {
        Header: 'Expired Date',
        accessor: 'expired_date',
        Cell: NumberDateTimeColumn,
    },
    {
        Header: 'Status',
        accessor: 'status',
        Cell: StatusApproval,
        unsortable: true,
    },
    {
        Header: 'Action',
        accessor: 'transaction_no',
        colWidth: 150,
        unsortable: true,
        Cell: ({ value }) => (
            <button
                className="btn btn-link"
                type="button"
                onClick={() => onCheckTransaction(value)}
                style={{ whiteSpace: 'nowrap', border: 'none' }}
                disabled={!value}
            >
                <i className="fa fa-refresh mr-2" />
                &nbsp;
                Cek Status
            </button>
        ),
        unsortable: true,
    },
]);

export const statusFilterList = [
    {
        id: '',
        name: 'Semua Status',
    },
    {
        value: '1',
        label: 'Open',
    },
    {
        value: '0',
        label: 'Expired',
    },
];

export const OrderResponseCode = {
    52: value => `Transaksi ${value} berhasil`,
    53: value => `Transaksi ${value} dibatalkan`,
    54: value => `Transaksi ${value} sudah melewati batas pembayaran`,
    31: value => `Transaksi ${value} belum dibayarkan oleh User`,
    55: value => `Transaksi ${value} gagal`,
};
