import StatusApproval from '../../../../components/table/components/StatusApproval';
import NumberDateTimeColumn from '../../../../components/table/components/NumberDateTimeColumn';

const columns = [
    {
      Header: 'Business Name',
      accessor: 'merchant_name',
    },
    {
      Header: 'Outlet',
      accessor: 'outlet_name',
    },
    {
      Header: 'Status',
      accessor: 'status',
      Cell: StatusApproval,
    },
    {
      Header: 'Create Date',
      accessor: 'createdate',
      Cell: NumberDateTimeColumn,
    },
    {
      Header: 'Update Date',
      accessor: 'updatedate',
      Cell: NumberDateTimeColumn,
    },
    {
      Header: 'Keterangan',
      accessor: 'notes',
    },
];

const statusFilterList = [
    {
      id: '0',
      name: 'Semua Data',
    },
    {
      id: '1',
      name: 'In Progress',
    },
    {
      id: '2',
      name: 'Reject',
    },
    {
      id: '4',
      name: 'Approve',
    },
    {
      id: '9',
      name: 'Suspend',
    },
];

const errorMessages = {
  businessEmail: 'Email usaha harus diisi',
  businessPhone: 'Phone usaha harus diisi',
  businessName: 'Nama usaha harus diisi',
  businessAddress: 'Alamat usaha harus diisi',
  businessProvince: 'Provinsi usaha harus diisi',
  businessCity: 'Kota usaha harus diisi',
  businessZipCode: 'Kode pos usaha harus diisi',
  businessLocationStatus: 'Status lokasi usaha harus diisi',
  companyName: 'Nama badan usaha harus diisi',
  ownerName: 'Nama pemilik harus diisi',
  direksiName: 'Nama direksi harus diisi',
  ownerAddress: 'Alamat pemilik harus diisi',
  direksiAddress: 'Alamat direksi harus diisi',
  ownerId: 'No KTP pemilik harus diisi',
  direksiId: 'No KTP direksi harus diisi',
  ownerPhone: 'No Telp pemilik harus diisi',
  direksiPhone: 'No Telp direksi harus diisi',
  ownerEmail: 'Email pemilik harus diisi',
  direksiEmail: 'Email direksi harus diisi',
  bankName: 'Nama bank harus diisi',
  bankAccountNumber: 'Nomor rekening bank harus diisi',
  bankAccountpic: 'Atas nama harus diisi',
  bankAccountOutlet: 'Cabang bank harus di isi',
  couponKeyword: 'Keyword Coupon harus di isi',
  keywordVerification: 'Keyword Verification harus di isi',
  externalCode: 'External Oultet Code harus di isi',
  couponPrefix: 'Coupon Prefix harus di isi',
  couponQuantity: 'Coupon Quantity harus di isi',
};

const fileUploadService = 'wallet';
export {
    columns, statusFilterList, errorMessages, fileUploadService,
};
