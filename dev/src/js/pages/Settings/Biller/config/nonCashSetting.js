import React from 'react';
import InputCheckbox from '../../../../components/form/InputCheckbox';

const tableColumn = [
  {
    id: 'selection',
    Header: ({
        getToggleAllPageRowsSelectedProps, toggleAllPageRowsSelected,
    }) => (
      <div className="d-flex justify-content-center align-items-center">
        <InputCheckbox
            {...getToggleAllPageRowsSelectedProps({ indeterminate: false })}
            changeEvent={checked => toggleAllPageRowsSelected(checked)}
        />
      </div>
    ),
    Cell: ({ row: { getToggleRowSelectedProps, toggleRowSelected } }) => (
        <div className="d-flex justify-content-center align-items-center">
            <InputCheckbox
                {...getToggleRowSelectedProps()}
                changeEvent={checked => toggleRowSelected(checked)}
            />
        </div>
    ),
    unsortable: true,
    colMinWidth: 50,
  },
  {
    Header: '<PERSON>a Usaha',
    accessor: 'usaha_name',
  },
  {
    Header: 'Email Outlet',
    accessor: 'usaha_email',
  },
  {
    Header: 'Nama Outlet',
    accessor: 'outlet_name',
  },
  {
    Header: 'Provider',
    accessor: 'provider_name',
  },
  {
    Header: 'status',
    accessor: 'status',
    Cell: ({ row: { original } }) => (+original.status === 1 ? (
      <div className="wallet-status-label" style={{ backgroundColor: '#66D4D3' }}>AKTIF</div>
    ) : (
      <div className="wallet-status-label" style={{ backgroundColor: '#C4C4C4' }}>NONAKTIF</div>
    )),
  },
];

const SwitchData = [
  {
      text: 'MESIN EDC',
      value: '1',
      disabled: true,
  },
  {
      text: 'TRANSFER',
      value: '2',
      disabled: true,
  },
  {
      text: 'WALLET QR SNAP DYNAMIC',
      value: '3',
      disabled: false,
  },
];

const dataStatusWallet = [
  {
      value: '1',
      text: 'Aktifkan Pembayaran',
  },
  {
      value: '0',
      text: 'Non-aktifkan Pembayaran',
  },
];


export { tableColumn, SwitchData, dataStatusWallet };
