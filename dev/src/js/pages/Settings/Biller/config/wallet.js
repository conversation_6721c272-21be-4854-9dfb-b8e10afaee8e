import StatusApproval from '../../../../components/table/components/StatusApproval';
import NumberDateTimeColumn from '../../../../components/table/components/NumberDateTimeColumn';
import EditColumn from '../../../../components/table/components/EditColumn';
import VerifiedStatus from '../../../../components/table/components/VerifiedStatus';
import ChannelColumn from '../components/ChannelColumn';

const tableColumn = [
  {
    Header: 'Busniness Name',
    accessor: 'merchant_name',
  },
  {
    Header: 'Provider',
    accessor: 'wallet_name',
  },
  {
    Header: 'Outlet',
    accessor: 'outlet_name',
  },
  {
    Header: 'Status',
    accessor: 'status',
    titleStyles: { width: '15%' },
    Cell: StatusApproval,
  },
  {
    Header: 'Channel',
    accessor: 'channel',
    titleStyles: { width: '15%' },
    Cell: ChannelColumn,
  },
  {
    Header: 'Create Date',
    accessor: 'createdate',
    Cell: NumberDateTimeColumn,
  },
  {
    Header: 'Update Date',
    accessor: 'updatedate',
    Cell: NumberDateTimeColumn,
  },
  {
    Header: 'Keterangan',
    accessor: 'notes',
  },
  {
    Header: 'Tipe Qris',
    accessor: 'qr_type',
  },
  {
    Header: 'Verified',
    accessor: 'is_verified',
    headerClassName: 'text-center',
    Cell: VerifiedStatus,
  },
];

const statusFilterList = [
  {
    id: '0',
    name: 'Semua Status',
  },
  {
    id: '1',
    name: 'In Progress',
  },
  {
    id: '2',
    name: 'Reject',
  },
  {
    id: '4',
    name: 'Approve',
  },
  {
    id: '9',
    name: 'Suspend',
  },
];

const jenisPajakList = [
  {
    value: 'Pengusaha Kena Pajak',
    name: 'Pengusaha Kena Pajak',
  },
  {
    value: 'Non Pengusaha Kena Pajak',
    name: 'Non Pengusaha Kena Pajak',
  },
];

const ciraterialMerchant = [
  {
    value: 'Mikro <300jt',
    name: 'Mikro <300jt',
  },
  {
    value: 'Kecil <2.5M',
    name: 'Kecil <2.5M',
  },
  {
    value: 'Menengah <50M',
    name: 'Menengah <50M',
  },
  {
    value: 'Besar >50M',
    name: 'Besar >50M',
  },
];

const settlementLimitList = [
  {
    id: '1',
    name: 'Daily Settlement with Transfer Fee',
  },
  {
    id: '2',
    name: 'Limit 250rb Free Transfer Fee',
  },
];

const errorMessages = {
  businessEmail: 'Email usaha harus diisi',
  businessPhone: 'Phone usaha harus diisi',
  businessName: 'Nama usaha harus diisi',
  businessAddress: 'Alamat usaha harus diisi',
  businessProvince: 'Provinsi usaha harus diisi',
  businessCity: 'Kota usaha harus diisi',
  businessArea: 'Kecamatan usaha harus diisi',
  businessZipCode: 'Kode pos usaha harus diisi',
  businessLocationStatus: 'Status lokasi usaha harus diisi',
  namaBadanUsaha: 'Nama badan usaha harus diisi',
  ownerName: 'Nama pemilik harus diisi',
  direksiName: 'Nama direksi harus diisi',
  ownerAddress: 'Alamat pemilik harus diisi',
  direksiAddress: 'Alamat direksi harus diisi',
  ownerId: 'No KTP pemilik harus diisi',
  direksiId: 'No KTP direksi harus diisi',
  ownerPhone: 'No Telp pemilik harus diisi',
  direksiPhone: 'No Telp direksi harus diisi',
  ownerEmail: 'Email pemilik harus diisi',
  direksiEmail: 'Email direksi harus diisi',
  bankName: 'Nama bank harus diisi',
  bankAccountNumber: 'Nomor rekening bank harus diisi',
  bankAccountpic: 'Atas nama harus diisi',
  bankAccountOutlet: 'Outlet harus diisi',
  ownerMerchantNumber: 'Nomor Izin Usaha Merchant harus diisi',
  ownerSiupNumber: 'Nomor Nomor Surat Izin Usaha Perdaganan (SIUP) harus diisi',
  merchantEstablishmentPlace: 'Tempat Pendirian Merchant harus diisi',
  validityPeriode: 'Masa Berlaku Izin Usaha Merchant harus di',
  businessPicName: 'Nama Pengurus harus diisi',
  businessPicTitle: 'Jabaran Penurus harus diisi',
  businessPicFinance: 'Nama PIC Keuangan harus diisi',
  picFinancePhoneNumber: 'Kontak PIC Keuangan harus diisi',
  picFinanceEmail: 'Email PIC keuangan harus diisi',
  businessLocationBusiness: 'Tipe Merchant harus diisi',
  typeLocationMerchant: 'Detail Lokasi Merchant harus diisi',
  linkGoogleMaps: 'Link Google Maps harus diisi',
  latitude: 'Latitude harus diisi',
  longitude: 'Longitude harus diisi',
  ktp: 'KTP harus diisi',
  akta: 'Akta Pendirian harus diisi',
  skm: 'Surat Keputusan Menteri harus diisi',
  siup: 'Surat Izin Usaha Perdangangan (SIUP) harus diisi',
  tdp: 'Nomor Izin Usaha harus diisi',
  bank: 'Rekening harus diisi',
};

const fileUploadService = 'wallet';

const tableColumnHistory = [
  {
    Header: 'Busniness Name',
    accessor: 'merchant_name',
    unsortable: true,
  },
  {
    Header: 'Provider',
    accessor: 'wallet_name',
    unsortable: true,
  },
  {
    Header: 'Tipe QRIS',
    accessor: 'qr_type',
    unsortable: true,
  },
  {
    Header: 'Outlet',
    accessor: 'outlet_name',
    unsortable: true,
  },
  {
    Header: 'Status',
    accessor: 'status',
    Cell: StatusApproval,
    unsortable: true,
  },
  {
    Header: 'Create Date',
    accessor: 'createdate',
    Cell: NumberDateTimeColumn,
    unsortable: true,
  },
  {
    Header: 'Update Date',
    accessor: 'updatedate',
    Cell: NumberDateTimeColumn,
    unsortable: true,
  },
  {
    Header: 'Keterangan',
    accessor: 'notes',
    unsortable: true,
  },
];

const qrisTypeOption = [
  {
    id: '',
    name: 'Semua QRIS',
  },
  {
    id: 'STATIC',
    name: 'Static',
  },
  {
    id: 'DYNAMIC',
    name: 'Dynamic',
  },
];

const channelOption = [
  {
    id: '',
    name: 'Semua Channel',
  },
  {
    id: 'dashboard',
    name: 'Dashboard',
  },
  {
    id: 'cockpit',
    name: 'Cockpit',
  },
  {
    id: 'crm',
    name: 'CRM',
  },
];

const applicantIdentityOption = {
  PERSEORANGAN: [
    { value: '1', label: 'Pemilik' },
    { value: '2', label: 'Penanggung Jawab' },
  ],
  BADAN_HUKUM: [
    { value: '1', label: 'Direksi' },
    { value: '2', label: 'Penanggung Jawab' },
  ],
};

const APPLICANT_IDENTITY = {
  OWNER: '1',
  PIC: '2',
};

export {
  tableColumn,
  statusFilterList,
  jenisPajakList,
  errorMessages,
  fileUploadService,
  ciraterialMerchant,
  settlementLimitList,
  tableColumnHistory,
  qrisTypeOption,
  channelOption,
  applicantIdentityOption,
  APPLICANT_IDENTITY,
};
