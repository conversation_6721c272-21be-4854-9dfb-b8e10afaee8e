import React, { Component } from 'react';
import PropTypes from 'prop-types';
import update from 'immutability-helper';

import CoreHOC from '../../../core/CoreHOC';
import Table from '../../../components/retina/table/Table';
import SidePopup from '../../../components/sidepopup/Container';
import InputText from '../../../components/form/InputText';
import InputPercent from '../../../components/form/InputPercent';
import Switch from '../../../components/form/Switch';
import SwitchBox from '../../../components/form/SwitchBox';
import ModalPopup from '../../../components/modalpopup/Container';
import SelectMultiple from '../../../components/form/SelectMultiple';
import SwitchBoxMultiple from '../../../components/form/SwitchBoxMultiple';

import SelectStatusNonCash from './components/SelectStatusNonCash';

import * as walletApi from '../../../data/wallet';

import {
    tableColumn,
    SwitchData,
    dataStatusWallet,
} from './config/nonCashSetting';
import { catchError } from '../../../utils/helper';

class NoCashSetting extends Component {
    static propTypes = {
        router: PropTypes.shape({
            push: PropTypes.func,
        }).isRequired,
        notificationSystem: PropTypes.shape({
            addNotification: PropTypes.func,
        }),
        hideProgress: PropTypes.func.isRequired,
    };

    static defaultProps = {
        notificationSystem: {
            addNotification: () => {},
        },
    };

    constructor(props) {
        super(props);

        this.state = {
            content: [],
            idDetail: '',
            namaUsaha: '',
            outlet: '',
            status: false,
            cashNonTunai: '',
            switchFilter: '',
            bank: '',
            merchant: '',
            bukuKas: '',
            mdr: '',
            customer: '',
            allStatus: '',
            confirmText: false,
            provider: [],
            filterStatus: [
                { text: 'aktif', value: true },
                { text: 'nonaktif', value: true },
            ],
            resultStatusWallet: '1',
            providerList: [],
            isFetch: false,
            search: '',
            tableData: [],
            tableMeta: {
                pageIndex: 0,
                pageSize: 10,
                total: 0,
            },
        };
    }

    componentWillMount() {
        this.fetchBank();
    }

    componentDidMount = () => {
        const {
            assignButtons, assignFilterColoumn, assignRangeDate, assignCalendar,
        } = this.props;
        const { tableMeta } = this.state;
        assignCalendar(null, null, null);
        assignButtons();
        assignFilterColoumn([]);
        assignRangeDate([]);
        this.fetchDataHandler(tableMeta);
    };

    componentDidUpdate(prevProps, prevState) {
        const { filterStatus, provider } = this.state;

        if (
            prevState.filterStatus[0].value !== filterStatus[0].value
            || prevState.filterStatus[1].value !== filterStatus[1].value
        ) {
            this.refetchData();
        }

        if (prevState.provider.length !== provider.length) {
            clearTimeout(this.timeOut);
            this.timeOut = setTimeout(() => {
                this.refetchData();
            }, 500);
        } else {
            let providerIsDifferent = false;

            for (let i = 0; i < provider.length; i++) {
                if (prevState.provider[i] !== provider[i]) {
                    providerIsDifferent = true;
                }

                if (providerIsDifferent) break;
            }

            if (providerIsDifferent) {
                clearTimeout(this.timeOut);
                this.timeOut = setTimeout(() => {
                    this.refetchData();
                }, 500);
            }
        }
    }

    fetchDataHandler = async (state) => {
        const { provider, filterStatus, search } = this.state;

        const { showProgress, hideProgress, notificationSystem } = this.props;
        const {
            pageIndex, pageSize, sortAccessor, sortDirection,
        } = state;

        const payload = {
            limit: pageSize,
            page: pageIndex + 1,
            ...(search && { search }),
            provider: JSON.stringify(provider),
            ...sortAccessor && { column: sortAccessor },
            ...sortDirection && { isAsc: sortDirection },
        };
        Object.assign(payload, { status: '' });

        const aktifValue = filterStatus[0].value;
        const inaktifValue = filterStatus[1].value;
        if (inaktifValue && !aktifValue) {
            Object.assign(payload, { status: 0 });
        } else if (aktifValue && !inaktifValue) {
            Object.assign(payload, { status: 1 });
        } else {
            delete payload.status;
        }
        showProgress();
        this.setState({ isFetch: true });
        try {
            const res = await walletApi.fetchNoPaymentCash(payload);

            if (!res) throw new Error('Gagal Mendapatkan Data');
            this.setState({
                tableData: res.data,
                tableMeta: {
                    pageIndex,
                    pageSize,
                    total: res.total,
                    ...sortAccessor && { sortAccessor },
                    ...sortDirection && { sortDirection },
                },
            });
        } catch (e) {
            notificationSystem.addNotification({
                title: 'Gagal Mendapatkan Data',
                message: catchError(e),
                level: 'error',
            });
        } finally {
            this.setState({ isFetch: false });
            hideProgress();
        }
    };

    callActionHandler = (value) => {
        const { original: formData } = value;
        const cashNonTunai = `${formData.provider_name} ${formData.tcash_qr_snap}`;
        let switchFilter = '1';
        if (formData.tcash_qr_snap === 'Dynamic') {
            switchFilter = '3';
        }

        let bukuKas = '';
        if (formData.akunting_code) {
            bukuKas = `${formData.akunting_code} | ${formData.akunting_name}`;
        }

        this.setState(
            {
                idDetail: formData.id,
                namaUsaha: formData.usaha_name || '',
                outlet: formData.outlet_name || '',
                status: formData.status === '1',
                cashNonTunai,
                switchFilter,
                bank: formData.provider_name || '',
                merchant: formData.merchant_code || '',
                bukuKas,
                mdr: formData.mdr || '',
                customer: formData.tambahan_biaya || '',
            },
            () => {
                this.sidePop.showPopup();
            },
        );
    };

    checkHandler = (data) => {
        this.setState({
            content: data,
        });
    };

    showPopupChangeStatus = () => {
        const { resultStatusWallet } = this.state;
        let confirmText = false;
        if (resultStatusWallet === '1') {
            confirmText = true;
        }

        this.setState(
            {
                allStatus: resultStatusWallet,
                confirmText,
            },
            () => {
                this.actionPopup.showPopup();
            },
        );
    };

    updateStatus = () => {
        const { allStatus, content } = this.state;
        const checkListArray = content.forEach(id => parseInt(id, 10));

        this.updateData(checkListArray, allStatus);
        this.actionPopup.hidePopup();
    };

    saveHandler = (id) => {
        const { status } = this.state;
        let aktifStatus = '0';
        if (status) {
            aktifStatus = '1';
        }

        this.updateData([id], aktifStatus);
        this.sidePop.hidePopup();
    };

    updateData = (id, status) => {
        const {
 router, notificationSystem, showProgress, hideProgress,
} = this.props;
        const { tableKey } = this.state;
        const param = {
            id: JSON.stringify(id),
            status,
        };
        showProgress();
        walletApi.updateNoPaymentCash(param).then(
            () => {
                this.setState({
                    content: [],
                    tableKey: tableKey + 1,
                }, () => {
                    notificationSystem.addNotification({
                        title: 'Berhasil Update Data',
                        level: 'success',
                    });
                });
            },
            (message) => {
                hideProgress();
                if (!message) {
                    router.push('/auth/login');
                } else {
                    notificationSystem.addNotification({
                        title: 'Gagal mengupdate data',
                        message: '',
                        level: 'error',
                    });
                }
            },
        );
    };

    changeSelectStatusUpdate = (resultStatusWallet) => {
        this.setState({
            resultStatusWallet,
        });
    };

    changeSearchFilter = (val) => {
        this.setState({ search: val }, () => this.refetchData());
    }

    changeEventProvider(val) {
        const provider = [];
        val.forEach(v => provider.push(v.value));

        this.setState({ provider });
    }

    changeFiltterStatusHandler(index, value) {
        const { filterStatus } = this.state;
        const newStatus = update(filterStatus, {
            [index]: { value: { $set: value } },
        });

        this.setState({ filterStatus: newStatus });
    }

    fetchBank() {
        const { router, notificationSystem } = this.props;

        walletApi.fetchPaymentList().then(
            (response) => {
                const providerList = [];
                response.data.forEach((e) => {
                    providerList.push({
                        value: e.id,
                        label: e.name,
                    });
                });
                this.setState({
                    providerList,
                });
            },
            (message) => {
                if (!message) {
                    router.push('/auth/login');
                } else {
                    notificationSystem.addNotification({
                        title: 'Gagal mendapatkan data',
                        message: '',
                        level: 'error',
                    });
                }
            },
        );
    }

    refetchData() {
        const { tableMeta } = this.state;
        this.fetchDataHandler({ ...tableMeta, pageIndex: 0 });
    }

    render() {
        const {
            idDetail,
            namaUsaha,
            outlet,
            cashNonTunai,
            switchFilter,
            bank,
            merchant,
            bukuKas,
            mdr,
            customer,
            confirmText,
            provider,
            status,
            resultStatusWallet,
            providerList,
            filterStatus,
            content,
            tableData,
            tableMeta,
            search,
            isFetch,
            tableKey,
        } = this.state;

        const selectedRow = content.reduce((acc, curr) => ({ ...acc, [curr]: true }), {});

        return (
            <div>
                <section className="panel">
                    <div className="panel-heading">
                        <div className="row">
                            <div className="col-md-3">
                                <h6 className="panel-title">
                                    Non Cash Setting
                                </h6>
                            </div>
                            <div
                                className="col-md-3"
                                style={{
                                    paddingRight: '0px',
                                    float: 'right',
                                }}
                            >
                                <InputText
                                    placeholder="Search keyword"
                                    changeEvent={this.changeSearchFilter}
                                    wait={500}
                                />
                            </div>
                        </div>
                    </div>
                    <div className="panel-heading table-header">
                        <div className="row">
                            <div className="col-md-12">
                                <div style={{ width: '100%' }}>
                                    <div className="row">
                                        <div className="col-md-5">
                                            <SelectStatusNonCash
                                                data={dataStatusWallet}
                                                value={resultStatusWallet}
                                                onChangeHandler={val => this.changeSelectStatusUpdate(val)}
                                                rowsData={content.length}
                                                onSaveHandler={() => this.showPopupChangeStatus()}
                                                disabled={!content.length}
                                            />
                                        </div>
                                        <div className="col-md-4" style={{ position: 'relative', zIndex: 11 }}>
                                            <SelectMultiple
                                                options={providerList}
                                                selector="label"
                                                value={provider}
                                                changeEvent={val => this.changeEventProvider(val)}
                                                placeholder="Pilih Provider"
                                                style={{ color: 'red' }}
                                            />
                                        </div>
                                        <div
                                            className="col-md-3"
                                            style={{
                                                float: 'right',
                                                textAlign: 'right',
                                            }}
                                        >
                                            <SwitchBoxMultiple
                                                dataset={filterStatus}
                                                onChangeEvent={(index, value) => this.changeFiltterStatusHandler(index, value)}
                                                widthFull
                                                widthFullButton
                                            />
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div className="panel-body">
                        <Table
                            key={`table-non-cash-${tableKey}`}
                            columns={tableColumn}
                            data={tableData}
                            totalData={tableMeta.total}
                            pageIndex={tableMeta.pageIndex}
                            rowLimit={tableMeta.pageSize}
                            searchQuery={search}
                            isLoading={isFetch}
                            onRowClick={this.callActionHandler}
                            fetchData={this.fetchDataHandler}
                            onSelectedChange={this.checkHandler}
                            selectedIds={selectedRow}
                        />
                    </div>
                </section>

                <SidePopup
                    ref={(c) => {
                        this.sidePop = c;
                    }}
                    width={650}
                    saveHandle={() => this.saveHandler(idDetail)}
                >
                    <h4 className="side-popup-title">
                        Detail Pembayaran Non-Tunai
                    </h4>
                    <div className="row mb-sm">
                        <div className="col-sm-12">
                            <InputText
                                label="Nama Usaha"
                                placeholder="Nama Usaha"
                                value={namaUsaha}
                                disabled
                            />
                        </div>
                    </div>
                    <div className="row mb-sm">
                        <div className="col-sm-12">
                            <InputText
                                label="Pilih Outlet"
                                placeholder="Pilih Outlet"
                                value={outlet}
                                disabled
                            />
                        </div>
                    </div>
                    <div className="row mb-sm">
                        <div className="col-sm-6">Status</div>
                        <div
                            className="col-sm-6"
                            style={{ textAlign: 'right' }}
                        >
                            <Switch
                                checked={status}
                                changeEvent={value => this.setState({
                                        status: value,
                                    })
                                }
                            />
                        </div>
                    </div>
                    <div className="row mb-sm">
                        <div className="col-sm-12">
                            <InputText
                                label="Nama Pembayaran Non Tunai *"
                                placeholder="Nama Pembayaran Non Tunai *"
                                value={cashNonTunai}
                                disabled
                            />
                        </div>
                    </div>
                    <div className="row mb-sm">
                        <div className="col-sm-12">Jenis *</div>
                        <div className="col-sm-12">
                            <SwitchBox
                                dataset={SwitchData}
                                value={switchFilter}
                                changeEvent={() => {}}
                                fullWidthButton
                            />
                        </div>
                    </div>
                    <div className="row mb-sm">
                        <div className="col-sm-12">
                            <InputText
                                label="Provider / Bank *"
                                placeholder="Provider / Bank *"
                                value={bank}
                                disabled
                            />
                        </div>
                    </div>
                    <div className="row mb-sm">
                        <div className="col-sm-12">
                            <InputText
                                label="Kode Merchant"
                                placeholder="Kode Merchant"
                                value={merchant}
                                disabled
                            />
                        </div>
                    </div>
                    <div className="row mb-sm">
                        <div className="col-sm-12">
                            <InputText
                                label="Settlement Buku Kas"
                                placeholder="Settlement Buku Kas"
                                value={bukuKas}
                                disabled
                            />
                        </div>
                    </div>
                    <div className="row mb-sm">
                        <div className="col-sm-12">
                            <InputPercent
                                label="MDR (%)"
                                value={mdr}
                                name="mdr"
                                desimal
                                disabled
                            />
                        </div>
                    </div>
                    <div className="row mb-sm">
                        <div className="col-sm-12">
                            <InputPercent
                                label="Biaya Tambahan Customer %"
                                value={customer}
                                name="customer"
                                desimal
                                disabled
                            />
                        </div>
                    </div>
                </SidePopup>
                <ModalPopup
                    title="Konfirmasi"
                    width={560}
                    confirmHandle={(val) => {
                        this.updateStatus(val);
                    }}
                    confirmText={confirmText ? 'Aktifkan' : 'Non-aktifkan'}
                    type={confirmText ? 'confirm' : 'delete'}
                    ref={(c) => {
                        this.actionPopup = c;
                    }}
                >
                    <div className="row mb-sm">
                        <div className="col-sm-12">
                            Apakah anda yakin ingin
                            {' '}
                            <b>
                                {confirmText
                                    ? 'Mengaktifkan'
                                    : 'Menon-aktifkan'}
                            </b>
                            {' '}
                            wallet dari
                            {' '}
                            <b>{content.length}</b>
                            {' '}
                            outlet
                        </div>
                    </div>
                </ModalPopup>
            </div>
        );
    }
}

export default CoreHOC(NoCashSetting);
