import React, { Component } from 'react';
import { PropTypes } from 'prop-types';
import moment from 'moment';
import _ from 'lodash';
import CoreHOC from '../../../core/CoreHOC';
import Table from '../../../components/retina/table/Table';
import Select from '../../../components/form/Select';
import SelectMultiple from '../../../components/form/SelectMultiple';

import * as billerApi from '../../../data/biller';

import { tableColumn, statusFilterList, OrderResponseCode } from './config/reportVa';

import { catchError } from '../../../utils/helper';
import InputNumber from '../../../components/form/InputNumber';
import ModalPopup from '../../../components/modalpopup/Container';
import InputText from '../../../components/form/InputText';

@CoreHOC
export default class Wallet extends Component {
    static propTypes = {
        calendar: PropTypes.shape({
            start: PropTypes.string,
            end: PropTypes.string,
        }),
        assignCalendar: PropTypes.func,
        assignButtons: PropTypes.func,
        router: PropTypes.shape({
            push: PropTypes.func,
        }),
        notificationSystem: PropTypes.shape({
            addNotification: PropTypes.func,
        }),
        showProgress: PropTypes.func.isRequired,
        hideProgress: PropTypes.func.isRequired,
    }

    static defaultProps = {
        calendar: {
            start: '',
            end: '',
        },
        assignCalendar: null,
        assignButtons: null,
        router: {
            push: null,
        },
        notificationSystem: ({
            addNotification: () => {
                // do nothing
            },
        }),
    }

    constructor(props) {
        super(props);
        const { calendar } = this.props;
        this.state = {
            calendar,
            statusFilter: '',
            providerFilter: [],
            providerFilterList: [],
            amountFilter: undefined,
            popupMessage: '',
            search: '',
            isFetch: false,
            tableData: [],
            tableMeta: {
                pageIndex: 0,
                pageSize: 10,
                total: 0,
            },
        };
        this.debounceSearch = _.debounce(this.refetchData.bind(this), 500);
    }

    componentWillMount() {
        const {
            assignCalendar, assignButtons,
        } = this.props;

        assignCalendar(null, null, (startDate, endDate) => {
            this.changeDateHandler(startDate, endDate);
        });
        assignButtons([]);

        this.getPaymentMethodList();
        this.refetchData();
    }

    refetchData = async () => {
        const { showProgress, hideProgress } = this.props;
        const { tableMeta } = this.state;
        showProgress();
        await this.fetchDataHandler({ ...tableMeta, pageIndex: 0 });
        hideProgress();
    }

    getPaymentMethodList = async () => {
        const { notificationSystem, showProgress, hideProgress } = this.props;

        try {
            showProgress();
            const res = await billerApi.getBillerPaymentMethod();
            if (!res.status) throw new Error(res.msg);
            const { data } = res;
            const providerFilterList = data.filter(x => x.type === 'va').map(x => ({ value: Number(x.id), label: x.additional_data.constant_provider }));

            // providerFilterList.unshift({ id: 0, name: 'Semua Bank' });

            this.setState({
                providerFilter: providerFilterList,
                providerFilterList,
            });
        } catch (e) {
        notificationSystem.addNotification({
            title: 'Failed to get Provider data',
            message: catchError(e),
            level: 'error',
        });
        } finally {
            hideProgress();
        }
    }

    changeDateHandler = (startDate, endDate) => {
        const { assignCalendar } = this.props;
            assignCalendar(startDate, endDate);
        this.setState({
            calendar: {
                start: startDate,
                end: endDate,
            },
        }, this.refetchData);
    }

    fetchDataHandler = async (state) => {
        const { notificationSystem } = this.props;

        const {
            statusFilter, providerFilter, calendar, amountFilter, search,
        } = this.state;

        const {
            pageIndex, pageSize, sortAccessor, sortDirection,
        } = state;

        const payload = {
            start_date: moment(calendar.start, 'DD-MM-YYYY').format('YYYY-MM-DD'),
            end_date: moment(calendar.end, 'DD-MM-YYYY').format('YYYY-MM-DD'),
            per_page: pageSize,
            page: parseInt(pageIndex, 10) + 1,
            limit: pageSize,
            ...(statusFilter && { status: statusFilter }),
            ...(providerFilter && providerFilter.length && { bank: providerFilter.map(x => x.value) }),
            ...(typeof amountFilter !== 'undefined' && { amount: amountFilter }),
            ...(sortAccessor && { order: sortAccessor }),
            ...(sortDirection && { sort: sortDirection }),
            ...(search && { search }),
        };
        this.setState({ isFetch: true });
        try {
            const res = await billerApi.getReportVaList(payload);
            if (!res.status) throw new Error(res.msg);
            this.setState({
                tableData: res.data,
                tableMeta: {
                    pageIndex,
                    pageSize,
                    total: res.meta.total,
                    ...sortAccessor && { sortAccessor },
                    ...sortDirection && { sortDirection },
                },
            });
        } catch (e) {
            notificationSystem.addNotification({
                title: 'Failed to get data',
                message: catchError(e),
                level: 'error',
            });
        } finally {
            this.setState({ isFetch: false });
        }
    }

    changeStatusFilterListFilterHandler = (val) => {
        this.setState({ statusFilter: val }, this.refetchData);
    }

    changeProviderFilterListHandler = (val) => {
        this.setState({ providerFilter: val }, this.refetchData);
    }

    changeAmountFilterHandler = (val, e) => {
        if (!e) return;
        this.setState({ amountFilter: val });
        this.debounceSearch();
    }

    changeSearchFilter = (val) => {
        this.setState({ search: val }, this.refetchData);
    }

    checkStatusHandle = async (val) => {
        const { notificationSystem, showProgress, hideProgress } = this.props;
        try {
            showProgress();
            const res = await billerApi.checkVaStatus(val);
            if (!res.data) throw new Error(res.status.message);
            this.setState({ popupMessage: OrderResponseCode[res.data.payment_status](res.data.transaction_no_nota) }, () => this.showPopup());
        } catch (e) {
            const err = e;
            notificationSystem.addNotification({
                title: 'Failed to check status transaction',
                message: catchError(err),
                level: 'error',
            });
        } finally {
            hideProgress();
        }
    };

    showPopup = () => {
        this.cekStatusPopup.showPopup();
    }

    hidePopup = () => {
        this.cekStatusPopup.hidePopup();
        this.setState({ popupMessage: '' }, () => this.refetchData());
    }

    render() {
        const {
            statusFilter, providerFilter, providerFilterList, amountFilter, popupMessage,
            search, isFetch, tableData, tableMeta,
        } = this.state;
        return (
            <div>
                <section className="panel">
                    <div className="panel-heading table-header">
                        <div className="row">
                            <div className="col-md-12">
                                <h4 className="panel-title" style={{ paddingTop: '8px' }}>Laporan Virtual Account</h4>
                            </div>
                        </div>
                    </div>
                    <div className="panel-heading table-header">
                        <div className="d-flex" style={{ justifyContent: 'space-between' }}>
                            <div className="row" style={{ width: '100%' }}>
                                <div className="col-sm-2">
                                    <InputNumber
                                        placeholder="Amount"
                                        name="amount"
                                        value={amountFilter}
                                        changeEvent={this.changeAmountFilterHandler}
                                    />
                                </div>
                                {providerFilterList.length === 0 || (
                                    <div className="col-sm-5">
                                        <SelectMultiple
                                            options={providerFilterList}
                                            value={providerFilter}
                                            selector="label"
                                            changeEvent={this.changeProviderFilterListHandler}
                                        />
                                    </div>
                                )}
                                <div className="col-sm-2">
                                    <Select
                                        data={statusFilterList}
                                        value={statusFilter}
                                        changeEvent={this.changeStatusFilterListFilterHandler}
                                    />
                                </div>
                                <div className="col-sm-3">
                                    <InputText
                                        placeholder="Search keyword"
                                        changeEvent={this.changeSearchFilter}
                                        wait={500}
                                    />
                                </div>
                            </div>
                        </div>
                    </div>
                    <div className="panel-body">
                        <Table
                            columns={tableColumn(this.checkStatusHandle)}
                            data={tableData}
                            totalData={tableMeta.total}
                            rowLimit={tableMeta.pageSize}
                            pageIndex={tableMeta.pageIndex}
                            searchQuery={search}
                            isLoading={isFetch}
                            fetchData={this.fetchDataHandler}
                        />
                    </div>
                </section>
                <ModalPopup
                    type="assign"
                    classes="assign-Modal"
                    confirmText="Oke"
                    confirmHandle={this.hidePopup}
                    ref={(c) => { this.cekStatusPopup = c; }}
                    width={460}
                    hideCancelButton
                >
                    <h3 style={{ paddingBottom: 16, borderBottom: 'solid 1px #005252' }}>
                        Information
                    </h3>
                    <p>
                        {popupMessage}
                    </p>
                </ModalPopup>
            </div>
        );
    }
}
