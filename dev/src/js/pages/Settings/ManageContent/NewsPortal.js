import React, { Component } from 'react';
import PropTypes from 'prop-types';
import * as moment from 'moment';
import CoreHOC from '../../../core/CoreHOC';
import { catchError } from '../../../utils/helper';

/* COMPONENTS */
import SwitchBox from '../../../components/form/SwitchBox';
import Button from '../../../components/form/Button';
import Select from '../../../components/form/Select';
import Table from '../../../components/retina/table/Table';
import CalendarPick from '../../../components/form/CalendarPick';

/* DATA */
import { getPost, getContentCategory, updatePostBulk } from '../../../data/setting/portal';

/* CONFIG */
import {
  TABLE_META, STATUS_LIST, SWITCH_LIST, FILTER_TYPE,
} from './config/Blog';
import { POST_TYPE_ENUM as postType } from './config/Post';
import { getFilterValue, getUpdatedFilterValue } from '../../../utils/table.util';
import InputText from '../../../components/form/InputText';

class NewsPortal extends Component {
  static propTypes = {
    calendar: PropTypes.shape({
      start: PropTypes.string,
      end: PropTypes.string,
    }).isRequired,
    assignCalendar: PropTypes.func.isRequired,
    assignButtons: PropTypes.func.isRequired,
    router: PropTypes.shape({
      push: PropTypes.func,
    }).isRequired,
    assignFilterColoumn: PropTypes.func.isRequired,
    assignRangeDate: PropTypes.func.isRequired,
    notificationSystem: PropTypes.shape({
      addNotification: PropTypes.func,
    }),
  }

  static defaultProps = {
    notificationSystem: ({
      addNotification: () => {},
    }),
  }

  constructor(props) {
    super(props);

    this.state = {
      type: postType.NEWS_PORTAL,
      category: [],
      statusSelect: '',
      filterTable: [
        { id: FILTER_TYPE.DATE_RANGE, value: { start: '', end: '' } },
        { id: FILTER_TYPE.STATUS, value: 'all' },
        { id: FILTER_TYPE.CATEGORY, value: 0 },
        { id: FILTER_TYPE.ALL, value: '' },
      ],
      isFetch: false,
      tableData: [],
      tableKey: 0,
      tableMeta: {
        pageIndex: 0,
        pageSize: 10,
        total: 0,
      },
      selectedRow: [],
    };
  }

  componentDidMount() {
    const {
      assignFilterColoumn, assignRangeDate,
      assignCalendar,
      assignButtons,
    } = this.props;
    const { tableMeta } = this.state;
    assignFilterColoumn([]);
    assignRangeDate([]);
    assignCalendar(null, null, null);
    assignButtons([
      { type: 'primary', content: <span> Add New Post </span>, action: () => { this.callManageHandler(); } },
    ]);
    this.getCategory();
    this._onFetch(tableMeta);
  }

  getCategory() {
    getContentCategory({ type: postType.NEWS_PORTAL }).then((res) => {
      if (res.data !== null) {
        this.setState({
          category: [{ id: 0, value: 'All Category' }, ...res.data],
        });
      }
    });
  }

  _onFetch = async (state) => {
    const { notificationSystem } = this.props;
    const { filterTable } = this.state;
    const {
      pageIndex, pageSize, sortAccessor, sortDirection,
    } = state;

    const { start, end } = getFilterValue(filterTable, FILTER_TYPE.DATE_RANGE);
    const statusFilter = getFilterValue(filterTable, FILTER_TYPE.STATUS);
    const categoryFilter = getFilterValue(filterTable, FILTER_TYPE.CATEGORY);
    const search = getFilterValue(filterTable, FILTER_TYPE.ALL);

    const payload = {
      ...start && { startDate: moment(start, 'DD/MM/YYYY').format('YYYY-MM-DD') },
      ...end && { endDate: moment(end, 'DD/MM/YYYY').format('YYYY-MM-DD') },
      status: statusFilter,
      filterByIdCategory: categoryFilter,
      resultPerpage: pageSize,
      page: Number(pageIndex) + 1,
      filterByType: postType.NEWS_PORTAL,
      ...search && { keyword: search },
      ...sortAccessor && { column: sortAccessor },
      ...sortDirection && { isAsc: sortDirection },
    };

    let retval = {
      data: [],
      total: 0,
    };
    this.setState({ isFetch: true });
    try {
      const { data, total } = await getPost(payload);
      const pageCount = Math.ceil(Number(total) / Number(pageSize));
      retval = { data, total };
    } catch (e) {
      notificationSystem.addNotification({
        title: 'Gagal mendapatkan data',
        message: catchError(e),
        level: 'error',
      });
    } finally {
      this.setState({
        tableData: retval.data,
        isFetch: false,
        tableMeta: {
          pageIndex,
          pageSize,
          total: retval.total,
          ...sortAccessor && { sortAccessor },
          ...sortDirection && { sortDirection },
        },
      });
    }
  }

  selectFilterHandle = async (val, type) => {
    this.updateCustomFilter(val, type);
  }

  updateCustomFilter = (val, type) => {
    const { filterTable, tableMeta } = this.state;
    const updatedFilterValue = getUpdatedFilterValue(filterTable, type, val);
    this.setState({ filterTable: updatedFilterValue }, () => this._onFetch({ ...tableMeta, pageIndex: 0 }));
  }

  handleSelectedChange = val => this.setState({ selectedRow: val });

  resetDateHandler() {
    this.selectFilterHandle({ start: '', end: '' }, FILTER_TYPE.DATE_RANGE);
  }

  callManageHandler() {
    const { router } = this.props;
    const { type } = this.state;
    router.push({
      pathname: `/web-portal/${type}/create`,
      state: { type },
    });
  }

  callEditDetailHandler(val) {
    const { router } = this.props;
    const { type } = this.state;
    router.push({
      pathname: `/web-portal/news-portal/edit/${val.id}`,
      state: { type },
    });
  }

  changeCustomStatus(val) {
    this.setState({
      statusSelect: val,
    });
  }

  changeCustomStatusButton(rows) {
    const { notificationSystem, router } = this.props;
    const { statusSelect, tableKey } = this.state;
    let status = '';
    if (statusSelect === 'Draft') {
      status = 'draft';
    } else if (statusSelect === 'Published') {
      status = 'publish';
    } else if (statusSelect === 'Trash') {
      status = 'trash';
    }

    const param = {
      id_post: JSON.stringify(rows),
      status,
    };

    if (status !== '' && rows.length !== 0) {
      updatePostBulk(param).then(() => {
        notificationSystem.addNotification({
          title: 'Berhasil',
          message: 'Berhasil update status post',
          level: 'success',
        });
        this.setState({
          selectedRow: [],
          tableKey: tableKey + 1,
          statusSelect: '',
        });
      }, (message) => {
        if (!message) {
          router.push('/auth/login');
        } else {
          notificationSystem.addNotification({
            title: 'Gagal update status',
            message: '',
            level: 'error',
          });
        }
      });
    }
  }

  render() {
    const {
      category, statusSelect, filterTable,
      isFetch, tableData, tableMeta, tableKey, selectedRow,
    } = this.state;

    const { start, end } = getFilterValue(filterTable, FILTER_TYPE.DATE_RANGE);
    const statusFilter = getFilterValue(filterTable, FILTER_TYPE.STATUS);
    const categoryFilter = getFilterValue(filterTable, FILTER_TYPE.CATEGORY);
    const search = getFilterValue(filterTable, FILTER_TYPE.ALL);

    const selectedIds = selectedRow.reduce((acc, curr) => ({ ...acc, [curr]: true }), {});

    return (
      <div>
        <section className="panel">
          <div className="panel-heading table-header">
            <div className="row">
              <div className="col-md-6 col-lg-7">
                <h4 style={{ margin: '7px' }} className="panel-title">News</h4>
              </div>
              <div className="col-md-6 col-lg-5" style={{ paddingRight: 0 }}>
                <div style={{ float: 'left', width: 'calc(100% - 75px)' }}>
                  <CalendarPick
                    type="range"
                    startDate={start}
                    endDate={end}
                    changeEvent={(startDate, endDate) => this.selectFilterHandle({ start: startDate, end: endDate }, FILTER_TYPE.DATE_RANGE)}
                  />
                </div>
                <div style={{ float: 'right', padding: '2px 5px' }}>
                  <Button
                    label="Reset"
                    clickEvent={() => this.resetDateHandler()}
                  />
                </div>
              </div>
            </div>
          </div>
          <div className="panel-heading table-header">
            <div className="row">
              <div className="col-md-3" style={{ paddingRight: 0 }}>
                <div style={{ float: 'left', width: 'calc(100% - 100px)' }}>
                  <Select
                    data={STATUS_LIST}
                    value={statusSelect}
                    placeholder="Bulk Action"
                    changeEvent={val => this.changeCustomStatus(val)}
                    disabled={!selectedRow.length}
                  />
                </div>
                <div style={{ float: 'left', padding: '2px 5px' }}>
                  <Button
                    label={`Apply ${selectedRow.length > 0 ? `(${selectedRow.length})` : ''}`}
                    clickEvent={() => this.changeCustomStatusButton(selectedRow)}
                    disabled={!selectedRow.length}
                  />
                </div>
              </div>
              <div className="col-md-4">
                <SwitchBox
                  dataset={SWITCH_LIST}
                  value={statusFilter}
                  changeEvent={value => this.selectFilterHandle(value, FILTER_TYPE.STATUS)}
                />
              </div>
              <div className="col-md-2">
                <Select
                  data={category}
                  value={categoryFilter}
                  changeEvent={value => this.selectFilterHandle(value, FILTER_TYPE.CATEGORY)}
                  classes="mb-reset"
                />
              </div>
              <div className="col-md-3">
                <InputText
                  placeholder="Search keyword"
                  changeEvent={val => this.selectFilterHandle(val, FILTER_TYPE.ALL)}
                  wait={500}
                />
              </div>
            </div>
          </div>
          <div className="panel-body">
            <Table
              key={`table-news-portal-${tableKey}`}
              columns={TABLE_META}
              data={tableData}
              totalData={tableMeta.total}
              pageIndex={tableMeta.pageIndex}
              rowLimit={tableMeta.pageSize}
              isLoading={isFetch}
              searchQuery={search}
              onRowClick={({ original }) => this.callEditDetailHandler(original)}
              fetchData={this._onFetch}
              onSelectedChange={this.handleSelectedChange}
              selectedIds={selectedIds}
            />
          </div>
        </section>
      </div>
    );
  }
}

export default CoreHOC(NewsPortal);
