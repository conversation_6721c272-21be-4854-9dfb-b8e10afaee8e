import React from 'react';
import InputCheckbox from '../../../../components/form/InputCheckbox';
import DateTimeColumn from '../../../../components/table/components/DateTimeColumn';

const TABLE_META = [
    {
        id: 'selection',
        Header: ({
            getToggleAllPageRowsSelectedProps, toggleAllPageRowsSelected,
        }) => (
            <div className="d-flex justify-content-center align-items-center">
                <InputCheckbox
                    {...getToggleAllPageRowsSelectedProps({
                        indeterminate: false,
                    })}
                    changeEvent={checked => toggleAllPageRowsSelected(checked)}
                />
            </div>
        ),
        Cell: ({ row: { getToggleRowSelectedProps, toggleRowSelected } }) => (
            <div className="d-flex justify-content-center align-items-center">
                <InputCheckbox
                    {...getToggleRowSelectedProps()}
                    changeEvent={checked => toggleRowSelected(checked)}
                />
            </div>
        ),
        unsortable: true,
        colMinWidth: 50,
    },
    {
        Header: 'Title',
        accessor: 'title',
    },
    {
        Header: 'Author',
        accessor: 'author_name',
    },
    {
        Header: 'Category',
        accessor: 'category_name',
    },
    {
        Header: 'Highlight',
        accessor: 'is_highlight',
    },
    {
        Header: 'SEQ',
        accessor: 'news_post_seq',
    },
    {
        Header: 'Date',
        accessor: 'createdate',
        cell: DateTimeColumn,
    },
    {
        Header: 'Id',
        accessor: 'id',
    },
];

const STATUS_LIST = [
    { label: 'Published', value: 'Published' },
    { label: 'Draft', value: 'Draft' },
    { label: 'Trash', value: 'Trash' },
];

const SWITCH_LIST = [
    { text: 'All', value: 'all' },
    { text: 'Published', value: 'published' },
    { text: 'Draft', value: 'draft' },
    { text: 'Trash', value: 'trash' },
];

const FILTER_TYPE = {
    DATE_RANGE: 'dateRange',
    STATUS: 'filterStatus',
    CATEGORY: 'filterCategory',
    ALL: 'all',
};

export {
    TABLE_META, STATUS_LIST, SWITCH_LIST, FILTER_TYPE,
};
