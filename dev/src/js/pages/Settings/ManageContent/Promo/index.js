import React, { Fragment, useRef } from 'react';
import { get } from 'lodash';

import CoreHOC from '../../../../core/CoreHOC';
import Table from '../../../../components/retina/table/Table';
import Select from '../../../../components/form/Select';
import DeleteConfirm from '../../../../components/modalpopup/DeleteConfirm';
import { usePromo } from './usePromo';
import { columns } from './config/table';
import SidePopup from '../../../../components/sidepopup/ContainerV2';
import SidePopupForm from './components/SidePopupForm';
import InputText from '../../../../components/form/InputText';

const Promo = (props) => {
    const sidePopupRef = useRef();
    const deleteConfirmRef = useRef();
    const {
        filterTable,
        handleFetch,
        categories,
        saveHandler,
        dataDetail,
        changeForm,
        changeFilter,
        statusOptions,
        onRowAction,
        deleteUnusedFile,
        handleDeletePromo,
        dataSource: { data, meta },
        isFetch,
    } = usePromo({
        ...props, sidePopupRef, deleteConfirmRef,
    });

    return (
        <Fragment>
            <section className="panel">
                <div className="panel-heading table-header">
                    <div className="row">
                        <div className="col-md-6">
                            <h4 className="panel-title" style={{ paddingTop: '8px' }}>Promo</h4>
                        </div>
                        <div className="col-md-6">
                            <div className="d-flex" style={{ gap: 8 }}>
                                <InputText
                                    placeholder="Search keyword"
                                    changeEvent={val => changeFilter('search', val)}
                                    wait={500}
                                />
                                <div style={{ width: 200 }}>
                                    <Select
                                        data={categories}
                                        value={filterTable.categoryId}
                                        changeEvent={value => changeFilter('categoryId', value)}
                                    />
                                </div>
                                <div style={{ width: 200 }}>
                                    <Select
                                        data={statusOptions}
                                        value={filterTable.status}
                                        changeEvent={value => changeFilter('status', value)}
                                    />
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div className="panel-body">
                    <Table
                        columns={columns(onRowAction)}
                        fetchData={handleFetch}
                        data={data}
                        totalData={meta.total}
                        pageIndex={meta.pageIndex}
                        rowLimit={meta.pageSize}
                        searchQuery={filterTable.search}
                        isLoading={isFetch}
                    />
                </div>
            </section>
            <DeleteConfirm
                title="Confirmation"
                confirmText="Yes, Delete Promo"
                cancelText="Cancel"
                confirmHandle={() => handleDeletePromo(dataDetail)}
                ref={deleteConfirmRef}
            >
                Are you sure to delete promo
                {' '}
                <b>{get(dataDetail, 'title', '')}</b>
                {' '}
                ?
            </DeleteConfirm>

            <SidePopup
                ref={sidePopupRef}
                width={700}
                saveHandle={saveHandler}
                onHide={() => deleteUnusedFile()}
                render={({ show, resetValidation, validateForm }) => {
                    if (show) {
                        return (
                            <SidePopupForm
                                data={dataDetail}
                                categories={categories.filter(x => x.id)}
                                validateInput={resetValidation}
                                validateForm={validateForm}
                                changeEvent={changeForm}
                            />
                        );
                    }
                    return null;
                }}
            />
        </Fragment>
    );
};

export default CoreHOC(Promo);
