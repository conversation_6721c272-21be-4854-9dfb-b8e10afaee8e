import React, { useCallback, useEffect, useState } from 'react';
import * as moment from 'moment';
import { catchError } from '../../../../utils/helper';
import * as PromoAPI from '../../../../data/setting/promo';
import { ACTION } from './config/table';
import { getNameFile, setFileUpload } from './utils';
import { deleteFileUpload } from '../../../../data/portal';

const defaultFormData = {
    slug: '',
    category_id: '',
    thumbnail: setFileUpload(),
    title: '',
    desc: '',
    meta_desc: '',
    content: '',
    snk: '',
    start_date: moment().format('DD/MM/YYYY'),
    end_date: moment().format('DD/MM/YYYY'),
    sequence: undefined,
    is_active: 1,
};

export const usePromo = (props) => {
    const {
        assignButtons,
        assignCalendar,
        showProgress,
        hideProgress,
        notificationSystem,
        sidePopupRef,
        deleteConfirmRef,
    } = props;

    const [filterTable, setFilterTable] = useState({
        status: '',
        categoryId: '',
        search: '',
    });
    const [isFetch, setIsFetch] = useState(true);
    const [dataSource, setDataSource] = useState({
        data: [],
        meta: {
            pageIndex: 1,
            pageSize: 10,
            total: 0,
        },
    });
    const [categories, setCategories] = useState([]);
    const [dataDetail, setDataDetail] = useState(defaultFormData);
    const statusOptions = [
        { id: '', name: 'All Status' },
        { id: 0, name: 'Inactive' },
        { id: 1, name: 'Active' },
        { id: 2, name: 'Expired' },
    ];

    const changeFilter = (key, value) => setFilterTable(prev => ({ ...prev, [key]: value }));

    const fetchCategories = async () => {
        try {
            const { data } = await PromoAPI.getListCategoriesPromo();
            const mapData = [{ id: '', name: 'All Category' }];
            (data || []).map((x) => {
                mapData.push({ id: x.id, name: x.title });
                return x;
            });
            setCategories(mapData);
        } catch (e) {
            notificationSystem.addNotification({
                title: 'Gagal mendapatkan data',
                message: catchError(e),
                level: 'error',
            });
        }
    };

    const handleFetch = async (state) => {
        const {
            pageIndex, pageSize, sortAccessor, sortDirection,
        } = state;

        const payload = {
            page: pageIndex + 1,
            limit: pageSize,
            ...(filterTable.categoryId && { category_id: filterTable.categoryId }),
            ...(filterTable.status !== '' && { status: filterTable.status }),
            ...(filterTable.search && { search: filterTable.search }),
            ...(sortAccessor && sortDirection && { sort: `${sortDirection.toLowerCase() === 'desc' ? '-' : ''}${sortAccessor}` }),
        };

        setIsFetch(true);
        const retval = { data: [], total: 0 };
        try {
            const { data, metadata } = await PromoAPI.getListPromo(payload);
            retval.data = data;
            retval.total = metadata.total_rows;
        } catch (e) {
            notificationSystem.addNotification({
                title: 'Gagal mendapatkan data',
                message: catchError(e),
                level: 'error',
            });
        } finally {
            setDataSource({
                data: retval.data,
                meta: {
                    pageIndex,
                    pageSize,
                    total: retval.total,
                    ...(sortAccessor && sortDirection && { sortAccessor, sortDirection }),
                },
            });
            setIsFetch(false);
        }
    };

    const refetch = useCallback((isResetPage = false) => {
        setTimeout(() => handleFetch({ ...dataSource.meta, ...isResetPage && { pageIndex: 0 } }), 500);
    }, [filterTable, dataSource.meta]);

    const openDetailPromo = async (data) => {
        try {
            showProgress();
            const { data: resData } = await PromoAPI.getDetailPromo(data.id);
            setDataDetail({
                    ...resData,
                    thumbnailOrigin: resData.thumbnail,
                    thumbnail: setFileUpload(resData.thumbnail),
                    start_date: moment(resData.start_date).format('DD/MM/YYYY'),
                    end_date: moment(resData.end_date).format('DD/MM/YYYY'),
            }, sidePopupRef.current.showPopup());
        } catch (err) {
            notificationSystem.addNotification({
                title: 'Gagal mendapatkan data',
                message: catchError(err),
                level: 'error',
            });
        } finally {
            hideProgress();
        }
    };

    const handleDeletePromo = async (data) => {
        try {
            showProgress();
            const { message } = await PromoAPI.deletePromo(data.id);
            notificationSystem.addNotification({
                title: 'Success',
                message,
                level: 'success',
            });
            setDataDetail(defaultFormData);
            refetch();
        } catch (err) {
            notificationSystem.addNotification({
                title: 'Gagal',
                message: catchError(err),
                level: 'error',
            });
        } finally {
            deleteConfirmRef.current.hidePopup();
            hideProgress();
        }
    };

    const onRowAction = (act, data) => {
        switch (act) {
            case ACTION.EDIT:
                openDetailPromo(data);
                break;
            case ACTION.DELETE:
                deleteConfirmRef.current.showPopup();
                setDataDetail(data);
                break;
            default:
                break;
        }
    };

    const saveHandler = async () => {
        try {
            const savePromo = dataDetail.id ? PromoAPI.updatePromo : PromoAPI.createPromo;
            showProgress();
            const payload = {
                ...dataDetail,
                start_date: moment(dataDetail.start_date, 'DD/MM/YYYY').format('YYYY-MM-DD'),
                end_date: moment(dataDetail.end_date, 'DD/MM/YYYY').format('YYYY-MM-DD'),
                thumbnail: getNameFile(dataDetail.thumbnail.fullpath),
                ...(dataDetail.content && { content: btoa(unescape(encodeURIComponent(dataDetail.content))) }),
                ...(dataDetail.snk && { snk: btoa(unescape(encodeURIComponent(dataDetail.snk))) }),
                ...(dataDetail.meta_desc && { meta_desc: btoa(unescape(encodeURIComponent(dataDetail.meta_desc))) }),
            };
            const { message } = await savePromo(payload);
            setDataDetail(prev => ({ ...prev, isSaved: true }));
            notificationSystem.addNotification({
                title: 'Success',
                message,
                level: 'success',
            });
            sidePopupRef.current.hidePopup();
            refetch();
        } catch (err) {
            notificationSystem.addNotification({
                title: 'Gagal',
                message: catchError(err),
                level: 'error',
            });
            sidePopupRef.current.failedCallback();
        } finally {
            hideProgress();
        }
    };

    const openAddPromo = () => setDataDetail(defaultFormData, sidePopupRef.current.showPopup());

    const deleteUnusedFile = () => new Promise((resolve) => {
        const {
            thumbnailOrigin, id, thumbnail: { fullpath }, isSaved,
        } = dataDetail;
        if (!isSaved && fullpath && (!id || (thumbnailOrigin && thumbnailOrigin !== fullpath))) {
            const url = new URL(fullpath);
            deleteFileUpload({ path: url.pathname }).finally(resolve());
        }
        resolve();
    });

    const changeForm = async (key, val) => {
        if (key === 'thumbnail') {
            await deleteUnusedFile();
        }
        setDataDetail(prev => ({ ...prev, [key]: val }));
    };

    useEffect(() => {
        assignButtons([
            {
                type: 'primary',
                content: (
                    <div className="d-flex align-items-center">
                        Add Promo
                    </div>
                ),
                action: openAddPromo,
            },
        ]);
        assignCalendar(null, null, null);
        fetchCategories();
    }, []);

    useEffect(() => refetch(true), [filterTable]);

    return {
        filterTable,
        handleFetch,
        dataDetail,
        setDataDetail,
        categories,
        saveHandler,
        changeFilter,
        statusOptions,
        onRowAction,
        dataSource,
        deleteUnusedFile,
        changeForm,
        handleDeletePromo,
        isFetch,
    };
};
