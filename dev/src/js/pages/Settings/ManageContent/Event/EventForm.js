import React, { Component } from 'react';
import PropTypes from 'prop-types';
import moment from 'moment';
import update from 'immutability-helper';
import { connect } from 'react-redux';
import { FieldFeedback, FieldFeedbacks } from 'react-form-with-constraints';

import CoreHOC from '../../../../core/CoreHOC';

import RichTextEditor from '../../../../components/form/RichTextEditor';
import InputText from '../../../../components/form/InputText';
import CalendarPick from '../../../../components/form/CalendarPick';
import FileUpload from '../../../../components/form/FileUpload';
import InputCheckbox from '../../../../components/form/InputCheckboxDark';
import TimePicker from '../../../../components/form/TimePicker';

import FormValidation from '../../../../components/form/FormValidation';

import { getEventDetail, saveEvent } from '../../../../data/setting/event';

import { fetchInfoUser } from '../../../../actions/userActions';
import { catchError } from '../../../../utils/helper';
import { getFormattedPayload, getDetail, setFileUpload } from './utility/data';

import { GENERATE_ERROR_MESSAGE } from './config';
import { deleteEvent } from '../../../../data/setting/event/api';
import DeleteConfirm from '../../../../components/modalpopup/DeleteConfirm';

class EventPost extends Component {
    footerButtons = [
        {
            id: '1',
            type: null,
            content: (
                <span>
                    Batal
                </span>
            ),
            action: () => this.gotoBasePath(),
            isDisabled: false,
        },
        {
            id: '2',
            type: 'primary',
            content: (
                <span>
                    Save
                </span>
            ),
            action: () => this.saveActionHandler(),
            isDisabled: false,
        },
    ];

    static propTypes = {
        params: PropTypes.shape({
            id: PropTypes.string,
        }),
        showProgress: PropTypes.func.isRequired,
        hideProgress: PropTypes.func.isRequired,
        router: PropTypes.shape({
            push: PropTypes.func,
        }).isRequired,
        notificationSystem: PropTypes.shape({
            addNotification: PropTypes.func,
        }),
        assignButtons: PropTypes.func,
        assignCalendar: PropTypes.func,
    }

    static defaultProps = {
        assignButtons: () => {
        },
        assignCalendar: () => {
        },
        params: {
            id: '',
        },
        notificationSystem: ({
            addNotification: () => {
            },
        }),
    }

    constructor(props) {
        super(props);
        this.state = {
            namePage: 'Add Event',
            form: {
                title: '',
                content: '',
                startDate: moment().format('DD/MM/YYYY'),
                endDate: moment().format('DD/MM/YYYY'),
                startTime: '15:00',
                endTime: '17:00',
                thumbnail: '',
                banner: '',
                social: '',
                registration: 'https://bit.ly/webinarmajoo',
                tiketPrice: 0,
                venue: 'Online',
                speaker: '',
                speakerTitle: '',
                city: 'Online',
                showPage: false,
            },
        };
    }

    componentDidMount = async () => {
        const { params, assignButtons, assignCalendar } = this.props;
        assignButtons([]);
        assignCalendar(null, null, null);
        let namePage = 'Create Event';

        if (params.id) {
            namePage = 'Edit Event';
        }

        this.setState({
            namePage,
        }, () => {
            if (params.id) {
                this.getData();
            }
        });
    }

    saveActionHandler = async () => {
        const { params: { id } } = this.props;
        const { form } = this.state;
        const isFormValid = await this.form.validateForm();

        if (isFormValid) {
            const payload = getFormattedPayload(form);
            payload.id = id;
            this.doSave(payload);
        }
    }

    doSave = async (payload) => {
        const {
            showProgress, hideProgress, notificationSystem, router,
        } = this.props;
        showProgress();
        try {
            await saveEvent(payload);
            notificationSystem.addNotification({
                title: 'Berhasil',
                message: 'Berhasil menyimpan Event',
                level: 'success',
            });
            setTimeout(() => {
                router.push({
                    pathname: '/web-portal/event/',
                });
            }, 1000);
        } catch (err) {
            notificationSystem.addNotification({
                title: 'Terjadi Kesalahan',
                message: catchError(err),
                level: 'error',
            });
        } finally {
            hideProgress();
        }
    }

    deleteEvent = async () => {
        const {
            showProgress, hideProgress, notificationSystem, router, params: { id },
        } = this.props;
        showProgress();
        try {
            await deleteEvent(id);
            notificationSystem.addNotification({
                title: 'Berhasil',
                message: 'Berhasil menghapus Event',
                level: 'success',
            });
            setTimeout(() => {
                router.push({
                    pathname: '/web-portal/event/',
                });
            }, 1000);
        } catch (err) {
            notificationSystem.addNotification({
                title: 'Terjadi Kesalahan',
                message: catchError(err),
                level: 'error',
            });
        } finally {
            hideProgress();
            this.deleteConfirmRef.hidePopup();
        }
    }

    gotoBasePath = () => {
        const { router } = this.props;
        router.push({
            pathname: '/web-portal/event/',
        });
    }

    getData = async () => {
        const {
            hideProgress, params: { id }, notificationSystem,
        } = this.props;

        try {
            const res = await getEventDetail({ id });
            const { data } = res;
            const form = getDetail(data);
            this.setState({ form });
        } catch (err) {
            notificationSystem.addNotification({
                title: 'Terjadi Kesalahan',
                message: catchError(err),
                level: 'error',
            });
        }
        hideProgress();
    }

    notifyUploadFailed = (err) => {
        const { notificationSystem } = this.props;
        notificationSystem.addNotification({
            title: 'Terjadi Kesalahan',
            message: catchError(err),
            level: 'error',
        });
    }

    onInputChangeValidate = ({ target }) => {
        this.form.validateInput(target);

        if (this.inputTimeout) {
            clearTimeout(this.inputTimeout);
        }
    }

    validateRefHidden = async (target) => {
        await this.onInputChangeValidate({ target });
    }

    inputChangeHandler = async (key, val, e) => {
        const { form } = this.state;

        if (e) {
            await this.onInputChangeValidate(e);
        }

        const newForm = update(form, {
            [key]: {
                $set: (val.path && !val.uid) ? setFileUpload(val.path) : val,
            },
        });

        this.setState({
            form: newForm,
        }, () => {
            this.validateRefHidden(this.thumbnailImageRef);
            this.validateRefHidden(this.bannerImageRef);
            this.validateRefHidden(this.socialImageRef);
        });
    }

    render() {
        const { namePage, form } = this.state;
        const { params: { id } } = this.props;
        const {
            title, content, startDate, endDate, thumbnail, banner, social,
            registration, tiketPrice, venue, speaker, speakerTitle,
            city, showPage, startTime, endTime,
        } = form;

        return (
            <FormValidation ref={(c) => {
                this.form = c;
            }}
            >
                <div className="row">
                    <div className="col-md-12">
                        <h4 className="side-popup-title" style={{ marginBottom: 18 }}>
                            {namePage}
                        </h4>
                        <section className="panel" style={{ marginTop: 10 }}>
                            <div className="panel-body">
                                <div className="row mb-lg">
                                    <div className="col-md-12">
                                        <label className="control-label">
                                            Event Title
                                            {' '}
                                            <span className="text-red">
                                                *
                                            </span>
                                        </label>
                                        <InputText
                                            placeholder="Event Title"
                                            name="eventTitle"
                                            value={title}
                                            changeEvent={(val, e) => this.inputChangeHandler('title', val, e)}
                                        />
                                        <FieldFeedbacks for="eventTitle">
                                            <FieldFeedback when={val => val === ''}>
                                                {GENERATE_ERROR_MESSAGE('Event Title', 'valueMissing')}
                                            </FieldFeedback>
                                        </FieldFeedbacks>
                                    </div>
                                </div>
                                <div className="row mb-lg">
                                    <div className="col-md-6">
                                        <div className="row mb-lg">
                                            <div className="col-md-12">
                                                <label className="control-label">
                                                    Speaker
                                                    {' '}
                                                    <span className="text-red">
                                                        *
                                                    </span>
                                                </label>
                                                <InputText
                                                    placeholder="Speaker"
                                                    name="speaker"
                                                    value={speaker}
                                                    changeEvent={(val, e) => this.inputChangeHandler('speaker', val, e)}
                                                />
                                                <FieldFeedbacks for="speaker">
                                                    <FieldFeedback when={val => val === ''}>
                                                        {GENERATE_ERROR_MESSAGE('Speaker', 'valueMissing')}
                                                    </FieldFeedback>
                                                </FieldFeedbacks>
                                            </div>
                                        </div>
                                    </div>
                                    <div className="col-md-6">
                                        <div className="row mb-lg">
                                            <div className="col-md-12">
                                                <label className="control-label">
                                                    Speaker Title
                                                    {' '}
                                                    <span className="text-red">
                                                        *
                                                    </span>
                                                </label>
                                                <InputText
                                                    placeholder="Speaker Title"
                                                    name="speakerTitle"
                                                    value={speakerTitle}
                                                    changeEvent={(val, e) => this.inputChangeHandler('speakerTitle', val, e)}
                                                />
                                                <FieldFeedbacks for="speakerTitle">
                                                    <FieldFeedback when={val => val === ''}>
                                                        {GENERATE_ERROR_MESSAGE('Speaker Title', 'valueMissing')}
                                                    </FieldFeedback>
                                                </FieldFeedbacks>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div className="row mb-lg">
                                    <div className="col-md-12">
                                        <InputText
                                            label="Venue"
                                            name="venue"
                                            placeholder="Venue"
                                            changeEvent={(val, e) => this.inputChangeHandler('venue', val, e)}
                                            value={venue}
                                        />
                                    </div>
                                </div>
                                <div className="row mb-lg">
                                    <div className="col-md-6">
                                        <InputText
                                            label="Registration Site"
                                            placeholder="Registration Site"
                                            name="registrationSite"
                                            value={registration}
                                            changeEvent={(val, e) => this.inputChangeHandler('registration', val, e)}
                                        />
                                    </div>
                                    <div className="col-md-6">
                                        <InputText
                                            label="Tiket Price"
                                            name="tiketPrice"
                                            value={tiketPrice}
                                            changeEvent={(val, e) => this.inputChangeHandler('tiketPrice', val, e)}
                                        />
                                    </div>
                                </div>
                                <div className="row mb-lg">
                                    <div className="col-md-12">
                                        <div className="row mb-lg">
                                            <div className="col-md-3">
                                                <span className="control-label">Event Start Date</span>
                                                <CalendarPick
                                                    className="lg"
                                                    popoverAlign="left"
                                                    type="picker"
                                                    name="startDate"
                                                    defaultDate={startDate}
                                                    changeEvent={(val, e) => this.inputChangeHandler('startDate', val, e)}
                                                />
                                            </div>
                                            <div className="col-md-3">
                                                <span className="control-label">Event Start</span>
                                                <TimePicker
                                                    className="lg"
                                                    name="startTime"
                                                    changeEvent={(val, e) => this.inputChangeHandler('startTime', moment(val).format('HH:mm'), e)}
                                                    value={startTime}
                                                    zeroTo24
                                                />
                                            </div>
                                            <div className="col-md-3">
                                                <span className="control-label">Event End Date</span>
                                                <CalendarPick
                                                    className="lg"
                                                    popoverAlign="left"
                                                    type="picker"
                                                    name="endDate"
                                                    defaultDate={endDate}
                                                    changeEvent={(val, e) => this.inputChangeHandler('endDate', val, e)}
                                                />
                                            </div>
                                            <div className="col-md-3">
                                                <span className="control-label">Event End</span>
                                                <TimePicker
                                                    className="lg"
                                                    name="endTime"
                                                    changeEvent={(val, e) => this.inputChangeHandler('endTime', moment(val).format('HH:mm'), e)}
                                                    value={endTime}
                                                    zeroTo24
                                                />
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div className="row mb-lg">
                                    <div className="col-md-4">
                                        <label className="control-label">
                                            Thumbnail Event 540 x 302
                                            {' '}
                                            <span className="text-red">
                                                *
                                            </span>
                                        </label>
                                        <FileUpload
                                            value={thumbnail}
                                            name="thumbnailImage"
                                            changeEvent={(val, e) => this.inputChangeHandler('thumbnail', val, e)}
                                            acceptUpload="image/*"
                                            isLocal
                                        />
                                        <div className="text-red">
                                            <input
                                                className="hidden"
                                                ref={(c) => {
                                                    this.thumbnailImageRef = c;
                                                }}
                                                name="thumbnailImageRef"
                                                type="text"
                                                value={thumbnail}
                                                onChange={() => {
                                                }}
                                                required
                                            />
                                            <FieldFeedbacks for="thumbnailImageRef">
                                                <FieldFeedback when={val => (val === '')}>
                                                    {GENERATE_ERROR_MESSAGE('Thumbnail Event 540 x 302', 'valueMissing')}
                                                </FieldFeedback>
                                            </FieldFeedbacks>
                                        </div>
                                    </div>
                                    <div className="col-md-4">
                                        <label className="control-label">
                                            Banner Event 800 x 800
                                            {' '}
                                            <span className="text-red">
                                                *
                                            </span>
                                        </label>
                                        <FileUpload
                                            value={banner}
                                            name="bannerImage"
                                            changeEvent={(val, e) => this.inputChangeHandler('banner', val, e)}
                                            acceptUpload="image/*"
                                            isLocal
                                        />
                                        <div className="text-red">
                                            <input
                                                className="hidden"
                                                ref={(c) => {
                                                    this.bannerImageRef = c;
                                                }}
                                                name="bannerImageRef"
                                                type="text"
                                                value={banner}
                                                onChange={() => { }}
                                                required
                                            />
                                            <FieldFeedbacks for="bannerImageRef">
                                                <FieldFeedback when={val => val === ''}>
                                                    {GENERATE_ERROR_MESSAGE('Banner Event 800 x 800', 'valueMissing')}
                                                </FieldFeedback>
                                            </FieldFeedbacks>
                                        </div>
                                    </div>
                                    <div className="col-md-4">
                                        <label className="control-label">
                                            Social Event
                                            {' '}
                                            <span className="text-red">
                                                *
                                            </span>
                                        </label>
                                        <FileUpload
                                            value={social}
                                            name="socialImage"
                                            changeEvent={(val, e) => this.inputChangeHandler('social', val, e)}
                                            onError={this.notifyUploadFailed}
                                            acceptUpload="image/*"
                                            isLocal
                                        />
                                        <div className="text-red">
                                            <input
                                                className="hidden"
                                                ref={(c) => {
                                                    this.socialImageRef = c;
                                                }}
                                                name="socialImageRef"
                                                type="text"
                                                value={social}
                                                onChange={() => { }}
                                                required
                                            />
                                            <FieldFeedbacks for="socialImageRef">
                                                <FieldFeedback when={val => (val === '')}>
                                                    {GENERATE_ERROR_MESSAGE('Social Event', 'valueMissing')}
                                                </FieldFeedback>
                                            </FieldFeedbacks>
                                        </div>
                                    </div>
                                </div>
                                <div className="row mb-lg">
                                    <div className="col-md-12">
                                        <label className="control-label">
                                            Content
                                            {' '}
                                            <span className="text-red">
                                                *
                                            </span>
                                        </label>
                                        <RichTextEditor
                                            name="content"
                                            changeEvent={(val, e) => this.inputChangeHandler('content', val, e)}
                                            value={content}
                                        />
                                        <FieldFeedbacks for="content">
                                            <FieldFeedback when={val => val === ''}>
                                                {GENERATE_ERROR_MESSAGE('Content', 'valueMissing')}
                                            </FieldFeedback>
                                        </FieldFeedbacks>
                                    </div>
                                </div>
                                <div className="row mb-lg">
                                    <div className="col-md-6">
                                        <InputCheckbox
                                            id="showPage"
                                            name="showPage"
                                            checked={showPage}
                                            changeEvent={(val, e) => this.inputChangeHandler('showPage', val, e)}
                                        >
                                            Show in Page
                                        </InputCheckbox>
                                    </div>
                                </div>
                                <div className="row mb-lg">
                                    <div className="col-sm-2">
                                        {id && (
                                            <button
                                                type="button"
                                                className="btn btn-remove"
                                                onClick={() => this.deleteConfirmRef.showPopup()}
                                            >
                                                <i className="fa fa-trash" />
                                            </button>
                                        )}
                                    </div>
                                    <div className="col-sm-10 text-right">
                                        {
                                            this.footerButtons.map(x => (
                                                <button
                                                    key={x.id}
                                                    type="button"
                                                    className={
                                                        `btn ${x.type ? `btn-${x.type}` : ''}`
                                                    }
                                                    onClick={
                                                        !x.isDisabled ? x.action : () => {
                                                        }
                                                    }
                                                    disabled={x.isDisabled}
                                                >
                                                    {x.content}
                                                </button>
                                            ))
                                        }
                                    </div>
                                </div>
                            </div>
                        </section>
                    </div>
                </div>
                <DeleteConfirm
                    title="Confirmation"
                    confirmText="Yes, Delete Event"
                    cancelText="Cancel"
                    confirmHandle={this.deleteEvent}
                    ref={(c) => { this.deleteConfirmRef = c; }}
                >
                    Are you sure to delete event?
                </DeleteConfirm>
            </FormValidation>
        );
    }
}

const mapStateToProps = state => ({
    idUser: state.user.profile.id,
});

const mapDispatchToProps = dispatch => ({
    fetchProfile: (token) => {
        dispatch(fetchInfoUser(token));
    },
});

export default CoreHOC(connect(mapStateToProps, mapDispatchToProps)(EventPost));
