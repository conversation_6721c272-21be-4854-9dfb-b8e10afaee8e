import React, { Component } from 'react';
import PropTypes from 'prop-types';
import CoreHOC from '../../../../core/CoreHOC';
import Table from '../../../../components/retina/table/Table';
import InputText from '../../../../components/form/InputText';
import Select from '../../../../components/form/Select';
import ModalPopup from '../../../../components/modalpopup/v.2/Container';
import { getPageBannerDataListV01 } from '../../../../data/setting/pageBanner';
import { catchError, formatDate } from '../../../../utils/helper';
import { buildColumns } from './resource/meta';
import { statusFilterList } from './resource/config';

class PageBanner extends Component {
  static propTypes = {
    calendar: PropTypes.shape({ start: PropTypes.string, end: PropTypes.string }),
    assignCalendar: PropTypes.func,
    assignButtons: PropTypes.func,
    router: PropTypes.shape({ push: PropTypes.func }),
    notificationSystem: PropTypes.shape({ addNotification: PropTypes.func }),
    assignRangeDate: PropTypes.func,
  }

  static defaultProps = {
    calendar: { start: '', end: '' },
    assignCalendar: () => { },
    assignButtons: () => { },
    notificationSystem: { addNotification: () => { } },
    assignRangeDate: () => { },
    router: { push: null },
  };

  constructor(props) {
    super(props);
    const { calendar } = this.props;

    this.state = {
      dateFilter: calendar,
      statusFilter: '',
      searchQuery: '',
      activePageData: [],
      tableData: [],
      tableMeta: {
        pageIndex: 1,
        limit: 10,
        totalData: 0,
      },
      tableLoading: false,
    };

    this.columns = buildColumns(this.handleActiveBanner, this.handleDetailBanner);
  }

  componentDidMount() {
    const { assignCalendar, assignButtons, assignRangeDate } = this.props;
    assignCalendar(null, null, (startDate, endDate) => {
      this.changeDateHandler(startDate, endDate);
    });
    assignButtons([{
      type: 'primary',
      content: <span>
        <i className="fa fa-plus" />
        {' '}
        Add New Banner
               </span>,
      action: this.handleNewBanner,
    },
    ]);
    assignRangeDate([]);

    this._onFetchs({ pageIndex: 0 });
  }

  componentDidUpdate(prevProps, prevState) {
    const {
      dateFilter, statusFilter, searchQuery, tableMeta,
    } = this.state;
    const filtersChanged = (prevState.dateFilter
      && (prevState.dateFilter.start !== dateFilter.start
      || prevState.dateFilter.end !== dateFilter.end))
      || prevState.statusFilter !== statusFilter
      || prevState.searchQuery !== searchQuery;

    if (filtersChanged) {
      this._onFetchs({ pageIndex: 0, pageSize: tableMeta.limit });
    }
  }

  componentWillUnmount() {
    const { assignButtons, assignCalendar } = this.props;
    assignButtons([]);
    assignCalendar(null, null, null);
  }

  changeDateHandler = (startDate, endDate) => {
    const { assignCalendar } = this.props;
    assignCalendar(startDate, endDate);
    this.setState({ dateFilter: { start: startDate, end: endDate } });
  }

  handleSearchChange = (value) => {
    this.setState({ searchQuery: value });
  }

  _onFetchs = async (params) => {
    const { notificationSystem } = this.props;
    const {
      pageIndex, pageSize, sortAccessor, sortDirection,
    } = params;
    const {
      dateFilter: { start: startDate, end: endDate }, statusFilter, searchQuery, tableMeta,
    } = this.state;

    this.setState({ tableLoading: true });

    const status = statusFilter === 'All Status' ? '' : statusFilter;

    const payload = {
      start_date: formatDate(startDate, 'yyyy-mm-dd'),
      end_date: formatDate(endDate, 'yyyy-mm-dd'),
      ...status !== '' && { status },
      limit: pageSize || 10,
      page: pageIndex + 1,
      ...searchQuery && { search: searchQuery },
    };

    if (sortAccessor) {
      payload.sort = sortDirection;
      payload.sort_by = sortAccessor;
    }

    try {
      const response = await getPageBannerDataListV01(payload);
      this.setState({
        tableData: response.data,
        tableMeta: {
          pageIndex,
          limit: pageSize || 10,
          totalData: response.meta.total,
        },
      });
    } catch (err) {
      notificationSystem.addNotification({
        title: 'Get data failed', message: catchError(err), level: 'error',
      });
      this.setState({ tableData: [], tableMeta: { ...tableMeta, totalData: 0 } });
    } finally {
      this.setState({ tableLoading: false });
    }
  }

  handleNewBanner = () => {
    const { router } = this.props;
    router.push('/manage-content/page-banner/add-banner');
  }

  handleActiveBanner = (activePageData) => {
    this.setState({ activePageData }, () => {
      this.activePageBanner.showPopup();
    });
  }

  closeActivePageModal = () => {
    this.setState({ activePageData: [] }, () => {
      this.activePageBanner.hidePopup();
    });
  }

  handleDetailBanner = ({ id }) => {
    const { router } = this.props;
    router.push(`/manage-content/page-banner/edit-banner/${id}`);
  }

  render() {
    const {
      statusFilter, activePageData, tableData, tableMeta, tableLoading,
    } = this.state;

    return (
      <div>
        <section className="panel">
          <div className="panel-body">
            <div className="panel-heading table-header">
              <div className="row" style={{ display: 'flex', alignItems: 'center' }}>
                <div className="col-xs-12 col-sm-6">
                  <h4 className="panel-title">Page Banner</h4>
                </div>
                <div className="col-xs-12 col-sm-6">
                  <div className="row" style={{ display: 'flex', justifyContent: 'flex-end', alignItems: 'center' }}>
                    <div className="col-xs-12 col-sm-5">
                      <Select
                        data={statusFilterList}
                        value={statusFilter}
                        changeEvent={val => this.setState({ statusFilter: val })}
                      />
                    </div>
                    <div className="col-xs-12 col-sm-5">
                      <InputText
                        classes="filter"
                        placeholder="Cari..."
                        changeEvent={this.handleSearchChange}
                        wait={500}
                      />
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div className="panel-body">
              <Table
                columns={this.columns}
                data={tableData}
                fetchData={this._onFetchs}
                isLoading={tableLoading}
                pageIndex={tableMeta.pageIndex}
                rowLimit={tableMeta.limit}
                totalData={tableMeta.totalData}
              />
            </div>
          </div>
        </section>
        <ModalPopup
          ref={(c) => { this.activePageBanner = c; }}
          width={480}
          cancelText="Close"
          cancelHandle={this.closeActivePageModal}
        >
          <div
            className="modal-popup-header"
            style={{
              background: 'none',
              borderBottom: '1px solid #005252',
              marginBottom: 25,
              paddingTop: 16,
              paddingBottom: 16,
            }}
          >
            Active Page
          </div>
          <ul>
            {
              activePageData.map(activePage => <li key={`activePage-${activePage}`}>{activePage}</li>)
            }
          </ul>
        </ModalPopup>
      </div>
    );
  }
}

export default CoreHOC(PageBanner);
