import React, { useEffect, useState } from 'react';
import * as moment from 'moment';
import { getFilterValue, getUpdatedFilterValue } from '../../../../utils/table.util';
import * as apiSupplies from '../../../../data/setting/suppliesFormPortal';
import { catchError } from '../../../../utils/helper';
import { printExcel } from '../../../../data';

export const useSuppliesFormPortal = (props) => {
    const {
        calendar,
        assignButtons,
        assignCalendar,
        showProgress,
        hideProgress,
        notificationSystem,
        refDetail,
    } = props;
    const [filterTable, setFilterTable] = useState([
        { id: 'date', value: calendar },
    ]);
    const [dataSource, setDataSource] = useState({
        data: [],
        meta: {
            pageIndex: 0,
            pageSize: 10,
            total: 0,
        },
    });
    const [dataDetail, setDataDetail] = useState(null);
    const [isFetch, setIsFetch] = useState(true);

    const updateCustomFilter = (val, type) => {
        const updatedFilterValue = getUpdatedFilterValue(filterTable, type, val);

        setFilterTable(updatedFilterValue);
    };

    const changeDateHandle = async (start, end) => {
        assignCalendar(start, end);

        const calendar = { start, end };
        updateCustomFilter(calendar, 'date');
    };

    const handleFetch = async (state) => {
        const { pageIndex, pageSize } = state;

        const filterDate = getFilterValue(filterTable, 'date');

        const payload = {
            page: pageIndex + 1,
            limit: pageSize,
            start: moment(filterDate.start, 'DD-MM-YYYY').format('YYYY-MM-DD'),
            end: moment(filterDate.end, 'DD-MM-YYYY').format('YYYY-MM-DD'),
        };

        setIsFetch(true);
        let retval = { data: [], total: 0 };
        try {
            const { data, metadata } = await apiSupplies.getListSupplies(payload);
            const { total_rows: total } = metadata;
            retval = { data, total };
        } catch (e) {
            notificationSystem.addNotification({
                title: 'Gagal mendapatkan data',
                message: catchError(e),
                level: 'error',
            });
        } finally {
            setIsFetch(false);
            setDataSource({
                data: retval.data,
                meta: {
                    pageIndex,
                    pageSize,
                    total: retval.total,
                },
            });
        }

        return retval;
    };

    const handleView = async (values) => {
        showProgress();

        try {
            const { data } = await apiSupplies.getDetailSupplies(values.original.id);
            setDataDetail(data);
        } catch (err) {
            notificationSystem.addNotification({
                title: 'Gagal mendapatkan data',
                message: catchError(err),
                level: 'error',
            });
        } finally {
            hideProgress();
        }
    };

    const handleExport = async () => {
        showProgress();

        try {
            const [filterDate] = filterTable;

            const payloadList = {
                page: 1,
                limit: dataSource.meta.total,
                start: moment(filterDate.value.start, 'DD-MM-YYYY').format('YYYY-MM-DD'),
                end: moment(filterDate.value.end, 'DD-MM-YYYY').format('YYYY-MM-DD'),
            };

            const { data: dataList } = await apiSupplies.getListSupplies(payloadList);

            const template = 'laporan_supplies_form_portal.xlsx';
            const outputName = 'laporan_supplies_form_portal';
            const alias = 'x';

            const variable = {
                mulai: moment(filterDate.value.start, 'DD-MM-YYYY').format('DD/MM/YYYY'),
                akir: moment(filterDate.value.end, 'DD-MM-YYYY').format('DD/MM/YYYY'),
                dateNow: moment().format('DD MMM YYYY'),
                list: dataList,
            };

            const payload = {
                param: [{
                    variable,
                    template,
                    output_name: outputName,
                    data: dataList,
                    alias,
                }],
            };

            const res = await printExcel(payload);
            const { data } = res;
            window.location = data;
        } catch (err) {
            notificationSystem.addNotification({
                title: 'Gagal mendapatkan data',
                message: catchError(err),
                level: 'error',
            });
        } finally {
            hideProgress();
        }
    };

    const handleResetDetail = () => setDataDetail(null);

    useEffect(() => {
        assignButtons([
            {
                type: 'primary',
                content: (
                    <span style={{ display: 'flex', alignItems: 'center' }}>
                        <i className="fa fa-file-download-white" />
                        {' '}
                        Download Data
                    </span>
                ),
                action: () => handleExport(),
            },
        ]);
        assignCalendar(null, null, (startDate, endDate) => {
            changeDateHandle(startDate, endDate);
        });
    }, []);

    useEffect(() => {
        assignButtons([
            {
                type: 'primary',
                content: (
                    <span style={{ display: 'flex', alignItems: 'center' }}>
                        <i className="fa fa-file-download-white" />
                        {' '}
                        Download Data
                    </span>
                ),
                action: () => handleExport(),
            },
        ]);
    }, [dataSource.meta.total]);

    useEffect(() => {
        if (dataDetail) refDetail.current.showPopup();
    }, [dataDetail]);

    useEffect(() => {
        handleFetch({ ...dataSource.meta, pageIndex: 0 });
    }, [filterTable]);

    return {
        filterTable,
        handleFetch,
        handleView,
        dataDetail,
        handleResetDetail,
        dataSource,
        isFetch,
    };
};
