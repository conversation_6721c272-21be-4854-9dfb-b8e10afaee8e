import React, { Fragment, useRef } from 'react';

import CoreHOC from '../../../../core/CoreHOC';
import SidePopup from '../../../../components/sidepopup/ContainerV2';
import Table from '../../../../components/retina/table/Table';

import { useSuppliesFormPortal } from './useSuppliesFormPortal';
import { columns } from './config/table';
import Detail from './Detail';

const SuppliesFormPortal = (props) => {
    const refDetail = useRef();
    const {
        handleFetch,
        handleView,
        dataDetail,
        handleResetDetail,
        dataSource: { data, meta },
        isFetch,
    } = useSuppliesFormPortal({ ...props, refDetail });

    return (
        <Fragment>
            <section className="panel">
                <div className="panel-heading table-header">
                    <div className="row">
                        <div className="col-md-6">
                            <h4 className="panel-title" style={{ paddingTop: '8px' }}>Supplies Form Portal</h4>
                        </div>
                    </div>
                </div>
                <div className="panel-body">
                    <Table
                        columns={columns}
                        data={data}
                        totalData={meta.total}
                        pageIndex={meta.pageIndex}
                        rowLimit={meta.pageSize}
                        isLoading={isFetch}
                        onRowClick={handleView}
                        fetchData={handleFetch}
                    />
                </div>
            </section>

            <SidePopup
                ref={refDetail}
                width={600}
                onHide={handleResetDetail}
                render={({ show }) => {
                    if (show) return <Detail data={dataDetail} />;

                    return (null);
                }}
            />
        </Fragment>
    );
};

export default CoreHOC(SuppliesFormPortal);
