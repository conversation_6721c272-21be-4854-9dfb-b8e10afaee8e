import React from 'react';
import DateTimeColumn from '../../../../components/table/components/DateTimeColumn';

const USER_NEED_POS = ['', 'Ya, Butuh', 'Ragu-Ragu', 'Tidak / Belum Butuh'];
const tableColumn = [
    {
        Header: 'Submit Date',
        accessor: 'create_date',
        unsortable: true,
        Cell: DateTimeColumn,
    },
    {
        Header: 'Campaign',
        accessor: 'campaign_name',
        unsortable: true,
    },
    {
        Header: 'Leads Name',
        accessor: 'name',
        unsortable: true,
    },
    {
        Header: 'Phone',
        accessor: 'phone',
        unsortable: true,
    },
    {
        Header: 'Daerah Tempat Tinggal',
        accessor: 'city',
        unsortable: true,
    },
    {
        Header: 'Business Name',
        accessor: 'business_name',
        unsortable: true,
    },
    {
        Header: 'Business Category',
        accessor: 'business_category',
        unsortable: true,
    },
    {
        Header: 'Business Address',
        accessor: 'alamat_bisnis',
        unsortable: true,
    },
    {
        Header: 'Omset Per Bulan',
        accessor: 'omset',
        unsortable: true,
    },
    {
        Header: 'Membutuhkan Aplikasi Kasir',
        accessor: 'is_need_pos',
        unsortable: true,
        Cell: ({ data, value }) => {
            let param = data;
            if (value) {
                param = value;
            }
            if (Array.isArray(param)) {
                param = '';
            }
            return <span>{USER_NEED_POS[param]}</span>;
        },
    },
    {
        Header: 'Notes',
        accessor: 'extra_note',
        unsortable: true,
    },
];

const selectAllCampaign = { id: '', name: 'All Campaign' };

const FILTERTYPE = {
    DATE: 'filterDate',
    CAMPAIGN: 'filterCampaign',
    ALL: 'all',
};

export {
    tableColumn, selectAllCampaign, FILTERTYPE, USER_NEED_POS,
};
