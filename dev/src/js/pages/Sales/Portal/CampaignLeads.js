import React, { Component } from 'react';
import PropTypes from 'prop-types';
import moment from 'moment';
import CoreHOC from '../../../core/CoreHOC';
import { printExcel } from '../../../data';
/* COMPONENT */
import Table from '../../../components/retina/table/Table';
import Select from '../../../components/form/Select';
import DateTimePicker from '../../../components/form/DateTimePicker';
/* HELPER */
import { getPortalCampaignLeads, getPortalCampaign } from '../../../data/setting/portal';
import { catchError } from '../../../utils/helper';
import {
    tableColumn, selectAllCampaign, FILTERTYPE, USER_NEED_POS,
} from './helper/HelperCampaign';
/* STYLES */
import './styles/leads.css';
import { getFilterValue, getUpdatedFilterValue } from '../../../utils/table.util';

class CampaignLeads extends Component {
    static propTypes = {
        calendar: PropTypes.shape({
            start: PropTypes.string,
            end: PropTypes.string,
        }).isRequired,
        assignCalendar: PropTypes.func.isRequired,
        assignButtons: PropTypes.func.isRequired,
        notificationSystem: PropTypes.shape({
            addNotification: PropTypes.func,
        }),
        router: PropTypes.shape({
            push: PropTypes.func,
        }).isRequired,
        assignFilterColoumn: PropTypes.func.isRequired,
        assignRangeDate: PropTypes.func.isRequired,
        showProgress: PropTypes.func.isRequired,
        hideProgress: PropTypes.func.isRequired,
    }

    static defaultProps = {
        notificationSystem: ({
            addNotification: null,
        }),
    }

    constructor(props) {
        super(props);

        this.state = {
            listCampaignName: [],
            tableFilter: [
                { id: FILTERTYPE.DATE, value: '' },
                { id: FILTERTYPE.CAMPAIGN, value: '' },
                { id: FILTERTYPE.ALL, value: '' },
            ],
            isFetch: false,
            tableData: [],
            tableMeta: {
                pageIndex: 0,
                pageSize: 10,
            },

        };
        this.startInit();
    }

    componentDidMount = () => {
        this.fetchListCampaign();
    }

    startInit = () => {
        const {
            assignFilterColoumn, assignRangeDate, assignCalendar, assignButtons,
        } = this.props;

        assignFilterColoumn([]);
        assignRangeDate([]);
        assignCalendar(null, null, null);
        assignButtons([
            {
                type: 'primary',
                content: <span> Download </span>,
                action: () => { this.downloadData(); },
            },
        ]);
    }

    fetchListCampaign = async () => {
        const { showProgress, hideProgress, notificationSystem } = this.props;
        showProgress();

        try {
            const res = await getPortalCampaign();
            const { data } = res;

            const campaignName = data.map(x => Object.assign({}, { id: x.slug, name: x.slug }));

            this.setState({
                listCampaignName: campaignName,
            }, () => {
                hideProgress();
            });
        } catch (e) {
            notificationSystem.addNotification({
                title: 'Get Vacancy Detail failed',
                message: catchError(e),
                level: 'error',
            });
        }
    }

    fetchListLeads = async (state) => {
        const { hideProgress, showProgress, notificationSystem } = this.props;
        const { tableFilter } = this.state;
        const {
            pageIndex, pageSize,
        } = state;

        const filterDate = getFilterValue(tableFilter, FILTERTYPE.DATE);
        const filterCampaign = getFilterValue(tableFilter, FILTERTYPE.CAMPAIGN);
        const filterKeyword = getFilterValue(tableFilter, FILTERTYPE.ALL);

        const payload = {
            limit: pageSize,
            page: Number(pageIndex) + 1,
            ...filterDate && {
                tanggal: filterDate,
            },
            ...filterCampaign && {
                campaign: filterCampaign,
            },
            ...filterKeyword && {
                search: filterKeyword,
            },
        };
        showProgress();
        try {
            this.setState({ isFetch: true });
            const response = await getPortalCampaignLeads(payload);
            if (!response || !response.data) throw new Error('No data found');
            this.setState({
                isFetch: false,
                tableData: response.data,
                tableMeta: {
                    pageIndex,
                    pageSize,
                    totalData: response.metadata.total_rows,
                },
            });
        } catch (e) {
            if (notificationSystem) {
                notificationSystem.addNotification({
                    title: 'Failed',
                    message: catchError(e),
                    level: 'error',
                });
            }
        } finally {
            this.setState({ isFetch: false });
            hideProgress();
        }
    }

    changeTableFilter = (val, type) => {
        const { tableFilter, tableMeta } = this.state;
        const updatedFilterValue = getUpdatedFilterValue(tableFilter, type, val);

        this.setState({ tableFilter: updatedFilterValue, [type]: val }, () => this.fetchListLeads({ ...tableMeta, pageIndex: 0 }));
    }

    changeDateFilterHandler = (val) => {
        const tgl = moment(val).format('YYYY-MM-DD');
        this.changeTableFilter(tgl, FILTERTYPE.DATE);
    }

    clearDateFilterHandler = () => {
        this.changeTableFilter('', FILTERTYPE.DATE);
    }

    formatDataLaporan = (data) => {
        const res = data.map(x => ({
                create_date: moment(x.create_date).format('DD MMM YYYY'),
                campaign_name: x.campaign_name,
                name: x.name,
                phone: x.phone,
                lokasi_bisnis: x.lokasi_bisnis,
                alamat_bisnis: x.alamat_bisnis,
                city: x.city,
                business_name: x.business_name,
                business_category: x.business_category,
                omset: x.omset,
                jumlah_outlet: x.jumlah_outlet,
                lama_bisnis: x.lama_bisnis,
                is_need_pos: USER_NEED_POS[x.is_need_pos],
                extra_note: x.extra_note,
                grab_merchant: x.grab_merchant_id,
            }));

        return res;
    }

    downloadData = async () => {
        const {
            notificationSystem, router, showProgress, hideProgress,
        } = this.props;
        const {
            tableFilter,
        } = this.state;
        const filterDate = getFilterValue(tableFilter, FILTERTYPE.DATE);
        const filterCampaign = getFilterValue(tableFilter, FILTERTYPE.CAMPAIGN);
        const template = 'template_campaign_leads_v3.xlsx';
        const outputName = 'campaign_leads';
        const alias = 'x';
        const variable = {};

        if (filterDate === '') {
            notificationSystem.addNotification({
                level: 'error',
                title: 'Download data failed',
                message: 'Harap menggunakan filter date untuk menghindari overload data',
            });
            return;
        }

        const payload = {
            page: -1,
            ...filterDate && {
                tanggal: filterDate,
            },
            ...filterCampaign && {
                campaign: filterCampaign,
            },
        };

        const _data = await getPortalCampaignLeads(payload);

        if (!_data || (_data && (!_data.data || (_data.data && _data.data.length <= 0)))) {
            notificationSystem.addNotification({
                level: 'error',
                title: 'Download data failed',
                message: 'Tidak ada data yang tersedia dengan filter saat ini',
            });
            return;
        }

        const dataToPrint = this.formatDataLaporan(_data.data);

        const kirim = {
            param: [{
                variable,
                template,
                output_name: outputName,
                data: dataToPrint,
                alias,
            }],
        };

        showProgress();
        printExcel(kirim).then((response) => {
            if (response.status) {
                window.location = response.data;
            } else {
                notificationSystem.addNotification({
                    title: 'Gagal mendapatkan data',
                    message: response.msg,
                    level: 'error',
                });
            }
        }, (message) => {
            if (!message) {
                router.push('/auth/login');
            } else {
                notificationSystem.addNotification({
                    title: 'Gagal mendapatkan data',
                    message,
                    level: 'error',
                });
            }
        });
        hideProgress();
    }

    render() {
        const {
            listCampaignName, tableFilter, tableData, isFetch, tableMeta,
        } = this.state;

        const filterDate = getFilterValue(tableFilter, FILTERTYPE.DATE);
        const filterCampaign = getFilterValue(tableFilter, FILTERTYPE.CAMPAIGN);

        return (
            <section className="panel">
                <div className="panel-heading table-header">
                    <div className="row" style={{ marginBottom: '15px' }}>
                        <div className="col-md-12">
                            <h4 className="panel-title" style={{ paddingTop: '8px' }}>Campaign Leads</h4>
                        </div>
                    </div>
                    <div className="row">
                        <div className="col-md-6">
                            <Select
                                data={[selectAllCampaign, ...listCampaignName]}
                                value={filterCampaign}
                                changeEvent={val => this.changeTableFilter(val, FILTERTYPE.CAMPAIGN)}
                            />
                        </div>
                        <div className="col-md-4">
                            <DateTimePicker
                                className="text-right date-time-picker-component"
                                value={filterDate}
                                onChange={this.changeDateFilterHandler}
                                dateFormat="DD MMM YYYY"
                                timeFormat="HH:mm"
                                hideTime
                            />
                        </div>
                        <div className="col-md-2">
                            <button
                                type="button"
                                className="btn-clear-filterdate"
                                tabIndex="-1"
                                onClick={this.clearDateFilterHandler}
                            >
                                Clear
                            </button>
                        </div>
                    </div>
                </div>
                <div className="panel-body">
                    <Table
                        columns={tableColumn}
                        data={tableData}
                        pageIndex={tableMeta.pageIndex}
                        totalData={tableMeta.totalData}
                        fetchData={this.fetchListLeads}
                        isLoading={isFetch}
                    />
                </div>
            </section>
        );
    }
}

export default CoreHOC(CampaignLeads);
