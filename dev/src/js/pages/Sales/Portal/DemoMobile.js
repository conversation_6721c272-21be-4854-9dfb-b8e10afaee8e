import React, { Component } from 'react';
import PropTypes from 'prop-types';
import moment from 'moment';
import CoreHOC from '../../../core/CoreHOC';
import { printExcel } from '../../../data';

import Table from '../../../components/retina/table/Table';
import SidePopup from '../../../components/sidepopup/Container';
import InputText from '../../../components/form/InputText';
import InputTextArea from '../../../components/form/InputTextArea';
import Switch from '../../../components/form/Switch';
import { demoMobile, updateDemoMobile } from '../../../data/sales';
import { TABLE_META } from './helper/DemoMobile';

class DemoMobile extends Component {
  static propTypes = {
    calendar: PropTypes.shape({
      start: PropTypes.string,
      end: PropTypes.string,
    }).isRequired,
    assignCalendar: PropTypes.func.isRequired,
    assignButtons: PropTypes.func.isRequired,
    notificationSystem: PropTypes.shape({
      addNotification: PropTypes.func,
    }),
    router: PropTypes.shape({
      push: PropTypes.func,
    }).isRequired,
    assignFilterColoumn: PropTypes.func.isRequired,
    assignRangeDate: PropTypes.func.isRequired,
    showProgress: PropTypes.func.isRequired,
    hideProgress: PropTypes.func.isRequired,
  }

  static defaultProps = {
    notificationSystem: ({
      addNotification: () => {},
    }),
  }

  constructor(props) {
    super(props);

    this.state = {
      status: false,
      list: [],
      id: '',
      statusString: '',
      name: '',
      jenisUsaha: '',
      email: '',
      phone: '',
      location: '',
      address: '',
      type: '',
      referalName: '',
      referalCompany: '',
      tanggalEnd: '',
      note: '',
      tanggalStart: '',
      pageSource: '',
      isFetch: false,
      search: '',
    };
  }

  componentWillMount() {
    const {
      assignFilterColoumn, assignRangeDate, assignCalendar, assignButtons, calendar,
    } = this.props;
    assignFilterColoumn([]);
    assignRangeDate([]);
    assignCalendar(null, null, (startDate, endDate) => { this.changeDateHandler(startDate, endDate); });
    assignButtons([
      { type: 'primary', content: <span> Download </span>, action: () => { this.downloadData(); } },
    ]);
    const tanggalStart = calendar.start;
    const tanggalEnd = calendar.end;
    this.setState({
      tanggalEnd,
      tanggalStart,
    }, () => {
      this.getData();
    });
  }

  getData() {
    const { router, notificationSystem, hideProgress } = this.props;
    const {
      tanggalEnd,
      tanggalStart,
    } = this.state;
    const param = {
      start: moment(tanggalStart, 'DD/MM/YYYY').format('YYYY-MM-DD'),
      end: moment(tanggalEnd, 'DD/MM/YYYY').format('YYYY-MM-DD'),
    };
    this.setState({ isFetch: true });
    demoMobile(param).then((response) => {
      if (response.status) {
        this.setState({
          list: response.data,
        });
      } else {
        notificationSystem.addNotification({
          title: 'Gagal mendapatkan data',
          message: '',
          level: 'error',
        });
      }
    }, (message) => {
      if (!message) {
        router.push('/auth/login');
      } else {
        notificationSystem.addNotification({
          title: 'Gagal mendapatkan data',
          message: '',
          level: 'error',
        });
      }
    }).finally(() => {
      this.setState({ isFetch: false });
    });
    hideProgress();
  }

  callEditDetailHandler = (val) => {
    this.setState({
      id: val.id,
      statusString: val.status_string || '',
      name: val.name || '',
      jenisUsaha: val.jenis_usaha || '',
      email: val.email || '',
      phone: val.phone || '',
      location: val.location || '',
      address: val.address || '',
      type: val.type || '',
      referalName: val.referal_name || '',
      referalCompany: val.referal_company || '',
      note: val.notes || '',
      status: val.status === '1' || '',
      pageSource: val.page_source,
    }, () => {
      this.sidePop.showPopup();
    });
  }

  changeSearch = val => this.setState({ search: val })

  changeDateHandler(startDate, endDate) {
    const { assignCalendar } = this.props;
    assignCalendar(startDate, endDate);
    this.setState({
      tanggalEnd: endDate,
      tanggalStart: startDate,
    }, () => {
      this.getData();
    });
  }

  saveHandler() {
    const { id, status } = this.state;
    const {
      router, notificationSystem, showProgress, hideProgress,
    } = this.props;

    let statusValue = 0;
    if (status) {
      statusValue = 1;
    }

    const param = {
      id,
      status: statusValue,
    };

    showProgress();
    updateDemoMobile(param).then((response) => {
      if (response.status) {
        notificationSystem.addNotification({
          title: 'Berhasil mengupdate data',
          message: '',
          level: 'success',
        });
        this.sidePop.hidePopup();
        this.getData();
      } else {
        notificationSystem.addNotification({
          title: 'Gagal mengupdate data',
          message: '',
          level: 'error',
        });
      }
    }, (message) => {
      if (!message) {
        router.push('/auth/login');
      } else {
        notificationSystem.addNotification({
          title: 'Gagal mengupdate data',
          message: '',
          level: 'error',
        });
      }
    });
    hideProgress();
  }

  downloadData() {
    const {
      router, notificationSystem, showProgress, hideProgress,
    } = this.props;
    const {
      list, tanggalStart, tanggalEnd,
    } = this.state;
    const template = 'demo_mobile.xlsx';
    const outputName = 'demo_mobile';
    const alias = 'x';
    const variable = this.state;
    const addVariable = {
        mulai: tanggalStart,
        akir: tanggalEnd,
        dateNow: moment().format('DD MMM YYYY'),
    };
    Object.assign(variable, addVariable);

    const kirim = {
      param: [{
        variable,
        template,
        output_name: outputName,
        data: list,
        alias,
      }],
    };

    showProgress();
    printExcel(kirim).then((response) => {
      if (response.status) {
        window.location = response.data;
      } else {
        notificationSystem.addNotification({
          title: 'Gagal mendapatkan data',
          message: response.msg,
          level: 'error',
        });
      }
    }, (message) => {
      if (!message) {
        router.push('/auth/login');
      } else {
        notificationSystem.addNotification({
          title: 'Gagal mendapatkan data',
          message,
          level: 'error',
        });
      }
    });
    hideProgress();
  }

  render() {
    const {
      list, status, id, statusString, name, jenisUsaha, email, phone, location, address, type, referalName, referalCompany, note, pageSource,
      search, isFetch,
    } = this.state;

    const filteredList = list.filter((item) => {
      if (search === '') return true;
      const searchableKeys = TABLE_META.map(col => col.accessor);
      return searchableKeys.some(key => item[key] && item[key].toString().toLowerCase().includes(search.toLowerCase()));
    });

    return (
      <div>
        <section className="panel">
          <div className="panel-heading table-header">
            <div className="row">
              <div className="col-md-6">
                <h4 className="panel-title" style={{ paddingTop: '8px' }}>
                  Demo majoo
                </h4>
              </div>
              <div className="col-md-6">
                <div style={{ float: 'right' }}>
                  <InputText
                  placeholder="Search keyword"
                  changeEvent={val => this.changeSearch(val)}
                  value={search}
                  />
                </div>
              </div>
            </div>
          </div>
          <div className="panel-body">
            <Table
              key={`table-demo-${search}`}
              columns={TABLE_META}
              data={filteredList}
              totalData={filteredList.length}
              onRowClick={val => this.callEditDetailHandler(val.original)}
              isLoading={isFetch}
              searchQuery={search}
              showDuplicatedData
            />
          </div>
        </section>
        <SidePopup
          ref={(c) => { this.sidePop = c; }}
          width={600}
          saveHandle={() => this.saveHandler(id)}
        >
          <h4 className="side-popup-title">
            {`Mau Majoo ${name} - ${statusString}`}
          </h4>
            <div className="row mb-sm">
              <div className="col-sm-6">
                <InputText label="Name" placeholder="Name" value={name} disabled />
              </div>
              <div className="col-sm-6">
                <InputText label="Type" placeholder="Name" value={type} disabled />
              </div>
            </div>
            <div className="row mb-sm">
              <div className="col-sm-12">
                <InputText label="Jenis Usaha" placeholder="Jenis Usaha" value={jenisUsaha} disabled />
              </div>
            </div>
            <div className="row mb-sm">
              <div className="col-sm-6">
                <InputText label="Email" placeholder="Email" value={email} disabled />
              </div>
              <div className="col-sm-6">
                <InputText label="Phone" placeholder="Phone" value={phone} disabled />
              </div>
            </div>
            <div className="row mb-sm">
              <div className="col-sm-6">
                <InputText label="Referal Name" placeholder="Referal Name" value={referalName} disabled />
              </div>
              <div className="col-sm-6">
                <InputText label="Referal Company" placeholder="Referal Company" value={referalCompany} disabled />
              </div>
            </div>
            <div className="row mb-sm">
              <div className="col-sm-6">
                <InputText label="Location" placeholder="Location" value={location} disabled />
              </div>
              <div className="col-sm-6">
                <InputText label="Address" placeholder="Address" value={address} disabled />
              </div>
            </div>
            <div className="row mb-sm">
              <div className="col-sm-12">
                <InputTextArea label="Note" placeholder="Note" value={note} disabled />
              </div>
            </div>
            <div className="row mb-sm">
              <div className="col-sm-12">
                <InputTextArea label="Page Source" placeholder="Page Source" value={pageSource} disabled />
              </div>
            </div>
            <div className="row mb-sm">
              <div className="col-sm-6">
                Is Follow Up
              </div>
              <div className="col-sm-6 text-right">
                <Switch
                  className="text-right"
                  checked={status}
                  changeEvent={value => this.setState({ status: value })}
                />
              </div>
            </div>
        </SidePopup>
      </div>
    );
  }
}

export default CoreHOC(DemoMobile);
