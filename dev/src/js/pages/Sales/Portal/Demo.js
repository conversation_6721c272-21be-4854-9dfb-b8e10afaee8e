import React, { Component } from 'react';
import PropTypes from 'prop-types';
import moment from 'moment';
import update from 'immutability-helper';

import CoreHOC from '../../../core/CoreHOC';

import Table from '../../../components/retina/table/Table';
import SidePopup from '../../../components/sidepopup/ContainerV2';
import SidebarForm from './sidebarForm';

import { demoInvitation, updateDemoInvitation } from '../../../data/sales';
import { printExcel } from '../../../data';

import { TABLE, STATUS_ENUM } from './config/config';
import { catchError } from '../../../utils/helper';
import InputText from '../../../components/form/InputText';

class Demo extends Component {
  static propTypes = {
    calendar: PropTypes.shape({
      start: PropTypes.string,
      end: PropTypes.string,
    }).isRequired,
    assignCalendar: PropTypes.func.isRequired,
    assignButtons: PropTypes.func.isRequired,
    notificationSystem: PropTypes.shape({
      addNotification: PropTypes.func,
    }),
    router: PropTypes.shape({
      push: PropTypes.func,
    }).isRequired,
    assignFilterColoumn: PropTypes.func.isRequired,
    assignRangeDate: PropTypes.func.isRequired,
    showProgress: PropTypes.func.isRequired,
    hideProgress: PropTypes.func.isRequired,
  }

  static defaultProps = {
    notificationSystem: ({
      addNotification: () => {},
    }),
  }

  constructor(props) {
    super(props);
    const { calendar } = this.props;

    this.state = {
      calendar,
      list: [],
      isFetch: false,
      search: '',
    };
  }

  componentDidMount = () => {
    const {
      assignFilterColoumn, assignRangeDate, assignCalendar, assignButtons,
    } = this.props;

    assignFilterColoumn([]);
    assignRangeDate([]);
    assignCalendar(null, null, (startDate, endDate) => {
        this.changeDateHandler(startDate, endDate);
    });
    assignButtons([
      {
        type: 'primary',
        content: <span> Download </span>,
        action: () => {
          this.downloadData();
        },
      },
    ]);

    this.getData();
  }

  getData = async () => {
    const { showProgress, notificationSystem, hideProgress } = this.props;
    const {
      calendar: { start, end },
    } = this.state;

    showProgress();
    const param = {
      start: moment(start, 'DD/MM/YYYY').format('YYYY-MM-DD'),
      end: moment(end, 'DD/MM/YYYY').format('YYYY-MM-DD'),
    };
    this.setState({ isFetch: true });
    try {
      const res = await demoInvitation(param);
      const { data: list } = res;

      this.setState({ list });
    } catch (err) {
      notificationSystem.addNotification({
        title: 'Failed Get Data',
        message: catchError(err),
        level: 'error',
      });
    } finally {
      this.setState({ isFetch: false });
      hideProgress();
    }
  }

  callEditDetailHandler = (
    { original },
  ) => {
    this.setState({
      form: {
        ...original,
      },
    }, () => {
      this.sidePop.showPopup();
    });
  }

  changeDateHandler = (startDate, endDate) => {
    const { assignCalendar } = this.props;
    assignCalendar(startDate, endDate);

    this.setState({
      calendar: {
        start: startDate,
        end: endDate,
      },
    }, () => {
      this.getData();
    });
  }

  saveHandler = async () => {
    const {
      notificationSystem, showProgress, hideProgress,
    } = this.props;
    const {
      form: { id, status },
    } = this.state;

    const param = {
      id,
      status: status ? 1 : 0,
    };

    showProgress();

    try {
      await updateDemoInvitation(param);
      await this.sidePop.hidePopup();
      await this.getData();

      notificationSystem.addNotification({
        title: 'Berhasil mengupdate data',
        level: 'success',
      });
    } catch (err) {
      notificationSystem.addNotification({
        title: 'Gagal mengupdate data',
        message: catchError(err),
        level: 'error',
      });
    } finally {
      hideProgress();
    }
  }

  downloadData = async () => {
    const {
        notificationSystem, showProgress, hideProgress,
    } = this.props;
    const {
      list, calendar: { start, end },
    } = this.state;

    const template = 'demo_invitation.xlsx';
    const outputName = 'demo_invitation';
    const alias = 'x';

    const variable = {
      mulai: start,
      akir: end,
      dateNow: moment().format('DD MMM YYYY'),
      ...this.state,
    };

    const kirim = {
      param: [{
        variable,
        template,
        output_name: outputName,
        data: list,
        alias,
      }],
    };

    showProgress();

    try {
      const res = await printExcel(kirim);
      const { data } = res;
      window.location = data;
    } catch (err) {
      notificationSystem.addNotification({
        title: 'Gagal mendapatkan data',
        message: catchError(err),
        level: 'error',
      });
    } finally {
      hideProgress();
    }
  }

  changeEvent = (key, value) => {
    const { form } = this.state;

    let newForm = update(form, {
      [key]: { $set: value },
    });

    if (key === 'status') {
      newForm = update(newForm, {
          status: { $set: value ? STATUS_ENUM.ACTIVE : STATUS_ENUM.NONACTIVE },
      });
    }

    this.setState({
      form: newForm,
    });
  }

  changeSearch = val => this.setState({ search: val })

  render() {
    const {
      list, form, search, isFetch,
    } = this.state;

    const filteredList = list.filter((item) => {
      if (search === '') return true;
      const searchableKeys = TABLE.map(col => col.accessor);
      return searchableKeys.some(key => item[key] && item[key].toString().toLowerCase().includes(search.toLowerCase()));
    });

    return (
      <div>
        <section className="panel">
          <div className="panel-heading table-header">
            <div className="row">
              <div className="col-md-6">
                <h4 className="panel-title" style={{ paddingTop: '8px' }}>
                  Demo Invitation
                </h4>
              </div>
              <div className="col-md-6">
                <div style={{ float: 'right' }}>
                  <InputText
                    placeholder="Search keyword"
                    changeEvent={val => this.changeSearch(val)}
                    value={search}
                  />
                </div>
              </div>
            </div>
          </div>
          <div className="panel-body">
            <Table
              key={`table-demo-${search}`}
              columns={TABLE}
              data={filteredList}
              totalData={filteredList.length}
              onRowClick={this.callEditDetailHandler}
              isLoading={isFetch}
              searchQuery={search}
              showDuplicatedData
            />
          </div>
        </section>

        <SidePopup
          ref={(c) => { this.sidePop = c; }}
          width={560}
          type="edit"
          saveHandle={() => this.saveHandler()}
          render={() => (
            <SidebarForm
              title="Demo Invitation"
              data={form}
              changeEvent={this.changeEvent}
            />
          )}
        />
      </div>
    );
  }
}

export default CoreHOC(Demo);
