import React, { Component } from 'react';
import PropTypes from 'prop-types';
import moment from 'moment';
import CoreHOC from '../../../core/CoreHOC';
import Table from '../../../components/retina/table/Table';
import Select from '../../../components/form/Select';
import InputText from '../../../components/form/InputText';
import SidePopup from '../../../components/sidepopup/Container';
import InputNumber from '../../../components/form/InputNumber';
import InputTextArea from '../../../components/form/InputTextArea';
import InputSelect from '../../../components/form/InputSelect';
import DeleteConfirm from '../../../components/modalpopup/DeleteConfirm';
import SaveConfirm from '../../../components/modalpopup/SaveConfirm';

import { getClaim, updateClaim, deleteClaim } from '../../../data/sales/support';
import { TABLE_META } from './config/Claim';
import { catchError } from '../../../utils/helper';

class Claim extends Component {
  static propTypes = {
    calendar: PropTypes.shape({ start: PropTypes.string, end: PropTypes.string }),
    assignCalendar: PropTypes.func,
    assignButtons: PropTypes.func,
    notificationSystem: PropTypes.shape({ addNotification: PropTypes.func }),
    router: PropTypes.shape({ push: PropTypes.func }),
    assignFilterColoumn: PropTypes.func,
    assignRangeDate: PropTypes.func,
  }

  static defaultProps = {
    calendar: { start: '', end: '' },
    assignCalendar: () => { },
    assignButtons: () => { },
    assignFilterColoumn: () => { },
    assignRangeDate: () => { },
    notificationSystem: null,
    router: null,
  }

  constructor(props) {
    super(props);

    this.state = {
      dateFilter: props.calendar,
      statusFilter: '0',
      searchQuery: '',
      tableData: [],
      tableLoading: false,
      jenis: [],
      jenisEdit: [],
      jenisSupport: '',
      detailSupport: '',
      qty: '',
      user: '',
      pemesan: '',
      email: '',
      telepon: '',
      outlet: '',
      alamat: '',
      kode: '',
      catatan: '',
      pengubah: '',
      id_update: '',
      waktu_claim: '',
      statusPerubahan: false,
      id_user_support: '',
      isDisable: false,
      jenisStatus: 'cancel',
      continoueProccess: true,
      userNote: '',
      id_status: '',
    };
  }

  componentDidMount() {
    const {
      assignCalendar, assignButtons, assignFilterColoumn, assignRangeDate, calendar,
    } = this.props;
    assignCalendar(null, null, (startDate, endDate) => {
      this.changeDateHandler(startDate, endDate);
    });
    assignButtons([]);
    assignFilterColoumn([]);
    assignRangeDate([]);
    this.setState({ dateFilter: calendar }, this.fetchData);
  }

  componentDidUpdate(prevProps, prevState) {
    const { dateFilter } = this.state;
    if ((prevState.dateFilter
      && (prevState.dateFilter.start !== dateFilter.start
        || prevState.dateFilter.end !== dateFilter.end))) {
      this.fetchData();
    }
  }

  fetchData = async () => {
    const { dateFilter } = this.state;
    const { router, notificationSystem } = this.props;

    this.setState({ tableLoading: true });

    const param = {
      start: moment(dateFilter.start, 'DD/MM/YYYY').format('YYYY-MM-DD'),
      end: moment(dateFilter.end, 'DD/MM/YYYY').format('YYYY-MM-DD'),
    };
    try {
      const responseClaim = await getClaim(param);
      if (this.sidePop) this.sidePop.hidePopup();

      const i = [];
      const k = [];
      responseClaim.data.status.forEach((x) => {
        const j = [x.id, x.label];
        if (x.id !== 0) k.push(j);
        i.push(j);
      });

      this.setState({
        tableData: responseClaim.data.summary,
        jenis: i,
        jenisEdit: k,
      });
    } catch (message) {
      if (!message) {
        router.push('/auth/login');
      } else {
        notificationSystem.addNotification({
          title: 'Get data failed', message: catchError(message), level: 'error',
        });
      }
    } finally {
      this.setState({ tableLoading: false });
    }
  }

  callEditDetailHandler = ({ original: value }) => {
    let isDisable = false;
    let statusPer = false;
    if (value.statusPerubahan === 'benar') statusPer = true;
    if (value.id_status === 37) isDisable = true;
    this.setState({
      jenisSupport: value.jenis,
      detailSupport: value.detail_support,
      qty: value.qty,
      user: value.user,
      pemesan: value.nama,
      email: value.email,
      telepon: value.telepon,
      outlet: value.outlet,
      alamat: value.alamat,
      kode: value.claim_ticket,
      catatan: value.catatan || '',
      pengubah: value.pengubah,
      id_status: value.id_status,
      id_update: value.id_claim,
      waktu_claim: value.waktu_claim,
      statusPerubahan: statusPer,
      id_user_support: value.id_user_support,
      isDisable,
      continoueProccess: true,
      userNote: value.user_note || '',
    }, () => {
      this.sidePop.showPopup();
    });
  }

  saveHandler = () => {
    const { continoueProccess: continoueProccessRaw, id_status: idStatus } = this.state;
    let continoueProccess = continoueProccessRaw;
    if (idStatus === '4' || idStatus === '37') {
      this.setState({
        jenisStatus: idStatus === '4' ? 'Cancel' : 'Close',
      }, () => {
        this.saveConfirmStatus.showPopup();
      });
      continoueProccess = false;
    }
    if (!continoueProccess) return;
    this.exeSave();
  }

  exeSave = () => {
    const { notificationSystem, router } = this.props;
    const {
      pemesan, email, catatan, telepon, alamat, id_status: status, id_update: id,
      jenisSupport, detailSupport, kode, outlet, nama, waktu_claim: waktu,
      id_user_support: userSuport, qty,
    } = this.state;
    const parameter = {
      pemesan,
      email,
      catatan,
      telepon,
      alamat,
      status,
      id,
      jenis_support: jenisSupport,
      detail_support: detailSupport,
      kode,
      outlet,
      nama,
      waktu,
      id_user_support: userSuport,
      qty,
    };
    updateClaim(parameter).then(() => {
      notificationSystem.addNotification({ title: 'Berhasil', message: 'Update claim success', level: 'success' });
      this.setState({ jenisStatus: '' });
      this.fetchData();
    }, (message) => {
      if (!message) {
        router.push('/auth/login');
      } else {
        notificationSystem.addNotification({ title: 'Update claim failed', message: '', level: 'error' });
      }
    });
  }

  callRemoveHandler = () => {
    this.sidePop.hidePopup();
    this.deleteConfirm.showPopup();
  }

  removeHandler = () => {
    const { notificationSystem, router } = this.props;
    const { id_update: id } = this.state;
    const parameter = { id, status: 9 };
    deleteClaim(parameter).then(() => {
      notificationSystem.addNotification({ title: 'Berhasil', message: 'Update claim success', level: 'success' });
      this.fetchData();
    }, (message) => {
      if (!message) {
        router.push('/auth/login');
      } else {
        notificationSystem.addNotification({ title: 'Update claim failed', message: '', level: 'error' });
      }
    });
  }

  changeDateHandler = (startDate, endDate) => {
    const { assignCalendar } = this.props;
    assignCalendar(startDate, endDate);
    this.setState({ dateFilter: { start: startDate, end: endDate } });
  }

  changeEventStatus = (idStatus) => {
    this.setState({ id_status: idStatus });
  }

  confirmHandler = () => {
    this.setState({ continoueProccess: true }, () => {
      this.saveConfirmStatus.hidePopup();
      this.exeSave();
    });
  }

  handleSearchChange = (value) => {
    this.setState({ searchQuery: value });
  }

  render() {
    const {
      jenis, statusFilter, isDisable, pemesan, email, catatan, telepon, alamat,
      kode, outlet, qty, user, jenisEdit, id_status: idStatus, statusPerubahan,
      userNote, pengubah, jenisStatus, tableData, tableLoading, searchQuery, jenisSupport,
    } = this.state;

    const filteredByStatus = statusFilter === '0'
      ? tableData
      : tableData.filter(item => String(item.id_status) === statusFilter);
    const filteredData = filteredByStatus.filter(item => (!searchQuery || (item.user && item.user.toLowerCase().includes(searchQuery.toLowerCase()))
      || (item.outlet && item.outlet.toLowerCase().includes(searchQuery.toLowerCase()))
      || (item.claim_ticket && item.claim_ticket.toLowerCase().includes(searchQuery.toLowerCase()))));

    return (
      <div>
        <section className="panel">
          <div className="panel-heading">
            <h4 className="panel-title">Claim</h4>
          </div>
          <div className="panel-heading table-header">
            <div className="row">
              <div className="col-md-8" />
              <div className="col-md-2" style={{ paddingRight: '0px' }}>
                <Select
                  data={jenis}
                  value={statusFilter}
                  changeEvent={val => this.setState({ statusFilter: val })}
                />
              </div>
              <div className="col-md-2" style={{ paddingRight: '0px' }}>
                <InputText
                  classes="filter"
                  placeholder="Cari..."
                  changeEvent={this.handleSearchChange}
                  wait={500}
                />
              </div>
            </div>
          </div>
          <div className="panel-body">
            <Table
              columns={TABLE_META}
              data={filteredData}
              isLoading={tableLoading}
              onRowClick={this.callEditDetailHandler}
              totalData={filteredData.length}
              searchQuery={searchQuery}
            />
          </div>
        </section>
        <SidePopup
          ref={(c) => { this.sidePop = c; }}
          width={560}
          type="edit"
          isDisable={isDisable}
          saveHandle={this.saveHandler}
          removeHandle={this.callRemoveHandler}
        >
          <h4 className="side-popup-title">Claim Support</h4>
          <div className="row mb-sm">
            <div className="col-sm-6">
              <InputText label="Support Type" placeholder="Support Type" value={jenisSupport} disabled />
            </div>
            <div className="col-sm-6">
              <InputNumber label="Qty" placeholder="Qty" value={qty} disabled />
            </div>
          </div>
          <div className="row mb-sm">
            <div className="col-sm-12"><InputText label="User" placeholder="User" value={user} disabled /></div>
          </div>
          <div className="row mb-sm">
            <div className="col-sm-12"><InputText label="Buyer" placeholder="Buyer" value={pemesan} disabled /></div>
          </div>
          <div className="row mb-sm">
            <div className="col-sm-12"><InputText label="Email" placeholder="Email" value={email} disabled /></div>
          </div>
          <div className="row mb-sm">
            <div className="col-sm-12"><InputText label="Phone" placeholder="Phone" value={telepon} disabled /></div>
          </div>
          <div className="row mb-sm">
            <div className="col-sm-12"><InputText label="Outlet" placeholder="Outlet" value={outlet} disabled /></div>
          </div>
          <div className="row mb-sm">
            <div className="col-sm-12"><InputTextArea label="Address" placeholder="Address" disabled value={alamat} /></div>
          </div>
          <div className="row mb-sm">
            <div className="col-sm-12"><InputText label="Code" placeholder="Code" value={kode} disabled /></div>
          </div>
          <div className="row mb-sm">
            <div className="col-sm-12">
              <InputSelect data={jenisEdit} value={idStatus} disabled={statusPerubahan} label="Status" changeEvent={this.changeEventStatus} />
            </div>
          </div>
          <div className="row mb-sm">
            <div className="col-sm-12">
              <InputTextArea label="Note" placeholder="Note" value={catatan} disabled={statusPerubahan} changeEvent={value => this.setState({ catatan: value })} />
            </div>
          </div>
          <div className="row mb-sm">
            <div className="col-sm-12">
              <InputTextArea label="User Note" placeholder="Note" value={userNote} disabled />
            </div>
          </div>
          {pengubah !== '' && pengubah !== null && (
            <div className="row mb-sm">
              <div className="col-sm-12">
                <InputText label="Updated By" placeholder="" value={pengubah} disabled />
              </div>
            </div>
          )}
        </SidePopup>
        <DeleteConfirm
          title="Remove Claim List"
          confirmText="Yes, Please"
          cancelText="No"
          confirmHandle={this.removeHandler}
          ref={(c) => { this.deleteConfirm = c; }}
        >
          {`${kode} will be removed from claim List.`}
        </DeleteConfirm>
        <SaveConfirm
          headerTitle="Change Status"
          confirmHandle={this.confirmHandler}
          confirmMsg={`Are you sure to ${jenisStatus} this ?`}
          confirmText="Yes, Please"
          cancelText="No"
          ref={(c) => { this.saveConfirmStatus = c; }}
        >
          {`Are you sure to ${jenisStatus} this ?`}
        </SaveConfirm>
      </div>
    );
  }
}

export default CoreHOC(Claim);
