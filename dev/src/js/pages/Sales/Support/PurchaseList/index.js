import React, { Component } from 'react';
import PropTypes from 'prop-types';
import Table from '../../../../components/retina/table/Table';
import InputText from '../../../../components/form/InputText';
import Select from '../../../../components/form/Select';
import { getPenjualan } from '../../../../data/sales/support';
import {
    tableColumns, reformatCalendar,
} from './utils';
import { catchError } from '../../../../utils/helper';

class PurchaseList extends Component {
    static propTypes = {
        calendar: PropTypes.shape({ start: PropTypes.string, end: PropTypes.string }),
        notificationSystem: PropTypes.shape({ addNotification: PropTypes.func }),
        onRowClick: PropTypes.func,
        handleUpdateStatusList: PropTypes.func,
        statusList: PropTypes.arrayOf(PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.number, PropTypes.string]))),
        listCategorySupport: PropTypes.arrayOf(PropTypes.shape({})),
    };

    static defaultProps = {
        calendar: { start: '', end: '' },
        notificationSystem: ({ addNotification: () => { } }),
        onRowClick: () => { },
        handleUpdateStatusList: () => { },
        statusList: [],
        listCategorySupport: [],
    };

    constructor(props) {
        super(props);
        const { calendar: { start, end } } = props;

        this.state = {
            dateFilter: { start, end },
            filterStatus: '',
            filterSupport: '',
            searchQuery: '',
            listSupport: [],
            tableData: [],
            tableMeta: {
                pageIndex: 1,
                limit: 10,
                totalData: 0,
            },
            tableLoading: false,
        };
    }

    componentDidMount() {
        this._onFetchs({ pageIndex: 0 });
    }

    static getDerivedStateFromProps(props, state) {
        const derivedState = {};

        if (props.listCategorySupport !== state.prevListCategorySupport) {
            const layananBerlangganan = props.listCategorySupport.find(x => String(x.id) === '31');
            const layananTambahan = props.listCategorySupport.find(x => String(x.id) === '2');
            if (layananBerlangganan && layananTambahan) {
                const supportOptions = layananBerlangganan.list.concat(layananTambahan.list);
                const listSupport = supportOptions.map(x => [x.id, x.name]);
                listSupport.unshift(['', 'All']);
                derivedState.listSupport = listSupport;
                derivedState.prevListCategorySupport = props.listCategorySupport;
            }
        }

        if (props.calendar.start !== state.dateFilter.start || props.calendar.end !== state.dateFilter.end) {
            derivedState.dateFilter = { start: props.calendar.start, end: props.calendar.end };
        }

        return Object.keys(derivedState).length ? derivedState : null;
    }

    componentDidUpdate(prevProps, prevState) {
        const {
            dateFilter, filterStatus, filterSupport, searchQuery, tableMeta,
        } = this.state;
        const filtersChanged = (prevState.dateFilter
            && (prevState.dateFilter.start !== dateFilter.start
                || prevState.dateFilter.end !== dateFilter.end))
            || prevState.filterStatus !== filterStatus
            || prevState.filterSupport !== filterSupport
            || prevState.searchQuery !== searchQuery;

        if (filtersChanged) {
            this._onFetchs({ pageIndex: 0, pageSize: tableMeta.limit });
        }
    }

    _onFetchs = async (params) => {
        const { notificationSystem, handleUpdateStatusList } = this.props;
        const {
            pageSize, pageIndex, sortAccessor, sortDirection,
        } = params;
        const {
            dateFilter, filterStatus, filterSupport, searchQuery, tableMeta,
        } = this.state;

        this.setState({ tableLoading: true });

        const payload = {
            start: reformatCalendar(dateFilter.start, 'YYYY-MM-DD'),
            end: reformatCalendar(dateFilter.end, 'YYYY-MM-DD'),
            resultPerpage: pageSize || 10,
            page: pageIndex + 1,
            id_status: filterStatus,
            keyword: searchQuery,
            support_type: filterSupport,
        };

        if (sortAccessor) {
            payload.isAsc = sortDirection;
            payload.column = sortAccessor;
        }

        try {
            const res = await getPenjualan(payload);
            this.setState({
                tableData: res.data.summary,
                tableMeta: {
                    pageIndex,
                    limit: pageSize || 10,
                    totalData: res.data.total,
                },
            });
            handleUpdateStatusList(res.data.status);
        } catch (e) {
            notificationSystem.addNotification({
                title: 'Get Data List Failed', message: catchError(e), level: 'error',
            });
            this.setState({ tableData: [], tableMeta: { ...tableMeta, totalData: 0 } });
        } finally {
            this.setState({ tableLoading: false });
        }
    }

    refetchTable = () => {
        this._onFetchs({ pageIndex: 0 });
    }

    handleSearchChange = (value) => {
        this.setState({ searchQuery: value });
    }

    render() {
        const {
            listSupport, filterStatus, filterSupport, tableData,
            tableMeta, tableLoading, searchQuery,
        } = this.state;
        const { onRowClick, statusList } = this.props;

        return (
            <section className="panel">
                <div className="panel-heading table-header">
                    <div className="row">
                        <div className="col-md-3">
                            <h4 className="panel-title" style={{ paddingTop: '8px' }}>
                                Purchase
                            </h4>
                        </div>
                        <div className="col-md-3">
                            <Select
                                data={statusList}
                                value={filterStatus}
                                changeEvent={val => this.setState({ filterStatus: val })}
                            />
                        </div>
                        <div className="col-md-3">
                            <Select
                                data={listSupport}
                                value={filterSupport}
                                changeEvent={val => this.setState({ filterSupport: val })}
                            />
                        </div>
                        <div className="col-md-3">
                            <div className="filter-container">
                                <InputText
                                    classes="filter"
                                    placeholder="Cari..."
                                    changeEvent={this.handleSearchChange}
                                    wait={500}
                                />
                            </div>
                        </div>
                    </div>
                </div>
                <div className="panel-body">
                    <Table
                        columns={tableColumns}
                        data={tableData}
                        fetchData={this._onFetchs}
                        isLoading={tableLoading}
                        onRowClick={onRowClick}
                        pageIndex={tableMeta.pageIndex}
                        rowLimit={tableMeta.limit}
                        totalData={tableMeta.totalData}
                        searchQuery={searchQuery}
                    />
                </div>
            </section>
        );
    }
}

export default PurchaseList;
