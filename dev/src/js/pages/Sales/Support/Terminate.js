import React, { PureComponent } from 'react';
import PropTypes from 'prop-types';
import moment from 'moment';
import update from 'immutability-helper';

/* COMPONENTS */
import CoreHOC from '../../../core/CoreHOC';
import Table from '../../../components/retina/table/Table';
import InputText from '../../../components/form/InputText';
import Select from '../../../components/form/Select';
import DeleteConfirm from '../../../components/modalpopup/DeleteConfirm';
import TerminateSidebar from './Sidebar/TerminateSidebar';

/* DATA */
import {
    getTerminate, getTerminateDetail, updateTerminate,
} from '../../../data/sales/support';

/* CONFIG */
import { catchError } from '../../../utils/helper';
import {
    TABLE_META, STATUS_LIST, PAYMENT_LIST,
} from './config/Terminate';

@CoreHOC
export default class Terminate extends PureComponent {
    static propTypes = {
        calendar: PropTypes.shape({ start: PropTypes.string, end: PropTypes.string }),
        notificationSystem: PropTypes.shape({ addNotification: PropTypes.func }),
        assignCalendar: PropTypes.func,
        assignButtons: PropTypes.func,
        assignFilterColoumn: PropTypes.func,
        assignRangeDate: PropTypes.func,
    };

    static defaultProps = {
        calendar: { start: '', end: '' },
        notificationSystem: { addNotification: null },
        assignCalendar: () => { },
        assignButtons: () => { },
        assignFilterColoumn: () => { },
        assignRangeDate: () => { },
    };

    constructor(props) {
        super(props);
        const { calendar } = this.props;

        this.state = {
            dateFilter: calendar,
            statusFilter: 0,
            paymentFilter: 0,
            searchQuery: '',
            tableData: [],
            tableMeta: {
                pageIndex: 1,
                limit: 10,
                totalData: 0,
            },
            tableLoading: false,
            form: {
                id: '',
                supportCategoryName: '',
                supportId: '',
                supportName: '',
                detail: '',
                categoryName: '',
                outletList: {
                    outletId: '', outletName: '', manager: '', activeExp: '',
                },
                userId: '',
                note: '',
                pic: '',
                email: '',
            },
        };
    }

    componentDidMount = () => {
        const {
            assignCalendar, assignButtons, assignFilterColoumn, assignRangeDate,
        } = this.props;
        assignButtons([]);
        assignFilterColoumn([]);
        assignRangeDate([]);
        assignCalendar(null, null, (startDate, endDate) => {
            this.changeDateHandler(startDate, endDate);
        });

        this._onFetchs({ pageIndex: 0 });
    }

    componentDidUpdate(prevProps, prevState) {
        const {
            dateFilter, statusFilter, paymentFilter, searchQuery, tableMeta,
        } = this.state;
        const filtersChanged = (prevState.dateFilter
            && (prevState.dateFilter.start !== dateFilter.start
                || prevState.dateFilter.end !== dateFilter.end))
            || prevState.statusFilter !== statusFilter
            || prevState.paymentFilter !== paymentFilter
            || prevState.searchQuery !== searchQuery;

        if (filtersChanged) {
            this._onFetchs({ pageIndex: 0, pageSize: tableMeta.limit });
        }
    }

    _onFetchs = async (params) => {
        const { notificationSystem } = this.props;
        const {
            pageIndex, pageSize, sortAccessor, sortDirection,
        } = params;
        const {
            dateFilter, statusFilter, paymentFilter, searchQuery, tableMeta,
        } = this.state;

        this.setState({ tableLoading: true });

        let payload = {
            start_date: moment(dateFilter.start, 'DD/MM/YYYY').format('YYYY-MM-DD'),
            end_date: moment(dateFilter.end, 'DD/MM/YYYY').format('YYYY-MM-DD'),
            status: statusFilter,
            paymentStatus: paymentFilter,
            limit: pageSize || 10,
            page: pageIndex + 1,
            search: searchQuery,
        };

        if (sortAccessor) {
            payload = { ...payload, order: sortAccessor, sort: sortDirection };
        }

        try {
            const res = await getTerminate(payload);
            const { data, meta } = res;
            this.setState({
                tableData: data,
                tableMeta: {
                    pageIndex,
                    limit: pageSize || 10,
                    totalData: meta.total,
                },
            });
        } catch (e) {
            notificationSystem.addNotification({
                title: 'Gagal mendapatkan data', message: catchError(e), level: 'error',
            });
            this.setState({ tableData: [], tableMeta: { ...tableMeta, totalData: 0 } });
        } finally {
            this.setState({ tableLoading: false });
        }
    }

    callEditDetailHandler = async ({ original: { id } }) => {
        const { notificationSystem } = this.props;
        try {
            const res = await getTerminateDetail({}, id);
            const {
                data: {
                    support_category_name: supportCategoryName, support_id: supportId,
                    support_name: supportName, support_detail: detail, company: categoryName,
                    outlet_id: outletId, outlet_name: outletName, user_id: userId,
                    user_name: manager, expired_date: activeExp, pic_email: email,
                    pic_name: pic, note,
                },
            } = res;
            this.setState({
                form: {
                    id,
                    supportCategoryName,
                    supportId,
                    supportName,
                    detail,
                    categoryName,
                    outletList: {
                        outletId, outletName, manager, activeExp,
                    },
                    userId,
                    pic,
                    email,
                    note: note || '',
                },
            }, () => {
                this.sidePop.showPopup();
            });
        } catch (err) {
            notificationSystem.addNotification({
                title: 'Gagal mendapatkan data', message: catchError(err), level: 'error',
            });
        }
    }

    changeEvent = (key, value) => {
        const { form } = this.state;
        const newForm = update(form, { [key]: { $set: value } });
        this.setState({ form: newForm });
    }

    saveHandle = () => {
        this.confirmPopup.showPopup();
    }

    submitHandle = async () => {
        const { notificationSystem } = this.props;
        const {
            form: {
                userId, supportId, outletList: { outletId }, note,
            }, tableMeta,
        } = this.state;
        const payload = {
            support_id: supportId, user_id: userId, outlet_id: outletId, note,
        };
        try {
            await updateTerminate(payload);
            this.confirmPopup.hidePopup();
            this.sidePop.hidePopup();
            notificationSystem.addNotification({ title: 'Berhasil memperbarui data', level: 'success' });
            this._onFetchs({ pageIndex: 0, pageSize: tableMeta.limit });
        } catch (err) {
            notificationSystem.addNotification({
                title: 'Gagal memperbarui data', message: catchError(err), level: 'error',
            });
        }
    }

    changeDateHandler = (start, end) => {
        const { assignCalendar } = this.props;
        assignCalendar(start, end);
        this.setState({ dateFilter: { start, end } });
    }

    handleSearchChange = (value) => {
        this.setState({ searchQuery: value });
    }

    render() {
        const { notificationSystem } = this.props;
        const {
            form, statusFilter, paymentFilter, tableData, tableMeta, tableLoading, searchQuery,
        } = this.state;

        return (
            <div>
                <section className="panel">
                    <div className="panel-heading table-header">
                        <div className="row">
                            <div className="col-md-3">
                                <h4 className="panel-title" style={{ paddingTop: '8px' }}>Termination</h4>
                            </div>
                            <div className="col-md-3">
                                <Select
                                    data={STATUS_LIST}
                                    value={statusFilter}
                                    changeEvent={value => this.setState({ statusFilter: value })}
                                />
                            </div>
                            <div className="col-md-3">
                                <Select
                                    data={PAYMENT_LIST}
                                    value={paymentFilter}
                                    changeEvent={value => this.setState({ paymentFilter: value })}
                                />
                            </div>
                            <div className="col-md-3">
                                <InputText
                                    classes="filter"
                                    placeholder="Cari..."
                                    changeEvent={this.handleSearchChange}
                                    wait={500}
                                />
                            </div>
                        </div>
                    </div>
                    <div className="panel-body">
                        <Table
                            columns={TABLE_META}
                            data={tableData}
                            fetchData={this._onFetchs}
                            isLoading={tableLoading}
                            onRowClick={this.callEditDetailHandler}
                            pageIndex={tableMeta.pageIndex}
                            rowLimit={tableMeta.limit}
                            totalData={tableMeta.totalData}
                            searchQuery={searchQuery}
                        />
                    </div>
                </section>

                <TerminateSidebar
                    data={form}
                    ref={(c) => { this.sidePop = c; }}
                    notificationSystem={notificationSystem}
                    changeEvent={this.changeEvent}
                    saveHandle={this.saveHandle}
                />

                <DeleteConfirm
                    ref={(c) => { this.confirmPopup = c; }}
                    title="Confirmation"
                    confirmText="Yes, Terminate"
                    cancelText="Cancel"
                    confirmHandle={this.submitHandle}
                >
                    Are You Sure to Terminate?
                </DeleteConfirm>
            </div>
        );
    }
}
