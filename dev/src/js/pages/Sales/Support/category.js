import React, { Component } from 'react';
import PropTypes from 'prop-types';
import { FieldFeedback, FieldFeedbacks } from 'react-form-with-constraints';
import CoreHOC from '../../../core/CoreHOC';
import Table from '../../../components/retina/table/Table';
import InputText from '../../../components/form/InputText';
import SidePopup from '../../../components/sidepopup/ContainerV2';
import InputNumber from '../../../components/form/InputNumber';
import InputTextArea from '../../../components/form/InputTextArea';
import ModalPopup from '../../../components/form/ModalPopup';
import DeleteConfirm from '../../../components/modalpopup/DeleteConfirm';
import Switch from '../../../components/form/Switch';
import { getSupportCategory, addSupportCategory, updateSupportCategory } from '../../../data/sales/master';
import { TABLE_META } from './config/category';
import { catchError } from '../../../utils/helper';

class CategorySuppport extends Component {
  static propTypes = {
    notificationSystem: PropTypes.shape({ addNotification: PropTypes.func }),
    router: PropTypes.shape({ push: PropTypes.func }),
    assignFilterColoumn: PropTypes.func,
    assignRangeDate: PropTypes.func,
    assignButtons: PropTypes.func,
  }

  static defaultProps = {
    notificationSystem: { addNotification: null },
    router: { push: null },
    assignFilterColoumn: () => { },
    assignRangeDate: () => { },
    assignButtons: () => { },
  }

  constructor(props) {
    super(props);

    this.state = {
      tableData: [],
      searchQuery: '',
      tableLoading: false,
      type: 'edit',
      name: '',
      id: '',
      desc: '',
      active_status: false,
      pro_business: false,
      id_jenis: 0,
      titleSide: 'Add Support Category',
      seq: 0,
      imgSrc: '',
      captionGambar: '',
      displayed: false,
    };
  }

  componentDidMount() {
    const {
      assignFilterColoumn, assignRangeDate, assignButtons, assignCalendar,
    } = this.props;
    assignFilterColoumn([]);
    assignRangeDate([]);
    assignCalendar(null, null, null);
    assignButtons([{ type: 'primary', content: <span> Add Support Category </span>, action: this.callAddHandler }]);
    this.fetchData();
  }

  fetchData = async () => {
    const { router, notificationSystem } = this.props;
    this.setState({ tableLoading: true });
    try {
      const response = await getSupportCategory();
      this.setState({ tableData: response.data });
      if (this.sidePop) this.sidePop.hidePopup();
    } catch (message) {
      if (!message) {
        router.push('/auth/login');
      } else {
        notificationSystem.addNotification({
          title: 'get data failed', message: catchError(message), level: 'error',
        });
      }
    } finally {
      this.setState({ tableLoading: false });
    }
  }

  callEditDetailHandler = (value) => {
    this.setState({
      id: value.id,
      name: value.name,
      desc: value.desc,
      id_jenis: value.id_jenis,
      active_status: value.active_status === '1',
      pro_business: value.pro_business === '1',
      type: 'edit',
      seq: value.seq,
      titleSide: 'Update Support Category',
    }, () => {
      this.sidePop.showPopup();
    });
  }

  callAddHandler = () => {
    this.setState({
      id: '',
      name: '',
      desc: '',
      id_jenis: 0,
      active_status: true,
      pro_business: true,
      type: 'save',
      seq: 0,
      titleSide: 'Add Support Category',
    }, () => {
      this.sidePop.showPopup();
    });
  }

  saveHandler = async () => {
    const {
      name, id, desc, id_jenis: idJenis, seq, active_status, pro_business, type,
    } = this.state;
    const { notificationSystem } = this.props;
    const parameter = {
      name,
      id,
      desc,
      id_jenis: idJenis,
      seq,
      active_status,
      pro_business,
    };

    try {
      if (type === 'edit') {
        await updateSupportCategory(parameter);
        notificationSystem.addNotification({
          title: 'Success', message: 'Update support category success', level: 'success',
        });
      } else {
        await addSupportCategory(parameter);
        notificationSystem.addNotification({
          title: 'Success', message: 'Add support category success', level: 'success',
        });
      }
      this.fetchData();
    } catch (err) {
      notificationSystem.addNotification({
        title: 'Update failed', message: catchError(err), level: 'error',
      });
    }
  }

  removeHandler = () => {
    if (this.deleteConfirm) this.deleteConfirm.hidePopup();
  }

  removeModal = () => {
    this.setState({ displayed: false, imgSrc: '', captionGambar: '' });
  }

  handleChange = (val, type, e = null) => {
    this.setState({ [type]: val });
    if (e && this.sidePop) {
      this.sidePop.validateInput(e.target);
    }
  }

  handleSearchChange = (value) => {
    this.setState({ searchQuery: value });
  }

  render() {
    const {
      type, titleSide, name, desc, seq,
      active_status, pro_business,
      imgSrc, captionGambar, displayed,
      tableData, tableLoading, searchQuery,
    } = this.state;

    const filteredData = tableData.filter(item => (!searchQuery
      || (item.name && item.name.toLowerCase().includes(searchQuery.toLowerCase()))
      || (item.desc && item.desc.toLowerCase().includes(searchQuery.toLowerCase()))));

    return (
      <div>
        <section className="panel">
          <div className="panel-heading table-header">
            <div className="row">
              <div className="col-md-9">
                <h4 className="panel-title" style={{ paddingTop: '8px' }}>
                  Support Category
                </h4>
              </div>
              <div className="col-md-3">
                <InputText
                  classes="filter"
                  placeholder="Cari..."
                  changeEvent={this.handleSearchChange}
                  wait={500}
                />
              </div>
            </div>
          </div>
          <div className="panel-body">
            <Table
              columns={TABLE_META}
              data={filteredData}
              isLoading={tableLoading}
              onRowClick={({ original }) => this.callEditDetailHandler(original)}
              totalData={filteredData.length}
              searchQuery={searchQuery}
            />
          </div>
        </section>
        <SidePopup
          ref={(c) => { this.sidePop = c; }}
          width={560}
          type="edit"
          saveHandle={() => this.saveHandler(type)}
          render={({ show }) => {
            if (show) {
              return (
                <div>
                  <h4 className="side-popup-title">{titleSide}</h4>
                  <div className="row mb-sm">
                    <div className="col-sm-12">
                      <InputText label="Name" placeholder="Name" value={name} changeEvent={(value, e) => this.handleChange(value, 'name', e)} name="name_form" />
                      <FieldFeedbacks for="name_form">
                        <FieldFeedback when={val => val === ''}>Name tidak boleh kosong</FieldFeedback>
                      </FieldFeedbacks>
                    </div>
                  </div>
                  <div className="row mb-sm">
                    <div className="col-sm-12">
                      <InputTextArea label="Description" placeholder="Description" value={desc} changeEvent={value => this.setState({ desc: value })} />
                    </div>
                  </div>
                  <div className="row mb-sm">
                    <div className="col-sm-12">
                      <InputNumber label="Seq" placeholder="Seq" value={seq} changeEvent={(value, e) => this.handleChange(value, 'seq', e)} name="seq_form" />
                      <FieldFeedbacks for="seq_form">
                        <FieldFeedback when={val => val === ''}>Seq tidak boleh kosong</FieldFeedback>
                      </FieldFeedbacks>
                    </div>
                  </div>
                  <div className="row mb-sm">
                    <div className="col-sm-6 text-left">
                      <span className="control-label" style={{ paddingTop: '12px' }}>Status</span>
                    </div>
                    <div className="col-sm-6 text-right">
                      <Switch className="text-right" checked={active_status} changeEvent={value => this.setState({ active_status: value })} />
                    </div>
                  </div>
                  <div className="row mb-sm">
                    <div className="col-sm-6 text-left">
                      <span className="control-label" style={{ paddingTop: '12px' }}>Is Subscribe</span>
                    </div>
                    <div className="col-sm-6 text-right">
                      <Switch className="text-right" checked={pro_business} valueTrue="True" valueFalse="False" changeEvent={value => this.setState({ pro_business: value })} />
                    </div>
                  </div>
                </div>
              );
            }
            return null;
          }}
        />
        <DeleteConfirm
          title="Hapus Tittle"
          confirmText="Ya, Hapus"
          cancelText="Tidak, Simpan"
          confirmHandle={this.removeHandler}
          ref={(c) => { this.deleteConfirm = c; }}
        >
          Menghapus pembelian akan menghilangkan secara permanen
          dari daftar Produk anda. Tindakan ini tidak bisa dibatalkan
        </DeleteConfirm>
        <ModalPopup
          src={imgSrc}
          name={captionGambar}
          clickEvent={this.removeModal}
          displayed={displayed}
        />
      </div>
    );
  }
}

export default CoreHOC(CategorySuppport);
