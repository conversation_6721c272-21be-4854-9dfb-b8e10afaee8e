import React, { Component } from 'react';
import PropTypes from 'prop-types';
import * as moment from 'moment';
import update from 'immutability-helper';
import CoreHOC from '../../../core/CoreHOC';
import { catchError } from '../../../utils/helper';

/* COMPONENTS */
import Table from '../../../components/retina/table/Table';
import Select from '../../../components/form/Select';
import InputText from '../../../components/form/InputText';
import DeleteConfirm from '../../../components/modalpopup/DeleteConfirm';
import CalendarWithPeriodHelper from '../../../components/form/CalendarWithPeriodHelper';
import FeedbackSidebar from './Sidebar/FeedbackSidebar';

/* DATA */
import {
  getFeedback, updateFeedback, deleteFeedback, getBusinessTypeList, getSupportTypeList,
} from '../../../data/sales/support';

/* CONFIG */
import {
  TABLE_META, STATUS_ENUM, SOURCE_ENUM_LIST,
} from './config/Feedback';

class Feedback extends Component {
  static propTypes = {
    assignCalendar: PropTypes.func,
    assignButtons: PropTypes.func,
    notificationSystem: PropTypes.shape({ addNotification: PropTypes.func }),
    assignFilterColoumn: PropTypes.func,
    assignRangeDate: PropTypes.func,
    showProgress: PropTypes.func.isRequired,
    hideProgress: PropTypes.func.isRequired,
    calendar: PropTypes.shape({ start: PropTypes.string, end: PropTypes.string }),
  }

  static defaultProps = {
    assignCalendar: () => { },
    assignButtons: () => { },
    notificationSystem: { addNotification: () => { } },
    assignFilterColoumn: () => { },
    assignRangeDate: () => { },
    calendar: { start: '', end: '' },
  }

  constructor(props) {
    super(props);
    const { calendar: { start, end } } = props;

    this.state = {
      dateFilter: { start, end },
      statusFilter: STATUS_ENUM.ALL,
      sourceFilter: '',
      businessTypeFilter: '',
      supportTypeFilter: '',
      searchQuery: '',
      tableData: [],
      tableLoading: false,
      statusFilterList: [],
      statusEditList: [],
      businessTypeList: [],
      supportTypeList: [],
      form: {
        usaha_name: '',
        name: '',
        email: '',
        note: '',
        respon: '',
        id_status: '',
        outlet: '',
        pengubah: '',
        id_update: '',
      },
    };
  }

  componentDidMount = async () => {
    const {
      assignFilterColoumn, assignRangeDate, assignCalendar, assignButtons,
    } = this.props;
    assignFilterColoumn([]);
    assignRangeDate([]);
    assignButtons([]);
    assignCalendar(null, null, null);

    await this.getDropDownData();
  }

  componentDidUpdate(prevProps, prevState) {
    const {
      dateFilter, statusFilter, sourceFilter, businessTypeFilter, supportTypeFilter, searchQuery,
    } = this.state;
    const filtersChanged = (prevState.dateFilter
      && (prevState.dateFilter.start !== dateFilter.start
        || prevState.dateFilter.end !== dateFilter.end))
      || prevState.statusFilter !== statusFilter
      || prevState.sourceFilter !== sourceFilter
      || prevState.businessTypeFilter !== businessTypeFilter
      || prevState.supportTypeFilter !== supportTypeFilter
      || prevState.searchQuery !== searchQuery;

    if (filtersChanged) {
      this.fetchData();
    }
  }

  getDropDownData = async () => {
    const { notificationSystem } = this.props;
    try {
      const { data: businessTypeList } = await getBusinessTypeList();
      const { data: supportTypeList } = await getSupportTypeList();
      this.setState({
        businessTypeList: [{ id: '', name: 'All Business Type' }, ...businessTypeList],
        supportTypeList: [{ id: '', name: 'All Support Type' }, ...supportTypeList],
      }, this.fetchData);
    } catch (err) {
      notificationSystem.addNotification({
        title: 'Get data Business List Failed', message: catchError(err), level: 'error',
      });
    }
  }

  fetchData = async () => {
    const { notificationSystem, hideProgress, showProgress } = this.props;
    const {
      dateFilter: { start, end }, statusFilter, sourceFilter,
      searchQuery, businessTypeFilter, supportTypeFilter,
    } = this.state;

    this.setState({ tableLoading: true });
    showProgress();

    const filter = JSON.stringify({
      ...sourceFilter !== '' && { source: sourceFilter },
      ...businessTypeFilter !== '' && { business_type: businessTypeFilter },
      ...supportTypeFilter !== '' && { support_type: supportTypeFilter },
      ...statusFilter !== STATUS_ENUM.ALL && { status: statusFilter },
    });
    const param = {
      start: moment(start, 'DD/MM/YYYY').format('YYYY-MM-DD'),
      end: moment(end, 'DD/MM/YYYY').format('YYYY-MM-DD'),
      ...searchQuery !== '' && { search: searchQuery },
      filter,
    };

    try {
      const { data: { status: statusFilterList, summary: feedbackList } } = await getFeedback(param);
      const statusEditList = statusFilterList.filter(({ id }) => id !== 0).map(({ id, label }) => ({ value: id, label }));
      this.setState({
        tableData: feedbackList,
        statusFilterList,
        statusEditList,
      });
    } catch (error) {
      notificationSystem.addNotification({
        title: 'Get data feedback failed', message: catchError(error), level: 'error',
      });
    } finally {
      this.setState({ tableLoading: false });
      hideProgress();
    }
  }

  changeCalendarHandler = (start, end) => {
    const { assignCalendar } = this.props;
    assignCalendar(start, end);
    this.setState({ dateFilter: { start, end } });
  };

  handleSearchChange = (val) => {
    this.setState({ searchQuery: val });
  }

  callEditDetailHandler = ({ original: value }) => {
    let idStatus = value.id_status;
    if (
      value.id_status !== STATUS_ENUM.NEW
      && value.id_status !== STATUS_ENUM.READ
      && value.id_status !== STATUS_ENUM.SOLVE
    ) {
      idStatus = STATUS_ENUM.ALL;
    }
    this.setState({
      form: {
        usaha_name: value.usaha_name,
        name: value.name,
        email: value.email,
        note: value.note,
        no_whatsapp: value.no_whatsapp || '',
        respon: value.respon || '',
        id_status: idStatus.toString(),
        outlet: value.outlet,
        pengubah: value.pengubah,
        id_update: value.feedback_id,
      },
    }, () => {
      this.sidePop.showPopup();
    });
  }

  saveHandler = async () => {
    const { showProgress, hideProgress, notificationSystem } = this.props;
    const { form: { respon, id_status: idStatus, id_update: idUpdate } } = this.state;
    showProgress();
    try {
      const payload = { respon, id_status: idStatus, id_update: idUpdate };
      await updateFeedback(payload);
      notificationSystem.addNotification({
        title: 'Success', message: 'Update data feedback success', level: 'success',
      });
      this.sidePop.hidePopup();
      this.fetchData();
    } catch (error) {
      notificationSystem.addNotification({
        title: 'Update data feedback failed', message: catchError(error), level: 'error',
      });
    } finally {
      hideProgress();
    }
  }

  callRemoveHandler = () => {
    this.sidePop.hidePopup();
    this.deleteConfirm.showPopup();
  }

  removeHandler = async () => {
    const { showProgress, hideProgress, notificationSystem } = this.props;
    const { form: { id_update: idUpdate } } = this.state;
    showProgress();
    try {
      const parameter = { id: idUpdate, status: '9' };
      await deleteFeedback(parameter);
      this.deleteConfirm.hidePopup();
      this.fetchData();
      notificationSystem.addNotification({
        title: 'Success', message: 'Remove data feedback success', level: 'success',
      });
    } catch (error) {
      notificationSystem.addNotification({
        title: 'Remove data feedback failed', message: catchError(error), level: 'error',
      });
    } finally {
      hideProgress();
    }
  }

  changeEvent = (key, value) => {
    const { form } = this.state;
    const newForm = update(form, { [key]: { $set: value } });
    this.setState({ form: newForm });
  }

  render() {
    const {
      statusFilterList, businessTypeList, supportTypeList,
      form, statusEditList, dateFilter, statusFilter, sourceFilter,
      businessTypeFilter, supportTypeFilter,
      tableData, tableLoading, searchQuery,
    } = this.state;
    const { name } = form;

    return (
      <div>
        <section className="panel">
          <div className="panel-heading table-header">
            <div className="row">
              <div className="col-md-12">
                <h4 className="panel-title" style={{ paddingTop: '8px' }}>Feedback</h4>
              </div>
            </div>
          </div>
          <div className="panel-heading table-header">
            <div className="row">
              <div className="col-md-6">
                <CalendarWithPeriodHelper
                  startDate={dateFilter.start}
                  endDate={dateFilter.end}
                  changeEvent={this.changeCalendarHandler}
                  styles={{ width: '100%' }}
                />
              </div>
              <div className="col-md-3">
                <Select
                  data={SOURCE_ENUM_LIST}
                  value={sourceFilter}
                  changeEvent={value => this.setState({ sourceFilter: value })}
                />
              </div>
              <div className="col-md-3">
                <Select
                  data={businessTypeList}
                  value={businessTypeFilter}
                  placeholder="Busniness Type"
                  changeEvent={value => this.setState({ businessTypeFilter: value })}
                />
              </div>
            </div>
          </div>
          <div className="panel-heading table-header">
            <div className="row">
              <div className="col-md-4">
                <Select
                  data={supportTypeList}
                  value={supportTypeFilter}
                  placeholder="Support Type"
                  changeEvent={value => this.setState({ supportTypeFilter: value })}
                />
              </div>
              <div className="col-md-4">
                <Select
                  data={statusFilterList}
                  value={statusFilter}
                  changeEvent={value => this.setState({ statusFilter: value })}
                />
              </div>
              <div className="col-md-4">
                <InputText
                  placeholder="Cari ..."
                  changeEvent={this.handleSearchChange}
                  wait={500}
                />
              </div>
            </div>
          </div>
          <div className="panel-body">
            <Table
              columns={TABLE_META}
              data={tableData}
              isLoading={tableLoading}
              onRowClick={this.callEditDetailHandler}
              totalData={tableData.length}
              searchQuery={searchQuery}
            />
          </div>
        </section>

        <FeedbackSidebar
          data={form}
          ref={(c) => { this.sidePop = c; }}
          changeEvent={this.changeEvent}
          statusList={statusEditList}
          saveHandle={this.saveHandler}
          removeHandle={this.callRemoveHandler}
        />

        <DeleteConfirm
          title="Remove Feedback List"
          confirmHandle={this.removeHandler}
          ref={(c) => { this.deleteConfirm = c; }}
        >
          Feed back from
          {' '}
          {` ${name} `}
          {' '}
          will be removed from feedback List.
        </DeleteConfirm>
      </div>
    );
  }
}

export default CoreHOC(Feedback);
