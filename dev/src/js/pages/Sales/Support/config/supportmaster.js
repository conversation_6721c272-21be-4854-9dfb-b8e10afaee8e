import PriceColumn from '../../../../components/table/components/PriceColumn';
import PercentColumn from '../../../../components/table/components/PercentColumn';

export const TABLE_META = [
    {
        Header: 'category',
        accessor: 'category',
        colMinWidth: 150,
    },
    {
        Header: 'name',
        accessor: 'name',
        colMinWidth: 125,
    },
    {
        Header: 'subtitle',
        accessor: 'subtitle',
        colMinWidth: 175,
    },
    {
        Header: 'Special Price',
        accessor: 'harga_spesial',
    },
    {
        Header: 'Price',
        accessor: 'harga',
        Cell: PriceColumn,
        colMinWidth: 120,
    },
    {
        Header: 'Discount',
        accessor: 'diskon',
        Cell: PercentColumn,
    },
    {
        Header: 'Quantity',
        accessor: 'qty',
    },
    {
        Header: 'Duration',
        accessor: 'duration',
    },
    {
        Header: 'seq',
        accessor: 'seq',
    },
];

export const SATUAN_DURATION = ['<PERSON><PERSON>', '<PERSON>ulan', '<PERSON>', 'Jam'];
