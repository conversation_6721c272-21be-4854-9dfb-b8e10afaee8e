import React from 'react';
import PropTypes from 'prop-types';
import CoreHOC from '../../../../core/CoreHOC';
import InputText from '../../../../components/form/InputText';
import Autocomplete from '../../../../components/form/Autocomplete';
import InputTextArea from '../../../../components/form/InputTextArea';
import Select from '../../../../components/form/Select';
import Rating from '../../../../components/form/Rating';
import RadioGroup from '../../../../components/form/RadioGroup';
import ImageUpload from '../../../../components/form/ImageUpload';
import Switch from '../../../../components/form/Switch';
import { getUserWilayah, getJenisUsaha } from '../../../../data';
import { addPipeline, getDetailUser, updatePipeline } from '../../../../data/sales';
import { GOOGLE_API_KEY } from '../../../../components/maps/maps.utils';

const listKasir = [
  { value: 'Mokapos', option: 'Mokapos' },
  { value: 'Pawoon', option: 'Pawoon' },
  { value: 'Hellobil', option: 'Hellobil' },
  { value: 'Qasir', option: 'Qasir' },
  { value: 'Olsera', option: 'Olsera' },
  { value: 'Spot', option: 'Spot' },
  { value: 'Nutapos', option: 'Nutapos' },
  { value: 'Lainnya', option: 'Lainnya' },
];

class AddFunel extends React.Component {
  static propTypes = {
    assignButtons: PropTypes.func,
    assignCalendar: PropTypes.func,
    assignFilterColoumn: PropTypes.func,
    assignRangeDate: PropTypes.func,
    location: PropTypes.shape({
      query: PropTypes.shape({}),
    }),
    notificationSystem: PropTypes.shape({
      addNotification: PropTypes.func.isRequired,
    }),
    router: PropTypes.shape({
      push: PropTypes.func.isRequired,
    }),
  }

  static defaultProps = {
    assignButtons: () => {},
    assignCalendar: () => {},
    assignFilterColoumn: () => {},
    assignRangeDate: () => {},
    notificationSystem: () => {},
    router: () => {},
    location: {
      query: null,
    },
  }

  constructor(props) {
    super(props);
    this.initiateState();
  }

  componentWillMount() {
    const {
      assignButtons, assignCalendar, assignFilterColoumn, assignRangeDate, location,
    } = this.props;
    assignButtons([]);
    assignCalendar(null, null, null);
    assignFilterColoumn([]);
    assignRangeDate([]);
    getJenisUsaha().then((res) => {
      this.setState({
          jenisList: res.data,
      });
    });

    getUserWilayah().then((res) => {
      this.setState({
        listProvinsi: res.data[0].province,
        idPipeline: location.query.id,
      }, () => {
        if (location.query.id !== undefined) {
          this.getDetail();
        }
      });
    });
    this.imageURLChangeHandler = this.imageURLChangeHandler.bind(this);
  }

  getDetail() {
    const { notificationSystem } = this.props;
    const { idPipeline } = this.state;
    const param = {
      client: idPipeline,
    };

    getDetailUser(param).then((res) => {
      const { data } = res;
      if (res.status) {
        this.setState({
          usaha: data.usaha,
          jenisId: data.jenis_usaha,
          address: data.address,
          email: data.usaha_email,
          provinsiId: data.user_usaha_provinsi_text,
          kotaId: data.user_usaha_kota_text,
          kecamatan: data.user_usaha_kecamatan,
          imageURL: data.picture,
          position: data.coordinat,
          aplikasi: data.survey_alasan,
          // aplikasiLain: data.survey_alasan,
          isUseKasir: data.is_user_app,
          pic: data.user,
          picEmail: data.pic_email,
          picPhone: data.user_notlp,
          note: data.note,
          leadSource: data.source,
          idUser: data.id_user,
          userKlopos: data.userKlopos,
          idPipeline: data.id,
          rating: parseInt(data.rating, 10),
          qty: data.outlet_qty,
          doRegister: false,
        }, () => {
          const { aplikasi } = this.state;
          this.changeEventProvinsi(data.user_usaha_provinsi_text);
          const listKasirFiltered = listKasir.filter(dl => dl.value === aplikasi);
          if (listKasirFiltered.length > 0) {
            this.setState({
              showApp: false,
              aplikasi,
              aplikasiLain: '',
            });
          } else {
            this.setState({
              showApp: true,
              aplikasi: 'Lainnya',
              aplikasiLain: aplikasi,
            });
          }
        });
      } else {
        notificationSystem.addNotification({
          title: 'Get Data Failed',
          message: res.msg,
          level: 'error',
        });
      }
    });
  }

  getGeoCode = (lat, lng) => {
    const { listProvinsi } = this.state;
    const latlng = `${lat},${lng}`;
    fetch(`https://maps.googleapis.com/maps/api/geocode/json?latlng=${latlng}&key=${GOOGLE_API_KEY}&language=id`)
      .then(res => res.json())
      .then((data) => {
      const { results } = data;
      const dt = results[1].address_components;
      const address = results[0].formatted_address;
      const provinsi = dt[5].short_name;
      const kota = dt[4].short_name;
      const kecamatan = dt[3].short_name;
      listProvinsi.find((el) => {
        if (el.name === provinsi) {
          this.changeEventProvinsi(el.id);
          el.kota.find((kt) => {
            if (kt.name === kota) {
              this.setState({ kotaId: kt.id });
            }
            return false;
          });
        }
        return false;
      });

      this.setState({
        kecamatan,
        address,
      });
    } catch (error) {
      console.error('Error fetching geocode data:', error);
    }
  }

  getGeoLocation = () => {
    if (navigator.geolocation) {
      navigator.geolocation.getCurrentPosition(
        (position) => {
          const lat = position.coords.latitude;
          const lng = position.coords.longitude;
          this.setState({
            position: `${lat}, ${lng}`,
          }, () => {
            this.getGeoCode(lat, lng);
          });
        },
      );
    }
  }

  cancelHandler = () => {
    const { location, router } = this.props;
    const user = location.query.user !== undefined ? location.query.user : '';
    const deepclick = location.query.deepclick !== undefined ? location.query.deepclick : '';
    if (location.query.id !== undefined) {
      router.push(`/funnel/funnel-list/detail?id=${location.query.id}&user=${user}&deepclick=${deepclick}`);
    } else {
      router.push(`/funnel?id=${user}&deepclick=${deepclick}`);
    }
  }

  // eslint-disable-next-line class-methods-use-this
  validateEmail(email) {
    // eslint-disable-next-line no-useless-escape
    const re = /^(([^<>()\[\]\\.,;:\s@"]+(\.[^<>()\[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/;
    return re.test(String(email).toLowerCase());
  }

  saveHandler() {
    const { notificationSystem, location, router } = this.props;
    const {
      usaha, jenisId, address, email, provinsiId, kotaId, kecamatan, imageURL, position,
      isUseKasir, aplikasi, aplikasiLain, showApp, pic, picEmail, picPhone, note, leadSource, userKlopos, idPipeline,
      doRegister, rating, qty,
    } = this.state;
    const param = {
      name: usaha,
      business_type: jenisId,
      address,
      email,
      user_usaha_provinsi_text: provinsiId,
      user_usaha_kota_text: kotaId,
      user_usaha_kecamatan: kecamatan,
      image: imageURL,
      location: position,
      pic_name: pic,
      pic_phone: picPhone,
      note,
      lead_source: leadSource,
      is_use_kasir: isUseKasir,
      aplikasi: showApp !== true ? aplikasi : aplikasiLain,
      pic_email: picEmail,
      userKlopos,
      id_pipeline: idPipeline,
      doRegister,
      rating,
      qty,
    };

    let errorValidate = false;
    if (usaha === '' || usaha === null) {
      notificationSystem.addNotification({
        title: 'Company name must be filled',
        message: '',
        level: 'error',
      });
      this.setState({
        msgUser: 'Company name must be filled',
      });
      errorValidate = true;
    }

    if (pic === '' || pic === null) {
      notificationSystem.addNotification({
        title: 'PIC Name must be filled',
        message: '',
        level: 'error',
      });
      this.setState({
        msgPic: 'PIC name must be filled',
      });
      errorValidate = true;
    }

    if (picEmail === '' || !this.validateEmail(picEmail) || picEmail == null) {
      notificationSystem.addNotification({
        title: 'Not valid email',
        message: '',
        level: 'error',
      });
      this.setState({
        msgPicEmail: 'Not valid email',
      });
      errorValidate = true;
    }

    if (picPhone === '' || picPhone === null) {
      notificationSystem.addNotification({
        title: 'PIC phone must be filled',
        message: '',
        level: 'error',
      });
      this.setState({
        msgPhone: 'PIC phone must be filled',
      });
      errorValidate = true;
    }

    if (errorValidate) {
      return;
    }

    if (location.query.id !== undefined) {
      if (email === '' || !this.validateEmail(email) || email == null) {
        notificationSystem.addNotification({
          title: 'Not valid email',
          message: '',
          level: 'error',
        });
        this.setState({
          msgEmail: 'Not valid email',
        });
        errorValidate = true;
      }
      updatePipeline(param).then((res) => {
        if (!res.status) {
          notificationSystem.addNotification({
            title: 'update pipeline failed',
            message: res.msg,
            level: 'error',
          });
          return;
        }
        const deepclick = location.query.deepclick !== undefined ? location.query.deepclick : '';
        notificationSystem.addNotification({
          title: 'Pipeline Successfully updated',
          level: 'success',
        });
        setTimeout(() => {
          router.push(`/funnel/funnel-list/detail?id=${location.query.id}&deepclick=${deepclick}`);
        }, 1000);
      }, (message) => {
        if (!message) {
          router.push('/auth/login');
        } else {
          notificationSystem.addNotification({
            title: 'update pipeline failed',
            message: '',
            level: 'error',
          });
        }
      });
      return;
    }

    addPipeline(param).then((response) => {
      if (response.status) {
        const user = location.query.user !== undefined ? location.query.user : '';
        const deepclick = location.query.deepclick !== undefined ? location.query.deepclick : '';
        notificationSystem.addNotification({
          title: 'Pipeline Successfully added',
          level: 'success',
        });
        setTimeout(() => {
          router.push(`/funnel?id=${user}&deepclick=${deepclick}`);
        }, 1000);
      } else {
        notificationSystem.addNotification({
          title: 'Pipeline failed to added data',
          message: response.msg,
          level: 'error',
        });
      }
    }, (message) => {
      if (!message) {
        router.push('/auth/login');
      } else {
        notificationSystem.addNotification({
          title: 'create pipeline failed',
          message: '',
          level: 'error',
        });
      }
    });
  }

  changeEventProvinsi(value) {
    const { listProvinsi } = this.state;
    const cek = listProvinsi.filter(d => d.id === value);
    if (cek.length === 0) {
      this.setState({
        listKota: [],
        provinsiId: value,
      });
    } else {
      this.setState({
        listKota: cek[0].kota,
        provinsiId: value,
      });
    }
  }

  imageURLChangeHandler(value) {
    this.setState({
      imageURL: value,
    });
  }

  initiateState() {
    const lebar = window.innerWidth;
      let lebarMax = 360;
      if (lebar < 500 && lebar > 361) {
        lebarMax = 460;
      } else if (lebar > 501) {
        lebarMax = 660;
      }
      this.state = {
          usaha: '',
          jenisList: [],
          listProvinsi: [],
          listKota: [],
          jenisId: '',
          address: '',
          // provinsi: '',
          // kota: '',
          kecamatan: '',
          email: '',
          provinsiId: '',
          kotaId: '',
          imageURL: '',
          position: '',
          isUseKasir: 'No',
          aplikasi: '',
          aplikasiLain: '',
          showApp: false,
          pic: '',
          picEmail: '',
          picPhone: '',
          note: '',
          leadSource: 'Web',
          idUser: '',
          userKlopos: '',
          lebarMax,
          idPipeline: '',
          doRegister: false,
          msgUser: '',
          msgPhone: '',
          msgPic: '',
          msgPicEmail: '',
          msgEmail: '',
          qty: 1,
          rating: 0,
      };
  }

  changeRating(val) {
    this.setState({
      rating: val,
    });
  }

  changeAppKasir(val) {
    if (val !== 'Lainnya') {
      this.setState({
        aplikasi: val,
        showApp: false,
      });
    } else {
      this.setState({
        aplikasi: val,
        aplikasiLain: '',
        showApp: true,
      });
    }
  }

  render() {
    const {
      usaha, jenisList, jenisId, address, email, listProvinsi, provinsiId, listKota, kotaId, kecamatan, imageURL, position,
      isUseKasir, aplikasi, aplikasiLain, showApp, pic, picEmail, picPhone, note, lebarMax, userKlopos, doRegister, idUser,
      msgUser, msgPhone, msgPic, msgEmail, msgPicEmail, qty, rating,
    } = this.state;
    const { location } = this.props;
    return (
      <div>
        <section className="panel">
          <div className="panel-heading">
            <h4 className="panel-title">Sales Leads</h4>
          </div>
          <div className="panel-heading table-header">
            <div className="row">
                <div className="col-sm-12 col-md-6 col-xs-12">
                  <InputText
                    label="Nama Outlet"
                    // placeholder="user"
                    value={usaha}
                    changeEvent={value => this.setState({ usaha: value, msgUser: '' })}
                  />
                  {msgUser !== '' && <font color="red" style={{ fontSize: 12 }}>{msgUser}</font>}
                </div>
                {location.query.id !== undefined && (
                <div className="col-sm-12 col-md-6 col-xs-12">
                  <InputText
                    label="User majoo (Email)"
                    // placeholder="User majoo (Email)"
                    value={userKlopos}
                    changeEvent={value => this.setState({ userKlopos: value })}
                  />
                </div>
                )}
                <div className="col-sm-12 col-md-6 col-xs-12">
                  <Autocomplete
                    data={jenisList}
                    selector="name"
                    value={jenisId}
                    changeEvent={value => this.setState({ jenisId: value })}
                    // placeholder="Search ..."
                    label="Jenis Bisnis"
                  />
                </div>

                {location.query.id !== undefined && (
                  <div className="col-sm-12 col-md-6 col-xs-12">
                    <InputText
                      label="Email"
                      // placeholder="Email"
                      value={email}
                      changeEvent={value => this.setState({ email: value, msgEmail: '' })}
                    />
                    {msgEmail !== '' && <font color="red" style={{ fontSize: 12 }}>{msgEmail}</font>}
                  </div>
                )}
                <div className="col-sm-6 col-xs-12 col-md-6">
                  <div className="row" style={{ display: 'flex', alignItems: 'flex-end' }}>
                    <div className="col-xs-8">
                      <InputText
                        label="GPS Loc"
                        // placeholder="Location"
                        value={position}
                        disabled
                      />
                    </div>
                    <div className="col-xs-4">
                      <br />
                      <button type="button" className="btn btn-primary btn-block" onClick={() => { this.getGeoLocation(); }}>Update Location</button>
                    </div>
                  </div>
                </div>
                <div className="col-sm-12 col-md-6 col-xs-12">
                  <InputTextArea
                    label="Alamat"
                    // placeholder="Address"
                    value={address}
                    changeEvent={value => this.setState({ address: value })}
                  />
                </div>
                <div className="col-sm-12 col-md-6 col-xs-12">
                  <div className="row">
                    <div className="col-xs-6">
                      <Autocomplete
                        data={listProvinsi}
                        selector="name"
                        value={provinsiId}
                        changeEvent={(val) => { this.changeEventProvinsi(val); }}
                        // placeholder="Search ..."
                        label="Provinsi"
                      />
                    </div>
                    <div className="col-xs-6">
                      <Autocomplete
                        data={listKota}
                        selector="name"
                        value={kotaId}
                        label="Kota"
                        changeEvent={(val) => { this.setState({ kotaId: val }); }}
                      />
                    </div>
                    <div className="col-xs-6">
                      <InputTextArea
                        label="Kecamatan"
                        // placeholder="Kecamatan"
                        value={kecamatan}
                        changeEvent={value => this.setState({ kecamatan: value })}
                      />
                    </div>
                  </div>
                </div>
                <div className="col-sm-12 col-md-6 col-xs-12">
                  <ImageUpload
                    label="Foto Outlet"
                    changeEvent={this.imageURLChangeHandler}
                    imageURL={imageURL}
                    imageWidth={lebarMax}
                    imageHeight={304}
                    imageCrop
                  />
                </div>
                <br />
                <div className="col-sm-6 col-xs-12 col-md-6">
                  <RadioGroup
                    changeEvent={value => this.setState({ isUseKasir: value })}
                    data={[
                      { value: 'Yes', label: 'Yes' },
                      { value: 'No', label: 'No' },
                    ]}
                    name="status-user"
                    picked={isUseKasir}
                    label="Pernah memakai aplikasi kasir ?"
                  />
                </div>
              { isUseKasir === 'Yes' && (
                <div className="col-sm-6 col-xs-12 col-md-6">
                  <Select
                    data={listKasir}
                    value={aplikasi}
                    changeEvent={value => this.changeAppKasir(value)}
                    classes=" mb-reset"
                    label="Pilih nama aplikasi kasir lainnya"
                  />
                  { showApp && (
                    <InputText
                      label="Tulis nama aplikasi kasir lainnya"
                      // placeholder="App Name"
                      value={aplikasiLain}
                      changeEvent={value => this.setState({ aplikasiLain: value })}
                    />
                  )}
                </div>
              )}
              <div className="col-sm-12 col-xs-12 col-md-12">
                <h5>CONTACT PERSON</h5>
              </div>
              <div className="col-sm-6 col-xs-12 col-md-6">
                <InputText
                  label="Nama PIC"
                  // placeholder="PIC Name"
                  value={pic}
                  changeEvent={(val) => { this.setState({ pic: val, msgPic: '' }); }}
                />
                {msgPic !== '' && <font color="red" style={{ fontSize: 12 }}>{msgPic}</font>}
              </div>
              <div className="col-sm-6 col-xs-12 col-md-6">
                <InputText
                  label="Email"
                  // placeholder="Email PIC"
                  value={picEmail}
                  changeEvent={value => this.setState({ picEmail: value, msgPicEmail: '' })}
                />
                {msgPicEmail !== '' && <font color="red" style={{ fontSize: 12 }}>{msgPicEmail}</font>}
              </div>
              <div className="col-sm-6 col-xs-12 col-md-6">
                <InputText
                  label="No Telepon"
                  // placeholder="Phone"
                  value={picPhone}
                  changeEvent={value => this.setState({ picPhone: value, msgPhone: '' })}
                />
                {msgPhone !== '' && <font color="red" style={{ fontSize: 12 }}>{msgPhone}</font>}
              </div>
              <div className="col-sm-6 col-xs-12 col-md-6">
                <InputTextArea
                  label="Catatan"
                  // placeholder="Note"
                  value={note}
                  changeEvent={value => this.setState({ note: value })}
                />
              </div>
              {location.query.id !== undefined && (
                <div className="col-sm-6 col-xs-12 col-md-6">
                  &nbsp;
                  <Rating
                    label="Rating"
                    value={rating}
                    changeEvent={value => this.setState({ rating: value })}
                  />
                </div>
              )}
                <div className="col-sm-6 col-xs-12 col-md-6">
                  <InputTextArea
                    label="Jumlah Outlet"
                    // placeholder="1"
                    value={qty}
                    changeEvent={value => this.setState({ qty: value })}
                  />
                </div>
              { /* <div className="row mb-sm">
                <div className="col-sm-12">
                  <RadioGroup
                    changeEvent={value => this.setState({ leadSource: value })}
                    data={[
                          { value: 'Web', label: 'Web' },
                          { value: 'Sales', label: 'Sales' },
                      ]}
                    name="lead-source"
                    picked={leadSource}
                    label="Lead Source"
                  />
                </div>
              </div> */ }
              {false && (idUser === '' || idUser === undefined) && (
                <div>
                  <div className="col-sm-6 text-left">
                    <span className="control-label" style={{ paddingTop: '12px' }}>Do Register</span>
                  </div>
                  <div className="col-sm-6 text-right">
                    <Switch className="text-right" valueFalse="No" valueTrue="Yes" checked={doRegister} changeEvent={value => this.setState({ doRegister: value })} />
                  </div>
                </div>
              )}
              <div className="col-sm-12 col-xs-12">
                &nbsp;
              </div>
              <div className="col-sm-6 col-xs-6 col-md-6">
                <button type="button" className="btn" style={{ width: '100px' }} onClick={() => { this.cancelHandler(); }}>Batal</button>
              </div>
              <div className="col-sm-6 col-xs-6 col-md-6 text-right">
                <button type="button" className="btn btn-primary" style={{ width: '100px' }} onClick={() => { this.saveHandler(); }}>Simpan</button>
              </div>
            </div>
          </div>
        </section>
      </div>
    );
  }
}

AddFunel.propTypes = {
  assignButtons: PropTypes.func,
  assignCalendar: PropTypes.func,
  assignFilterColoumn: PropTypes.func,
  assignRangeDate: PropTypes.func,
  location: PropTypes.shape({
    query: PropTypes.shape({
      id: PropTypes.number,
      deepclick: PropTypes.string,
      user: PropTypes.string,
    }),
  }),
  notificationSystem: PropTypes.shape({
    addNotification: PropTypes.func.isRequired,
  }),
  router: PropTypes.shape({
    push: PropTypes.func.isRequired,
  }),
};

AddFunel.defaultProps = {
  assignButtons: () => {},
  assignCalendar: () => {},
  assignFilterColoumn: () => {},
  assignRangeDate: () => {},
  location: {
    query: null,
  },
  notificationSystem: () => {},
  router: () => {},
};

export default CoreHOC(AddFunel);
