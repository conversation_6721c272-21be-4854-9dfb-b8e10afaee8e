

import React, {
    useContext, useEffect, useRef, useState,
} from 'react';
import { uniq } from 'lodash';
import Table from '../../../../../components/retina/table/Table';
import InputText from '../../../../../components/form/InputText';
import { TableAddProductColumn } from '../utils';
import SuppliesProductContext from '../context/SuppliesProductContext';
import { catchError } from '../../../../../utils/helper';
import InputSelectCheck from '../../../../../components/form/InputSelectCheck';
import { getSuppliesCategory } from '../../../../../data/sales/master';
import * as suppliesApi from '../../../../../data/sales/supplies';
import { TITLE_TYPE } from '../enum';

const AddProducts = () => {
    const {
        setIsAddProducts, notificationSystem, assignCustomFooter,
        showProgress, hideProgress, type,
    } = useContext(SuppliesProductContext);
    const [query, setQuery] = useState({
        outlet: [],
        category: [],
        search: '',
    });
    const [categories, setCategories] = useState([]);
    const [outlets, setOutlets] = useState([]);
    const [pageIndex, setPageIndex] = useState(1);
    const [hasMoreItems, setHasMoreItems] = useState(true);
    const [isLoading, setIsLoading] = useState(true);
    const [isFetching, setIsFetching] = useState(true);
    const [selectedProduct, setSelectedProduct] = useState([]);
    const [tableData, setTableData] = useState([]);
    const [unselectedRow, setUnselectedRow] = useState([]);
    const [tableMeta, setTableMeta] = useState({
        pageIndex: 0,
        total: 0,
        pageSize: 10,
    });

    const fetchingData = async ({ search, index: page = pageIndex }) => {
        if (!hasMoreItems) return;
        setIsLoading(true);
        const payload = {
            page,
            perpage: 20,
            status: 1,
            ...search && { search },
        };
        try {
            const { data, meta } = await suppliesApi.getListWhitelistOutletV2(payload);
            const mappedResponse = data.map(({ outlet_id: id, outlet_name: name }) => ({ id, name }));
            let datas = [];
            if (search !== undefined && (pageIndex === 0)) {
                datas = [...datas, ...mappedResponse];
            } else {
                datas = [
                    ...outlets,
                    ...mappedResponse,
                ];
            }
            const filtered = datas.reduce((acc, current) => {
                const x = acc.find(item => item.id === current.id);
                if (!x) {
                    return acc.concat([current]);
                }
                return acc;
            }, []);
            setPageIndex(meta.current_page + 1);
            setHasMoreItems(datas.length < meta.total);
            setOutlets(filtered);
        } catch (err) {
            notificationSystem.addNotification({
                title: 'Failed get list outlet',
                message: catchError(err),
                level: 'error',
            });
        } finally {
            setIsLoading(false);
        }
    };

    const fetchDataOutlets = React.useCallback(fetchingData, [pageIndex, hasMoreItems, outlets]);

    const onFetch = async (tableQuery) => {
        const { pageSize, pageIndex: page } = tableQuery;
        if (!query.outlet.length || !query.outlet.length) {
            setTableData([]);
            setTableMeta({
                pageSize,
                pageIndex: page,
                total: 0,
            });
        }
        const payload = {};
        Object.assign(payload, {
            per_page: pageSize,
            page: parseInt(page, 10) + 1,
        });

        Object.assign(payload, {
            ...query.search && { search: query.search },
            ...query.outlet.length && { id_outlet_seller: query.outlet.join(',') },
            ...(query.category.length && query.category.length !== categories.length && { category_no: query.category.join(',') }),
        });
        setIsLoading(true);
        try {
            const res = await suppliesApi.getSupplyProducts(payload);
            if (!res.data) throw new Error(res.message);
            const tempProducts = (res.data.product || []).map(x => ({ id: x.supplies_no, ...x, disabled: x.supplies_categories.includes(TITLE_TYPE[type]) }));
            setTableData(tempProducts);
            setUnselectedRow(prev => uniq([...prev, ...tempProducts.filter(item => item.disabled).map(item => item.id)]));
            setTableMeta({
                pageSize,
                pageIndex: page,
                total: res.meta.total,
            });
        } catch (err) {
            if (notificationSystem) {
                notificationSystem.addNotification({
                    title: 'Failed',
                    message: catchError(err),
                    level: 'error',
                });
            }
            setTableData([]);
            setTableMeta({
                pageSize,
                pageIndex: page,
                total: 0,
            });
        } finally {
            hideProgress();
            setIsLoading(false);
        }
    };

    const fetchCategory = async () => {
        try {
            setIsFetching(true);
            const payload = {
                page: 1,
                limit: 100,
                ...query.outlet.length && { outlet_id: query.outlet.join(',') },
            };
            const res = await getSuppliesCategory(payload);
            if (!Array.isArray(res.data)) throw new Error(res);
            const newList = await res.data.map(x => ({ id: x.no, name: x.name }));
            setCategories(newList);
        } catch (error) {
            notificationSystem.addNotification({
                title: 'Failed get list category',
                message: catchError(error),
                level: 'error',
            });
        } finally {
            setIsFetching(false);
        }
    };

    const handleAddProduct = async () => {
        try {
            if (selectedProduct.length < 1) throw new Error('please choose product first');
            showProgress();
            const payload = selectedProduct.map(x => ({ item_id: x, category: TITLE_TYPE[type] }));
            await suppliesApi.createProductCategory(payload);
            setIsAddProducts(false);
            notificationSystem.addNotification({
                title: 'Success',
                message: 'Success menambahkan data produk',
                level: 'success',
            });
        } catch (error) {
            notificationSystem.addNotification({
                title: 'Failed',
                message: catchError(error),
                level: 'error',
            });
        } finally {
            hideProgress();
        }
    };

    const handleSelectedChange = (selected) => {
        setSelectedProduct(selected.filter(item => !unselectedRow.includes(item)));
    };

    useEffect(() => {
        if (query.outlet.length) fetchCategory();
    }, [query.outlet]);

    useEffect(() => {
        fetchDataOutlets({ index: 1 });
    }, []);

    useEffect(() => {
        assignCustomFooter(
            <div className="d-flex" style={{ justifyContent: 'end' }}>
                <button
                    type="button"
                    onClick={() => setIsAddProducts(false)}
                    className="btn btn-light"
                >
                    Cancel
                </button>
                <button
                    type="button"
                    className="btn btn-primary"
                    onClick={handleAddProduct}
                    disabled={!selectedProduct.length}
                >
                    Add Product
                </button>
            </div>,
        );
        return () => assignCustomFooter(undefined);
    }, [selectedProduct.length]);

    useEffect(() => {
        onFetch({ ...tableMeta, pageIndex: 0 });
    }, [query.search]);

    return (
        <section className="panel">
            <div className="panel-heading">
                <button
                    type="button"
                    onClick={() => setIsAddProducts(false)}
                    style={{
                        padding: 0,
                        color: '#00b7b5',
                        border: 'none',
                        backgroundColor: 'transparent',
                        display: 'flex',
                        alignItems: 'center',
                        gap: 12,
                    }}
                >
                    <i className="fa fa-chevron-left" style={{ top: 'unset' }} />
                    Back
                </button>
                <h4 className="panel-title" style={{ paddingTop: '10px', fontWeight: 600 }}>
                    Add Products
                </h4>
            </div>
            <div className="panel-heading table-header">
                <div className="d-flex" style={{ justifyContent: 'space-between', width: '100%' }}>
                    <div className="d-flex" style={{ gap: '8px' }}>
                        <InputSelectCheck
                            option={outlets}
                            value={query.outlet}
                            labelCounter="Outlet"
                            onChange={value => setQuery(() => ({ category: [], outlet: value }))}
                            fetchData={fetchDataOutlets}
                            onSearch={search => fetchDataOutlets({ search, index: 1 })}
                            sortBy="id"
                            isLoading={isLoading}
                        />
                        <InputSelectCheck
                            option={categories}
                            value={query.category}
                            labelCounter="Category"
                            selectAllLabel="All Category"
                            onChange={value => setQuery(prev => ({ ...prev, category: value }))}
                            showSelectAll
                            isLoading={isFetching}
                        />
                        <button
                            type="button"
                            className="btn btn-primary"
                            style={{ height: 36, whiteSpace: 'nowrap' }}
                            onClick={() => onFetch({ ...tableMeta, pageIndex: 0 })}
                            disabled={!query.category.length || !query.outlet.length}
                        >
                            Filter Products
                        </button>
                    </div>
                    <div className="d-flex" style={{ gap: '8px' }}>
                        <InputText
                            placeholder="Search keyword"
                            changeEvent={value => setQuery(prev => ({ ...prev, search: value }))}
                            wait={500}
                        />
                    </div>
                </div>
            </div>
            <div className="panel-body">
                <Table
                    fetchData={onFetch}
                    columns={TableAddProductColumn}
                    data={tableData}
                    pageIndex={tableMeta.pageIndex}
                    totalData={tableMeta.total}
                    keyId="supplies_no"
                    onSelectedChange={handleSelectedChange}
                    isLoading={isLoading}
                    customEmptyDataProps={{
                        doodleImage: null,
                        title: null,
                        description: (
                            <div className="rt-noData" style={{ whiteSpace: 'nowrap' }}>There are no data records to display. Please select a filter outlet and category to display products</div>
                        ),
                    }}
                />
            </div>
        </section>
    );
};

export default AddProducts;
