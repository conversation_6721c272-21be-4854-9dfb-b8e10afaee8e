/* eslint-disable jsx-a11y/interactive-supports-focus */
/* eslint-disable jsx-a11y/click-events-have-key-events */


import React, {
    useContext, useEffect, useMemo, useRef, useState,
} from 'react';
import Table from '../../../../../components/retina/table/Table';
import { ActionOptions, StatusOptions, TableColumn } from '../utils';
import { ACTION, TITLE_TYPE } from '../enum';
import FileUpload from '../../../../../components/form/FileUpload';
import SuppliesProductContext from '../context/SuppliesProductContext';
import { catchError } from '../../../../../utils/helper';
import InputSelect from '../../../../../components/form/Select';
import ModalPopup from '../../../../../components/modalpopup/v.2/Container';
import * as suppliesApi from '../../../../../data/sales/supplies';
import InputText from '../../../../../components/form/InputText';

const Home = () => {
    const {
        type, notificationSystem, showProgress, hideProgress,
    } = useContext(SuppliesProductContext);
    const [action, setAction] = useState('1');
    const [query, setQuery] = useState({
        status: '',
        search: '',
    });
    const [modal, setModal] = useState({ wording: '', show: false, act: () => { } });
    const [file, setFile] = useState({
        name: '',
        fileUrl: '',
    });
    const [isFetch, setIsFetch] = useState(false);
    const [tableData, setTableData] = useState([]);
    const [selectedProduct, setSelectedProduct] = useState([]);
    const [tableMeta, setTableMeta] = useState({
        pageIndex: 0,
        total: 0,
        pageSize: 10,
        key: 0,
    });

    const onFetch = async (tableQuery) => {
        setIsFetch(true);
        const { pageSize, pageIndex } = tableQuery;

        const payload = {
            per_page: pageSize,
            page: parseInt(pageIndex, 10) + 1,
        };

        const { status, search } = query;

        Object.assign(payload, {
            ...status && { status },
            ...search && { search },
            supplies_category: TITLE_TYPE[type],
        });

        try {
            const res = await suppliesApi.getSupplyProducts(payload);
            if (!res.data) throw new Error(res.message);
            setFile({
                name: res.data.supplies_category_image.split('/').pop(),
                fileUrl: res.data.supplies_category_image,
            });
            const tempProducts = (res.data.product || []).map(x => ({ id: `${x.supplies_no}&&${x.category_id}`, ...x }));
            setTableData(tempProducts);
            setTableMeta({
                pageSize,
                pageIndex,
                total: res.meta.total,
            });
        } catch (err) {
            if (notificationSystem) {
                notificationSystem.addNotification({
                    title: 'Failed',
                    message: catchError(err),
                    level: 'error',
                });
            }
            setTableData([]);
            setTableMeta({
                pageSize,
                pageIndex,
                total: 0,
            });
        } finally {
            hideProgress();
            setIsFetch(false);
        }
    };

    const handleUpdateProduct = async (payload, isBulk = false) => {
        try {
            showProgress();
            const res = await suppliesApi.updateProduct(payload);
            if (isBulk) setSelectedProduct([]);
            setTableMeta(prev => ({ ...prev, key: prev.key + 1 }));
            notificationSystem.addNotification({
                title: 'Success',
                message: res.message,
                level: 'success',
            });
        } catch (err) {
            if (!isBulk && payload.appear_in_front === 1) {
                setModal({
                    wording: (
                        <div>
                            Can’t display 5 products in front page. Maximum
                            {' '}
                            <strong>4 products</strong>
                        </div>
                    ),
                    cancelText: 'Close',
                    show: true,
                });
            } else if (notificationSystem) {
                notificationSystem.addNotification({
                    title: 'Failed',
                    message: catchError(err),
                    level: 'error',
                });
            }
            hideProgress();
        }
    };

    const handleUpdateProductCategory = async (payload, isBulk = false) => {
        try {
            showProgress();
            const res = await suppliesApi.updateProductCategory(payload);
            if (isBulk) setSelectedProduct([]);
            setTableMeta(prev => ({ ...prev, key: prev.key + 1 }));
            notificationSystem.addNotification({
                title: 'Success',
                message: res.message,
                level: 'success',
            });
        } catch (err) {
            if (notificationSystem) {
                notificationSystem.addNotification({
                    title: 'Failed',
                    message: catchError(err),
                    level: 'error',
                });
            }
            hideProgress();
        }
    };
    const handleDeleteProduct = async (payload, isBulk = false) => {
        try {
            const res = await suppliesApi.deleteProductCategory(payload);
            if (isBulk) setSelectedProduct([]);
            setTableMeta(prev => ({ ...prev, key: prev.key + 1 }));
            notificationSystem.addNotification({
                title: 'Success',
                message: res.message,
                level: 'success',
            });
        } catch (err) {
            if (notificationSystem) {
                notificationSystem.addNotification({
                    title: 'Failed',
                    message: catchError(err),
                    level: 'error',
                });
            }
            hideProgress();
        }
    };

    const handleAction = (act, value) => {
        switch (act) {
            case ACTION.APPEAR:
                handleUpdateProductCategory({ appear_in_front: 1, item_category_ids: value });
                break;
            case ACTION.DISAPPEAR:
                handleUpdateProductCategory({ appear_in_front: 0, item_category_ids: value });
                break;
            case ACTION.ACTIVATE:
                handleUpdateProduct({ active_status: 1, item_ids: value });
                break;
            case ACTION.DEACTIVATE:
                handleUpdateProduct({ active_status: 0, item_ids: value });
                break;
            case ACTION.DELETE:
                handleDeleteProduct({ item_category_ids: value });
                break;
            default:
                break;
        }
    };

    const handleApply = () => {
        const modalVal = { show: true };
        const suppliesNo = selectedProduct.map(x => x.split('&&')[0]);
        const categoriesId = selectedProduct.map(x => +x.split('&&')[1]);
        switch (action) {
            case ACTION.APPEAR:
                modalVal.wording = categoriesId.length <= 4 ? (
                    <div>
                        Are you sure to display
                        {' '}
                        <strong>
                            {selectedProduct.length}
                            {' '}
                            products
                        </strong>
                        {' '}
                        on the front page?
                    </div>
                ) : (
                    <div>
                        Can’t display
                        {' '}
                        {categoriesId.length}
                        {' '}
                        products in front page. Maximum
                        {' '}
                        <strong>4 products</strong>
                    </div>
                );
                modalVal.cancelText = categoriesId.length <= 4 ? 'Cancel' : 'Close';
                if (categoriesId.length <= 4) {
                    modalVal.act = (cb) => {
                        handleUpdateProductCategory({ appear_in_front: 1, item_category_ids: categoriesId }, true);
                        setModal(prev => ({ ...prev, show: false }));
                        cb();
                    };
                }
                break;
            case ACTION.DISAPPEAR:
                modalVal.wording = (
                    <div>
                        Are you sure to not display
                        {' '}
                        <strong>
                            {selectedProduct.length}
                            {' '}
                            products
                        </strong>
                        {' '}
                        {' '}
                        on the front page?
                    </div>
                );
                modalVal.cancelText = 'Cancel';
                modalVal.act = (cb) => {
                    handleUpdateProductCategory({ appear_in_front: 0, item_category_ids: categoriesId }, true);
                    setModal(prev => ({ ...prev, show: false }));
                    cb();
                };
                break;
            case ACTION.ACTIVATE:
                modalVal.wording = (
                    <div>
                        Are you sure to activate
                        {' '}
                        <strong>
                            {selectedProduct.length}
                            {' '}
                            products
                        </strong>
                        {' '}
                        {' '}
                        on the front page?
                    </div>
                );
                modalVal.cancelText = 'Cancel';
                modalVal.act = (cb) => {
                    handleUpdateProduct({ active_status: 1, item_ids: suppliesNo }, true);
                    setModal(prev => ({ ...prev, show: false }));
                    cb();
                };
                break;
            case ACTION.DEACTIVATE:
                modalVal.wording = (
                    <div>
                        Are you sure to deactivate
                        {' '}
                        <strong>
                            {selectedProduct.length}
                            {' '}
                            products
                        </strong>
                        {' '}
                        {' '}
                        on the front page?
                    </div>
                );
                modalVal.cancelText = 'Cancel';
                modalVal.act = (cb) => {
                    handleUpdateProduct({ active_status: 0, item_ids: suppliesNo }, true);
                    setModal(prev => ({ ...prev, show: false }));
                    cb();
                };
                break;
            case ACTION.DELETE:
                modalVal.wording = (
                    <div>
                        Are you sure to delete
                        {' '}
                        <strong>
                            {selectedProduct.length}
                            {' '}
                            products
                        </strong>
                        {' '}
                        {' '}
                        on the front page?
                    </div>
                );
                modalVal.cancelText = 'Cancel';
                modalVal.act = (cb) => {
                    handleDeleteProduct({ item_category_ids: categoriesId }, true);
                    setModal(prev => ({ ...prev, show: false }));
                    cb();
                };
                break;
            default:
                modalVal.wording = '';
                break;
        }
        setModal(modalVal);
    };

    const showImage = () => {
        setModal({
            show: true,
            title: file.name,
            cancelText: '',
            wording: (
                <div className="d-flex" style={{ justifyContent: 'center' }}>
                    <img src={file.fileUrl} alt="section-img" style={{ height: 140 }} />
                </div>
            ),
        });
    };

    const selectedRows = useMemo(() => selectedProduct.reduce((acc, id) => ({ ...acc, [id]: true }), {}), [selectedProduct]);

    useEffect(() => {
        if (!isFetch) onFetch({ ...tableMeta, pageIndex: 0 });
    }, [query]);

    return (
        <div>
            <section className="panel">
                <div className="panel-heading">
                    <h4 className="panel-title" style={{ paddingTop: '10px', fontWeight: 600, textTransform: 'capitalize' }}>
                        {TITLE_TYPE[type]}
                    </h4>
                </div>
                <div className="panel-heading">
                    <div className="d-flex" style={{ justifyContent: 'space-between', alignItems: 'center', width: '100%' }}>
                        <div>
                            <i className="fa fa-info-circle" style={{ top: 'unset' }} />
                            {' '}
                            {`Upload gambar section ${TITLE_TYPE[type].toLowerCase()}. Ukuran maks 250kb (Default: majoo_image)`}
                        </div>
                        <div className="d-flex" style={{ alignItems: 'center', gap: 8 }}>
                            <div style={{ color: '#66D4D3', cursor: 'pointer' }} onClick={showImage} role="button">
                                {file.name}
                            </div>
                            <FileUpload
                                label="Edit"
                                type="button"
                                apiUrl="supplies/category_image"
                                acceptUpload="image/png"
                                category={TITLE_TYPE[type]}
                                changeEvent={() => onFetch(tableMeta)}
                                onError={(err) => {
                                    notificationSystem.addNotification({
                                        title: 'Upload failed',
                                        message: err,
                                        level: 'error',
                                    });
                                }}
                            />
                        </div>
                    </div>
                </div>
                <div className="panel-heading table-header">
                    <div className="d-flex" style={{ justifyContent: 'space-between', alignItems: 'center', width: '100%' }}>
                        <div className="d-flex" style={{ gap: 8 }}>
                            {Boolean(selectedProduct.length > 1) && (
                                <React.Fragment>
                                    <div style={{ width: '200px' }}>
                                        <InputSelect
                                            data={ActionOptions}
                                            value={action}
                                            changeEvent={value => setAction(value)}
                                            placeholder="Select Action"
                                        />
                                    </div>
                                    <button type="button" className="btn btn-light" style={{ height: 36 }} onClick={handleApply}>Apply</button>
                                </React.Fragment>
                            )}
                        </div>
                        <div className="d-flex" style={{ gap: '8px' }}>
                            <div style={{ width: '200px' }}>
                                <InputSelect
                                    data={StatusOptions}
                                    value={query.status}
                                    changeEvent={value => setQuery(prev => ({ ...prev, status: value }))}
                                    placeholder="All Status"
                                />
                            </div>
                            <InputText
                                placeholder="Search keyword"
                                changeEvent={value => setQuery(prev => ({ ...prev, search: value }))}
                                wait={500}
                            />
                        </div>
                    </div>
                    <div style={{
                        marginTop: 12,
                        padding: '8px 16px',
                        color: '#111111',
                        background: '#E6F8F9',
                        border: '1px solid #00B7B5',
                        borderRadius: '4px',
                    }}
                    >
                        You must display
                        {' '}
                        <strong>4 product,</strong>
                        {' '}
                        if the data is empty, it will show 4 product at your top row.
                    </div>
                </div>
                <div className="panel-body">
                    <Table
                        key={`table-supplies-${tableMeta.key}`}
                        fetchData={onFetch}
                        columns={TableColumn(handleAction)}
                        onSelectedChange={setSelectedProduct}
                        data={tableData}
                        pageIndex={tableMeta.pageIndex}
                        totalData={tableMeta.total}
                        searchQuery={query.search}
                        isLoading={isFetch}
                        selectedIds={selectedRows}
                    />
                </div>
            </section>
            <ModalPopup
                headerTitle={modal.title || 'Confirmation'}
                confirmText={modal.title ? '' : 'Apply'}
                cancelText={modal.cancel}
                show={modal.show}
                hideBack={modal.hideBack}
                onHide={() => setModal({ wording: '', show: false })}
                confirmHandle={modal.act}
            >
                {modal.wording}
            </ModalPopup>
        </div>
    );
};

export default Home;
