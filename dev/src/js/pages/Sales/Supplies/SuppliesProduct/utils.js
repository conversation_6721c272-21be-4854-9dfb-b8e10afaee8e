import React from 'react';
import RowMenuAction from './components/RowMenuAction';
import Switch from '../../../../components/form/Switch';
import { ACTION } from './enum';
import PriceColumn from '../../../../components/table/components/PriceColumn';
import InputCheckbox from '../../../../components/form/InputCheckbox';

export const TableColumn = onRowAction => [
    {
        id: 'selection',
        Header: ({
            getToggleAllPageRowsSelectedProps, toggleAllPageRowsSelected,
        }) => (
            <div className="d-flex justify-content-center align-items-center">
                <InputCheckbox
                    {...getToggleAllPageRowsSelectedProps({
                        indeterminate: false,
                    })}
                    changeEvent={checked => toggleAllPageRowsSelected(checked)}
                />
            </div>
        ),
        Cell: ({ row: { getToggleRowSelectedProps, toggleRowSelected } }) => (
            <div className="d-flex justify-content-center align-items-center">
                <InputCheckbox
                    {...getToggleRowSelectedProps()}
                    changeEvent={checked => toggleRowSelected(checked)}
                />
            </div>
        ),
        unsortable: true,
    },
    {
        Header: 'Product name',
        accessor: 'name',
    },
    {
        Header: 'Outlet Name',
        accessor: 'outlet_name',
    },
    {
        Header: 'QTY Sold',
        accessor: 'quantity_sold',
    },
    {
        Header: 'Last Update',
        accessor: 'updated_at',
    },
    {
        Header: 'Appear in Front',
        accessor: 'appear_in_front',
        Cell: (props) => {
            const { value, row: { original } } = props;
            return (
                <Switch checked={Boolean(value)} changeEvent={val => onRowAction(val ? ACTION.APPEAR : ACTION.DISAPPEAR, [original.category_id])} noanimation />
            );
        },
    },
    {
        Header: 'Status',
        accessor: 'active_status',
        Cell: (props) => {
            const { value } = props;
            return (
                <div className={`supplies-status-label ${value ? 'active' : 'inactive'}`}>{value ? 'Active' : 'Inactive'}</div>
            );
        },
    },
    {
        Header: 'Action',
        maxWidth: 100,
        accessor: 'action',
        Cell: (props) => {
            const { row: { original } } = props;
            return (
                <RowMenuAction
                    options={[
                        {
                            title: 'Active',
                            onClick: () => onRowAction(ACTION.ACTIVATE, [original.supplies_no]),
                        },
                        {
                            title: 'Non Active',
                            onClick: () => onRowAction(ACTION.DEACTIVATE, [original.supplies_no]),
                        },
                        {
                            title: 'Delete',
                            onClick: () => onRowAction(ACTION.DELETE, [original.category_id]),
                        },
                    ]}
                />
            );
        },
    },
];

export const TableAddProductColumn = [
    {
        id: 'selection',
        Header: ({
            getToggleAllPageRowsSelectedProps, toggleAllPageRowsSelected, data, selectedFlatRows,
        }) => {
            const selectedRows = selectedFlatRows.filter(({ original }) => !original.disabled);
            const isChecked = selectedRows.length && selectedRows.length === data.filter(row => !row.disabled).length;

            return (
                <div className="d-flex justify-content-center align-items-center">
                    <InputCheckbox
                        {...getToggleAllPageRowsSelectedProps({
                            indeterminate: false,
                        })}
                        checked={isChecked}
                        disabled={data.every(row => row.disabled)}
                        changeEvent={checked => toggleAllPageRowsSelected(checked)}
                    />
                </div>
            );
        },
        Cell: ({ row: { getToggleRowSelectedProps, toggleRowSelected, original } }) => (
            <div className="d-flex justify-content-center align-items-center">
                <InputCheckbox
                    {...(!original.disabled || original.isSelected ? { ...getToggleRowSelectedProps() } : {})}
                    disabled={original.disabled}
                    changeEvent={checked => toggleRowSelected(checked)}
                />
            </div>
        ),
        unsortable: true,
        sticky: 'left',
    },
    {
        Header: 'Product name',
        accessor: 'name',
        unsortable: true,
    },
    {
        Header: 'Outlet Name',
        accessor: 'outlet_name',
        unsortable: true,
    },
    {
        Header: 'Category',
        accessor: 'category_name',
        unsortable: true,
    },
    {
        Header: 'Price',
        accessor: 'price',
        colWidth: 150,
        unsortable: true,
        Cell: PriceColumn,
    },
];

export const StatusOptions = [
    { value: '', option: 'All Status' },
    { value: '1', option: 'Active' },
    { value: '0', option: 'Inactive' },
];

export const ActionOptions = [
    { value: ACTION.ACTIVATE, option: 'Active' },
    { value: ACTION.DEACTIVATE, option: 'Non Activated' },
    { value: ACTION.APPEAR, option: 'Appear in front (On)' },
    { value: ACTION.DISAPPEAR, option: 'Disappear' },
    { value: ACTION.DELETE, option: 'Delete' },
];
