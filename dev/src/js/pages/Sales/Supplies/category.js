import React, { Component } from 'react';
import PropTypes from 'prop-types';
import CoreHOC from '../../../core/CoreHOC';
import Table from '../../../components/retina/table/Table';
import SidePopup from '../../../components/sidepopup/Container';
import InputText from '../../../components/form/InputText';
import InputTextArea from '../../../components/form/InputTextArea';
import ImageUpload from '../../../components/form/ImageUpload';
import { catchError } from '../../../utils/helper';
import {
    getSuppliesCategory,
    updateSuppliesCategory,
    addSuppliesCategory,
    getSuppliesTypesCategory,
} from '../../../data/sales/master';
import { TypeAction } from './resources/enum';
import { COLUMN_CATEGORY, MAX_FILE_SIZE } from './resources/meta';
import SelectMultiple from '../../../components/form/SelectMultiple';

class Supplies extends Component {
    static propTypes = {
        assignCalendar: PropTypes.func,
        assignButtons: PropTypes.func,
        notificationSystem: PropTypes.shape({
            addNotification: PropTypes.func,
        }),
        assignFilterColoumn: PropTypes.func,
        assignRangeDate: PropTypes.func,
        showProgress: PropTypes.func,
        hideProgress: PropTypes.func,
    }

    static defaultProps = {
        assignCalendar: () => { },
        assignButtons: () => { },
        notificationSystem: null,
        assignFilterColoumn: () => { },
        assignRangeDate: () => { },
        showProgress: () => {},
        hideProgress: () => {},
    }

    constructor(props) {
        super(props);

        this.state = {
            name: '',
            listType: [],
            desc: '',
            id: '',
            no: '',
            type: 'edit',
            titleSide: 'Add Supplies Category',
            imageURL: '',
            listTypeCategory: [],
            search: '',
            isFetch: false,
            dataSource: {
                list: [],
                meta: {
                    pageIndex: 0,
                    pageSize: 10,
                    total: 0,
                },
            },
        };
    }

    componentWillMount() {
        const {
        assignCalendar, assignButtons, assignFilterColoumn, assignRangeDate,
        } = this.props;
        const { dataSource: { meta } } = this.state;
        this.getCategoryTypes();
        assignCalendar(null, null, null);
        assignButtons([{ type: 'primary', content: <span> Add Category </span>, action: () => { this.callManageHandler(); } }]);
        assignFilterColoumn([]);
        assignRangeDate([]);

        this.imageURLChangeHandler = this.imageURLChangeHandler.bind(this);
        this.handleFetchTable(meta);
    }

    getCategoryTypes = async () => {
        const { notificationSystem } = this.props;
        try {
            const { data } = await getSuppliesTypesCategory();
            const listTypeCategory = data.map(({ id, name }) => ({ value: +id, label: name }));
            this.setState({
                listTypeCategory,
            });
        } catch (err) {
            notificationSystem.addNotification({
                title: 'Get data types failed',
                message: catchError(err),
                level: 'error',
            });
        }
    }

    handleFetchTable = async (state) => {
        const { notificationSystem, showProgress, hideProgress } = this.props;
        const {
            pageIndex, pageSize,
        } = state;

        const { search } = this.state;

        const payload = {
            limit: pageSize,
            page: parseInt(pageIndex, 10) + 1,
            ...search && { search },
        };
        this.setState({ isFetch: true });
        try {
            showProgress();
            const { data, meta: { total } } = await getSuppliesCategory(payload);
            const result = data.map(val => ({
                ...val,
                business_type_label: val.business_type ? val.business_type.join(', ') : '-',
                description_label: val.description ? val.description : '-',
            }));
            this.setState({
                isFetch: false,
                dataSource: {
                    list: result,
                    meta: {
                        pageIndex,
                        pageSize,
                        total,
                    },
                },
            });
        } catch (err) {
            notificationSystem.addNotification({
                title: 'Get data failed',
                message: catchError(err),
                level: 'error',
            });
            this.setState({
                isFetch: false,
                dataSource: {
                    list: [],
                    meta: {
                        pageIndex,
                        pageSize,
                        total: 0,
                    },
                },
            });
        } finally {
            hideProgress();
        }
    }

    callManageHandler = () => {
        this.setState({
            name: '',
            desc: '',
            listType: [],
            type: TypeAction.add,
            no: '',
            titleSide: 'Add Supplies Category',
            imageURL: '',
        }, () => {
        this.sidePop.showPopup();
        });
    }

    callEditDetailHandler = (val) => {
        const data = val.original;
        const { listTypeCategory } = this.state;
        const tempArr = data.category_types;
        const listType = listTypeCategory.filter(({ value }) => tempArr.some(e => +e === value));
        this.setState({
            name: data.name,
            listType,
            desc: data.description,
            id: data.id,
            no: data.no,
            type: TypeAction.edit,
            titleSide: 'Update Supplies Category',
            imageURL: data.image_url,
        }, () => {
            this.sidePop.showPopup();
        });
    }

    saveHandler = (type) => {
        const {
            name,
            listType: listRaw,
            desc,
            id,
            imageURL,
            dataSource: { meta },
        } = this.state;
        const listType = listRaw.map(val => val.value);
        const { notificationSystem } = this.props;
        switch (type) {
            case TypeAction.add:
                addSuppliesCategory({
                    name,
                    desc,
                    image_url: imageURL,
                    category_types: listType,
                })
                    .then(() => {
                        notificationSystem.addNotification({
                            title: 'Berhasil',
                            message: 'Berhasil Add category',
                            level: 'success',
                        });
                        this.handleFetchTable(meta);
                    })
                    .catch((err) => {
                        notificationSystem.addNotification({
                            title: 'Failed Add Data',
                            message: catchError(err),
                            level: 'error',
                        });
                    })
                    .finally(() => {
                        this.sidePop.hidePopup();
                    });
                break;
            case TypeAction.edit:
                updateSuppliesCategory({
                    name,
                    desc,
                    id,
                    image_url: imageURL,
                    category_types: listType,
                })
                    .then(() => {
                        notificationSystem.addNotification({
                            title: 'Berhasil',
                            message: 'Berhasil Update category',
                            level: 'success',
                        });
                        this.handleFetchTable(meta);
                    })
                    .catch((err) => {
                        notificationSystem.addNotification({
                            title: 'Failed Update Data',
                            message: catchError(err),
                            level: 'error',
                        });
                    })
                    .finally(() => {
                        this.sidePop.hidePopup();
                    });
                break;
            default:
                break;
        }
    }

    imageURLChangeHandler = (value) => {
        const { hideProgress } = this.props;
        hideProgress();
        this.setState({ imageURL: value });
    }

    handleChangeTypeCategory = (val) => {
        this.setState({
            listType: [...val],
        });
    }

    handleOnErrorCrop = (err) => {
        const { notificationSystem, hideProgress } = this.props;
        this.setState({
            imageURL: '',
        });
        hideProgress();
        notificationSystem.addNotification({
            title: 'Failed Upload Image',
            message: catchError(err),
            level: 'error',
        });
    }

    handleOnUploadImage = () => {
        const { showProgress } = this.props;
        showProgress();
        this.setState({
            imageURL: '',
        });
    }

    handleOnSearch = (val) => {
        const { dataSource: { meta } } = this.state;
        this.setState({
            search: val,
        }, () => this.handleFetchTable({ ...meta, pageIndex: 0 }));
    }

    render() {
        const {
            type,
            titleSide,
            name,
            listType,
            desc,
            no,
            imageURL,
            listTypeCategory,
            search,
            isFetch,
            dataSource: {
                list,
                meta,
            },
        } = this.state;
        return (
        <div>
            <section className="panel">
            <div className="panel-heading">
                <h4 className="panel-title">Supplies Category</h4>
            </div>
            <div className="panel-body">
                <div>
                    <div className="panel-heading table-header">
                        <div className="row">
                            <div className="col-md-offset-8 col-xs-12 col-sm-4">
                                <InputText
                                    classes="filter"
                                    placeholder="Cari ..."
                                    changeEvent={this.handleOnSearch}
                                    wait={500}
                                />
                            </div>
                        </div>
                    </div>
                    <div className="panel-body">
                        <Table
                            columns={COLUMN_CATEGORY}
                            data={list}
                            pageIndex={meta.pageIndex}
                            rowLimit={meta.pageSize}
                            totalData={meta.total}
                            isLoading={isFetch}
                            searchQuery={search}
                            onRowClick={val => this.callEditDetailHandler(val)}
                            fetchData={this.handleFetchTable}
                        />
                    </div>
                </div>
            </div>
            </section>
            <SidePopup
            ref={(c) => { this.sidePop = c; }}
            width={600}
            type="edit"
            saveHandle={() => this.saveHandler(type)}
            isDisable={!name || !imageURL}
            >
            <h4 className="side-popup-title">
                {titleSide}
            </h4>
                <div className="row mb-sm">
                    <div className="col-sm-12">
                        <InputText
                            label="Category Name"
                            placeholder="Category Name"
                            value={name}
                            changeEvent={value => this.setState({ name: value })}
                        />
                    </div>
                </div>
                {type === TypeAction.edit && (
                    <div className="row mb-sm">
                        <div className="col-sm-12">
                        <InputText label="Category Number" placeholder="Category Number" value={no} disabled />
                        </div>
                    </div>
                )}
                <div className="row mb-sm">
                <div className="col-sm-12">
                    <InputTextArea label="Category Description" placeholder="Category Description" value={desc} changeEvent={value => this.setState({ desc: value })} />
                </div>
                </div>
                <div className="row mb-sm">
                    <div className="col-sm-12" style={{ zIndex: '1000' }}>
                        <SelectMultiple
                            options={listTypeCategory}
                            value={listType}
                            selector="label"
                            placeholder="Business Type"
                            label="Business Type"
                            changeEvent={val => this.handleChangeTypeCategory(val)}
                        />
                    </div>
                </div>
                <div className="row mb-sm">
                    <div className="col-sm-3">
                        <ImageUpload
                            changeEvent={this.imageURLChangeHandler}
                            imageURL={imageURL}
                            imageWidth={450}
                            imageHeight={304}
                            imageCrop
                            maxFileSize={MAX_FILE_SIZE}
                            onError={this.handleOnErrorCrop}
                            onUploading={this.handleOnUploadImage}
                        />
                    </div>
                </div>
            </SidePopup>
        </div>
        );
    }
}

export default CoreHOC(Supplies);
