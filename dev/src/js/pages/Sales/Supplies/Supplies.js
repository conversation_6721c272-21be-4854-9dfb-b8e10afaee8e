import React, { Component } from 'react';
import PropTypes from 'prop-types';
import update from 'immutability-helper';
import CoreHOC from '../../../core/CoreHOC';
import Table from '../../../components/retina/table/Table';
import SidePopup from '../../../components/sidepopup/Container';
import ModalPopup from '../../../components/modalpopup/Container';
import InputSelect from '../../../components/form/InputSelect';
import { PaymentStatus, DeliveryStatus } from './resources/enum';

/* API */
import {
    getSupplies, updateSupplies, detailSupplies,
} from '../../../data/sales';

/* COMPONENT */

import OrderDetail from './resources/OrderDetail';
import ChangeStatusPopup from './resources/ChangeStatusPopup';
import {
    shipmentStatusChoices, paymentStatusChoices, confirmationTypes,
} from './Constant';
import { TABLE_META_SUPPLIES, FILTERTYPE } from './resources/meta';
import { catchError } from '../../../utils/helper';
import { getFilterValue, getUpdatedFilterValue } from '../../../utils/table.util';
import InputText from '../../../components/form/InputText';

class Supplies extends Component {
    static propTypes = {
        assignCalendar: PropTypes.func,
        assignButtons: PropTypes.func,
        showProgress: PropTypes.func,
        hideProgress: PropTypes.func,
        notificationSystem: PropTypes.shape({
            addNotification: PropTypes.func,
        }),
        calendar: PropTypes.shape({
            start: PropTypes.string,
            end: PropTypes.string,
        }),
    }

    static defaultProps = {
        assignCalendar: () => {},
        assignButtons: () => {},
        showProgress: () => {},
        hideProgress: () => {},
        notificationSystem: {
            addNotification: null,
        },
        calendar: ({
            start: '2010-10-10',
            end: '2010-10-10',
        }),
    }

    constructor(props) {
        super(props);
        const { calendar: { start, end } } = props;
        this.state = {
            selectedData: undefined,
            confirmationStatus: {
                type: '',
                choices: [],
                value: '',
                shippingNumber: '',
            },
            tableFilter: [
                { id: FILTERTYPE.SHIPMENT, value: 'all' },
                { id: FILTERTYPE.PAYMENT, value: 'all' },
                { id: FILTERTYPE.DATE, value: { start, end } },
                { id: FILTERTYPE.ALL, value: '' },
            ],
            isFetch: false,
            tableData: [],
            tableMeta: {
                pageIndex: 0,
                pageSize: 10,
                total: 0,
            },
        };
    }

    componentDidMount = () => {
        const {
            assignCalendar, assignButtons,
        } = this.props;
        const { tableMeta } = this.state;
        assignButtons();
        assignCalendar(null, null, (start, end) => {
            this.changeTableFilter({ start, end }, FILTERTYPE.DATE);
            assignCalendar(start, end);
        });
        this.handleFetchTable(tableMeta);
    }

    changeTableFilter = (val, type) => {
        const { tableFilter, tableMeta } = this.state;
        const updatedFilterValue = getUpdatedFilterValue(tableFilter, type, val);

        this.setState({ tableFilter: updatedFilterValue, [type]: val }, () => this.handleFetchTable({ ...tableMeta, pageIndex: 0 }));
    }

    fetchDetailSupplies = (item) => {
        const { notificationSystem } = this.props;
        if (item.number === '') {
            notificationSystem.addNotification({
                title: 'Get data detail transaction failed',
                message: 'ID ORDER INVALID',
                level: 'error',
            });
            return;
        }

        const payload = {
            id: item.number.replaceAll('/', '-'),
        };

        detailSupplies(payload)
            .then((res) => {
                const { status, data, msg } = res;
                if (!status) {
                    throw new Error(msg);
                }
                const { detail } = data;
                const subtotal = detail.map(product => +product.bill_subtotal)
                    .reduce((prev, next) => prev + next);
                const trxSummary = {
                    subtotal,
                    additional: +data.additional_cost,
                    shipping: +data.shipping_fee,
                    tax: +data.tax_fee,
                    discount: +data.discount_fee,
                    total: +data.bill_total,
                    paylater: +data.paylater_fee,
                };
                const newItem = {
                    ...data,
                    purchasedItems: detail,
                    summaryData: trxSummary,
                    payment_status: item.payment_status.toString(),
                    shipment_status: item.shipment_status.toString(),
                };
                this.setState({
                    selectedData: newItem,
                }, () => {
                    this.orderSidebar.showPopup();
                });
            })
            .catch((err) => {
                notificationSystem.addNotification({
                    title: 'Get data detail transaction failed',
                    message: catchError(err),
                    level: 'error',
                });
            });
    }

    changePaymentStatus = () => {
        const { selectedData } = this.state;
        const choices = [
            {
                id: PaymentStatus.termOfPayment,
                label: 'Waiting Payment',
            }, {
                id: PaymentStatus.lunas,
                label: 'Lunas',
            }, {
                id: PaymentStatus.jatuhTempo,
                label: 'Jatuh Tempo',
            },
        ];

        const newConfirmationStatus = {
            type: 'payment',
            choices,
            value: selectedData.payment_status,
            shippingNumber: '',
        };

        this.setState({
            confirmationStatus: newConfirmationStatus,
        }, () => {
            this.popupChangeStatus.showPopup();
        });
    }

    changeShipmentStatus = () => {
        const { selectedData } = this.state;
        const choices = [
            {
                id: DeliveryStatus.menungguKonfirmasi,
                label: 'Menunggu Konfirmasi',
            }, {
                id: DeliveryStatus.diproses,
                label: 'Pesanan Diproses',
            }, {
                id: DeliveryStatus.dalamPengiriman,
                label: 'Dalam Pengiriman',
            }, {
                id: DeliveryStatus.diterima,
                label: 'Diterima',
            },
        ];

        const newConfirmationStatus = {
            type: 'shipment',
            choices,
            value: selectedData.shipment_status,
            shippingNumber: '',
        };

        this.setState({
            confirmationStatus: newConfirmationStatus,
        }, () => {
            this.popupChangeStatus.showPopup();
        });
    }

    changeConfirmationStatus = (val) => {
        const { confirmationStatus } = this.state;
        const newConfirmationStatus = update(confirmationStatus, {
            value: {
                $set: val,
            },
        });

        this.setState({
            confirmationStatus: newConfirmationStatus,
        });
    }

    changeShipmentNumber = (val) => {
        const { confirmationStatus } = this.state;
        const newConfirmationStatus = update(confirmationStatus, {
            shippingNumber: {
                $set: val,
            },
        });

        this.setState({
            confirmationStatus: newConfirmationStatus,
        });
    }

    confirmPopupStatus = () => {
        const { notificationSystem } = this.props;
        const { confirmationStatus, selectedData, tableMeta } = this.state;

        if (confirmationStatus.type === confirmationTypes.SHIPMENT && String(confirmationStatus.value) === String(selectedData.shipment_status)) {
            return;
        }

        if (confirmationStatus.type === confirmationTypes.PAYMENT && String(confirmationStatus.value) === String(selectedData.payment_status)) {
            return;
        }

        let payload = {
            id: selectedData.id,
            note: selectedData.note === null ? '' : selectedData.note,
            status: '31',
        };

        if (confirmationStatus.type === confirmationTypes.PAYMENT) {
            payload = {
                ...payload,
                payment_status: confirmationStatus.value,
                shipment_status: selectedData.shipment_status,
            };
        } else {
            payload = {
                ...payload,
                shipment_status: confirmationStatus.value,
                payment_status: selectedData.payment_status,
            };
        }

        updateSupplies(payload)
            .then((response) => {
                const { status, msg } = response;
                if (!status) {
                    throw new Error(msg);
                }
                notificationSystem.addNotification({
                    title: 'Berhasil',
                    message: 'update supplies success',
                    level: 'success',
                });
                this.popupChangeStatus.hidePopup();
                this.orderSidebar.hidePopup();
                this.handleFetchTable(tableMeta);
            })
            .catch((err) => {
                notificationSystem.addNotification({
                    title: 'Update supplies failed',
                    message: catchError(err),
                    level: 'error',
                });
            });
    }

    handleFetchTable = async (state) => {
        const {
            pageIndex, pageSize,
        } = state;
        const { tableFilter } = this.state;
        const {
            showProgress, hideProgress, notificationSystem,
        } = this.props;
        const dateFilter = getFilterValue(tableFilter, FILTERTYPE.DATE);
        const paymentFilter = getFilterValue(tableFilter, FILTERTYPE.PAYMENT);
        const shipmentFilter = getFilterValue(tableFilter, FILTERTYPE.SHIPMENT);
        const searchFilter = getFilterValue(tableFilter, FILTERTYPE.ALL);

        const payload = {
            start: dateFilter.start.replaceAll('/', '-'),
            end: dateFilter.end.replaceAll('/', '-'),
            limit: pageSize,
            page: parseInt(pageIndex, 10) + 1,
            search: searchFilter,
            payment_id: paymentFilter !== 'all' ? paymentFilter : '',
            shipment_id: shipmentFilter !== 'all' ? shipmentFilter : '',
        };
        showProgress();
        this.setState({ isFetch: true });
        try {
            const response = await getSupplies(payload);
            const {
                data, meta,
            } = response;
            this.setState({
                isFetch: false,
                tableData: data.summary,
                tableMeta: {
                    pageIndex,
                    pageSize,
                    total: meta.total,
                },
            });
        } catch (error) {
            notificationSystem.addNotification({
                title: 'Get data detail transaction failed',
                message: catchError(error),
                level: 'error',
            });
            this.setState({
                isFetch: false,
                tableData: [],
                tableMeta: {
                    pageIndex,
                    pageSize,
                    total: 0,
                },
            });
        }
        hideProgress();
    }

    render() {
        const {
            selectedData, tableFilter, confirmationStatus,
            tableData, tableMeta, isFetch,
        } = this.state;
        const paymentStatus = getFilterValue(tableFilter, FILTERTYPE.PAYMENT);
        const shipmentStatus = getFilterValue(tableFilter, FILTERTYPE.SHIPMENT);
        const search = getFilterValue(tableFilter, FILTERTYPE.ALL);

        return (
            <div>
                <section className="panel">
                    <div className="panel-heading">
                        <h4 className="panel-title">Supplies</h4>
                    </div>
                    <div>
                        <div className="panel-heading table-header">
                            <div className="row">
                                <div className="col-xs-12 col-sm-4">
                                    <InputSelect
                                        data={paymentStatusChoices}
                                        value={paymentStatus}
                                        changeEvent={val => this.changeTableFilter(val, FILTERTYPE.PAYMENT)}
                                    />
                                </div>
                                <div className="col-xs-12 col-sm-4">
                                    <InputSelect
                                        data={shipmentStatusChoices}
                                        value={shipmentStatus}
                                        changeEvent={val => this.changeTableFilter(val, FILTERTYPE.SHIPMENT)}
                                    />
                                </div>
                                <div className="col-xs-12 col-sm-4">
                                    <InputText
                                        classes="filter"
                                        placeholder="Cari ..."
                                        changeEvent={val => this.changeTableFilter(val, FILTERTYPE.ALL)}
                                        wait={500}
                                    />
                                </div>
                            </div>
                        </div>
                        <div className="panel-body">
                            <Table
                                columns={TABLE_META_SUPPLIES}
                                data={tableData}
                                pageIndex={tableMeta.pageIndex}
                                rowLimit={tableMeta.pageSize}
                                totalData={tableMeta.total}
                                searchQuery={search}
                                isLoading={isFetch}
                                onRowClick={({ original }) => this.fetchDetailSupplies(original)}
                                fetchData={this.handleFetchTable}
                            />
                        </div>
                    </div>
                </section>
                <SidePopup
                    ref={(c) => { this.orderSidebar = c; }}
                    width={1200}
                    btnSaveText="Change Payment Status"
                    btnDraftText="Change Shipment Status"
                    saveHandle={this.changePaymentStatus}
                    saveDraft={this.changeShipmentStatus}
                >
                    <OrderDetail
                        data={selectedData}
                    />
                </SidePopup>
                <ModalPopup
                    type="upload"
                    confirmText="Save"
                    cancelText="Cancel"
                    confirmHandle={this.confirmPopupStatus}
                    title={confirmationStatus.type === confirmationTypes.PAYMENT ? 'Change Payment Status' : 'Change Shipment Status'}
                    width={400}
                    ref={(c) => { this.popupChangeStatus = c; }}
                >
                    <ChangeStatusPopup
                        type={confirmationStatus.type}
                        choices={confirmationStatus.choices}
                        value={confirmationStatus.value}
                        changeHandler={this.changeConfirmationStatus}
                        shipmentNumber={confirmationStatus.shippingNumber}
                        changeShipmentNumber={this.changeShipmentNumber}
                    />
                </ModalPopup>
            </div>
        );
    }
}

export default CoreHOC(Supplies);
