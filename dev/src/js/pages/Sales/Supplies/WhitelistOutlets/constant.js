import React from 'react';
import Franchisor from './components/Franchisor';

export const TABLE_META_OUTLET = onSelect => [
    {
        Header: 'No',
        accessor: 'no',
        colWidth: 80,
    },
    {
        Header: 'Outlet',
        accessor: 'outlet_name',
    },
    {
        Header: 'Location',
        accessor: 'address',
        colMinWidth: 200,
    },
    {
        Header: 'Last Activation Date',
        accessor: 'last_activation',
    },
    {
        Header: 'MDR',
        accessor: 'mdr_percent',
        Cell: ({ value, row: { original } }) => (original.status === 1 ? `${value}%` : '-'),
    },
    {
        Header: 'Franchisor',
        accessor: 'status_franchise',
        Cell: Franchisor,
    },
    {
        Header: 'Status',
        accessor: 'statusString',
    },
    {
        Header: 'Action',
        accessor: 'action',
        Cell: ({ row: { original } }) => (
            <React.Fragment>
                <button onClick={() => onSelect(original)} className="btn btn-primary" type="button">{original.status === 1 ? 'EDIT' : 'SETTING'}</button>
            </React.Fragment>
        ),
    },
];

export const TABLE_META_AREA = [
    {
        Header: 'No',
        accessor: 'no',
        colWidth: 80,
    },
    {
        Header: 'Location',
        accessor: 'address',
        colMinWidth: 200,
    },
    {
        Header: 'Active',
        accessor: 'active_text',
    },
    {
        Header: 'Not Active',
        accessor: 'not_active_text',
    },
    {
        Header: 'Action',
        accessor: 'action',
        Cell: ({ value }) => (
            <button
                className="btn btn-primary"
                type="button"
                style={value.isActiveAll ? { backgroundColor: '#FF5630' } : {}}
                onClick={value.handler}
            >
                {value.isActiveAll ? 'Deactivate All' : 'Activate All'}
            </button>
        ),
    },
];

export const WHITELIST_TYPE = {
    OUTLET: 'outlet',
    AREA: 'area',
};

export const DATASET_SWITCH_BOX = [
    { text: 'OUTLET', value: WHITELIST_TYPE.OUTLET },
    { text: 'AREA', value: WHITELIST_TYPE.AREA },
];

export const STATUS_WHITELIST = {
    ALL: {
        label: 'All Status',
        value: null,
    },
    ACTIVE: {
        label: 'Active',
        value: 1,
    },
    NOT_ACTIVE: {
        label: 'Not Active',
        value: 0,
    },
};

export const DATASET_STATUS = [STATUS_WHITELIST.ALL.label, STATUS_WHITELIST.ACTIVE.label, STATUS_WHITELIST.NOT_ACTIVE.label];

export const FRANCHISE_OPTIONS = [
    {
        value: '',
        label: 'Tampikan Semua',
    },
    {
        value: 0,
        label: 'Toko Supplies',
    },
    {
        value: 1,
        label: 'Franchisor',
    },
];
