import React, { Component } from 'react';
import PropTypes from 'prop-types';
import moment from 'moment';
import Table from '../../../../components/retina/table/Table';
import {
    TABLE_META_OUTLET, TABLE_META_AREA, DATASET_SWITCH_BOX, WHITELIST_TYPE, DATASET_STATUS, STATUS_WHITELIST, FRANCHISE_OPTIONS,
} from './constant';
import {
    getListWhitelistOutlet, getListWhitelistArea, updateOutletWhitelist, updateAreaWhitelist,
} from '../../../../data/sales/supplies';
import SwitchBox from '../../../../components/form/SwitchBox';
import Select from '../../../../components/form/Select';
import ModalPopup from '../../../../components/modalpopup/v.2/Container';
import { catchError } from '../../../../utils/helper';
import InputText from '../../../../components/form/InputText';
import Switch from '../../../../components/form/Switch';
import InputPercent from '../../../../components/form/InputPercent';
import CoreHOC from '../../../../core/CoreHOC';
import { getFilterValue, getUpdatedFilterValue } from '../../../../utils/table.util';

@CoreHOC
class WhitelistOutlets extends Component {
    static propTypes = {
        assignCalendar: PropTypes.func,
        showProgress: PropTypes.func,
        hideProgress: PropTypes.func,
        notificationSystem: PropTypes.shape({
            addNotification: PropTypes.func,
        }),
    }

    static defaultProps = {
        assignCalendar: () => {},
        showProgress: () => {},
        hideProgress: () => {},
        notificationSystem: {
            addNotification: () => {},
        },
    }

    constructor(props) {
        super(props);
        this.state = {
            filters: [
                { id: 'status', value: 'All Status' },
                { id: 'type', value: WHITELIST_TYPE.OUTLET },
                { id: 'all', value: '' },
                { id: 'franchise', value: '' },
            ],
            dataSelected: null,
            selectedData: {
                data: {},
                show: false,
            },
            dataList: [],
            tableMeta: {
                pageIndex: 0,
                pageSize: 10,
                total: 0,
            },
        };
    }

    componentDidMount() {
        const { assignCalendar } = this.props;
        const { tableMeta } = this.state;
        assignCalendar(null, null, null);
        this.onFetch(tableMeta);
    }

    updateFilter = (val, type) => {
        const { filters, tableMeta } = this.state;
        const updatedFilterValue = getUpdatedFilterValue(filters, type, val);

        this.setState({ filters: updatedFilterValue, [type]: val }, () => {
            this.onFetch({ ...tableMeta, pageIndex: 0 });
        });
    }

    onFetch = async (state) => {
        const { hideProgress, showProgress, notificationSystem } = this.props;
        const { filters } = this.state;
        const {
            pageSize, pageIndex,
        } = state;
        const pageType = getFilterValue(filters, 'type');
        const search = getFilterValue(filters, 'all');
        const status = getFilterValue(filters, 'status');
        const franchise = getFilterValue(filters, 'franchise');

        let resData = [],
            total = 0,
            err = null;
        try {
            showProgress();
            const payload = {
                perpage: pageSize,
                page: Number(pageIndex) + 1,
                search,
            };
            if (pageType === WHITELIST_TYPE.OUTLET) {
                if (status === STATUS_WHITELIST.ACTIVE.label || status === STATUS_WHITELIST.NOT_ACTIVE.label) {
                    Object.assign(payload, {
                        status: status === STATUS_WHITELIST.ACTIVE.label ? 1 : 0,
                    });
                }
                if (franchise !== '') {
                    Object.assign(payload, {
                        status_franchise: franchise,
                    });
                }
                const res = await getListWhitelistOutlet(payload);
                resData = res.data ? res.data.map((val, index) => ({
                            ...val,
                            no: Number(res.meta.from) + index,
                            action: {
                                handler: () => this.handleActionTable(index),
                                isActiveAll: Number(val.status) === 1,
                            },
                            last_activation: val.last_activation ? moment(val.last_activation).format('DD MMMM YYYY') : '-',
                            statusString: Number(val.status) === STATUS_WHITELIST.ACTIVE.value ? STATUS_WHITELIST.ACTIVE.label : STATUS_WHITELIST.NOT_ACTIVE.label,
                })) : [];
                total = res.meta.total;
            } else {
                const res = await getListWhitelistArea(payload);
                resData = res.data ? res.data.map((val, index) => ({
                    ...val,
                    no: Number(res.meta.from) + index,
                    action: {
                        handler: () => this.handleActionTable(index),
                        isActiveAll: Number(val.not_active) === 0,
                    },
                    active_text: `${val.active} Outlet`,
                    not_active_text: `${val.not_active} Outlet`,
                })) : [];
                total = res.meta.total;
            }
        } catch (e) {
            err = e;
            notificationSystem.addNotification({
                title: 'get data whitelist failed',
                message: catchError(err),
                level: 'error',
            });
        }
        hideProgress();
        this.setState({
            dataList: resData,
            tableMeta: {
                pageIndex,
                pageSize,
                total,
            },
        });
    }

    handleActionTable = (index) => {
        const { dataList } = this.state;
        this.setState({ dataSelected: dataList[index] }, () => {
            this.modal.showPopup();
        });
    }

    handleUpdateOutlet = () => {
        const { selectedData } = this.state;
        const payload = {
            idOutlet: selectedData.data.outlet_id,
            body: {
                status: Number(selectedData.data.status),
                mdr_percent: Number(selectedData.data.status) === 0 ? 0 : Number(selectedData.data.mdr_percent),
            },
        };

        this.handleUpdateStatus(WHITELIST_TYPE.OUTLET, payload);
    }

    handleUpdateArea = () => {
        const { dataSelected } = this.state;
        const payload = {
            idCity: dataSelected.city_id,
            body: {
                status: Number(dataSelected.not_active) === 0 ? 0 : 1,
            },
        };
        this.handleUpdateStatus(WHITELIST_TYPE.AREA, payload);
    }

    handleUpdateStatus = async (type, payload) => {
        const { hideProgress, showProgress, notificationSystem } = this.props;
        const { tableMeta } = this.state;
        try {
            showProgress();
            if (type === WHITELIST_TYPE.OUTLET) {
                await updateOutletWhitelist(payload);
            } else {
                await updateAreaWhitelist(payload);
            }
            this.modal.hidePopup();
            this.perRowModal.hidePopup();
            notificationSystem.addNotification({
                title: 'Berhasil',
                message: `Update whitelist ${type === WHITELIST_TYPE.OUTLET ? 'outlet' : 'area'} success`,
                level: 'success',
            });
            this.onFetch({ ...tableMeta, pageIndex: 0 });
        } catch (err) {
            notificationSystem.addNotification({
                title: `Update whitelist ${type === WHITELIST_TYPE.OUTLET ? 'outlet' : 'area'} failed`,
                message: catchError(err),
                level: 'error',
            });
        } finally {
            hideProgress();
        }
    }

    handleOpenPopupRow(row) {
        this.setState({ selectedData: { show: true, data: row } });

        this.perRowModal.showPopup();
    }

    handleSetDetailPopup(val, key) {
        const newVal = { show: this.state.selectedData.show, data: { ...this.state.selectedData.data, [key]: val } };

        this.setState({ selectedData: newVal });
    }

    render() {
        const {
            filters, dataSelected, selectedData: { data: selectedData, show }, dataList, tableMeta,
        } = this.state;

        const filterStatus = getFilterValue(filters, 'status');
        const pageType = getFilterValue(filters, 'type');
        const filterFranchise = getFilterValue(filters, 'franchise');
        const search = getFilterValue(filters, 'all');
        const isActiveAll = dataSelected && dataSelected.action.isActiveAll;

        return (
            <div>
                <section className="panel">
                    <div className="panel-heading">
                        <h4 className="panel-title">Supplies Whitelist Outlet</h4>
                    </div>
                    <div className="panel-body">
                        <div className="d-flex mb-sm" style={{ justifyContent: 'space-between', alignItems: 'center' }}>
                            <div>
                                <SwitchBox
                                    dataset={DATASET_SWITCH_BOX}
                                    value={pageType}
                                    changeEvent={val => this.updateFilter(val, 'type')}
                                />
                            </div>
                            <div className="d-flex">
                                <div className="mr-md">
                                    <InputText
                                        placeholder="Search keyword"
                                        changeEvent={val => this.updateFilter(val, 'all')}
                                        wait={500}
                                    />
                                </div>
                                { pageType === WHITELIST_TYPE.OUTLET && (
                                    <React.Fragment>
                                        <div className="mr-md" style={{ width: '180px' }}>
                                            <Select
                                                value={filterFranchise}
                                                data={FRANCHISE_OPTIONS}
                                                changeEvent={val => this.updateFilter(val, 'franchise')}
                                            />
                                        </div>
                                        <div className="mr-md" style={{ width: '140px' }}>
                                            <Select
                                                value={filterStatus}
                                                data={DATASET_STATUS}
                                                changeEvent={val => this.updateFilter(val, 'status')}
                                            />
                                        </div>
                                    </React.Fragment>
                                )}
                            </div>
                        </div>
                        <div>
                            <Table
                                columns={pageType === WHITELIST_TYPE.OUTLET ? TABLE_META_OUTLET(row => this.handleOpenPopupRow(row)) : TABLE_META_AREA}
                                data={dataList}
                                totalData={tableMeta.total}
                                pageIndex={tableMeta.pageIndex}
                                rowLimit={tableMeta.pageSize}
                                searchQuery={search}
                                fetchData={this.onFetch}
                            />
                        </div>
                    </div>
                </section>
                <ModalPopup
                    ref={(c) => { this.modal = c; }}
                    headerTitle="Confirmation"
                    confirmText={pageType === WHITELIST_TYPE.OUTLET ? 'Change Status' : `${isActiveAll ? 'Deactivate All' : 'Activate All'}`}
                    confirmHandle={pageType === WHITELIST_TYPE.OUTLET ? this.handleUpdateOutlet : this.handleUpdateArea}
                    customStyleBtnConfirm={isActiveAll ? { backgroundColor: '#FF5630' } : {}}
                >
                    { pageType === WHITELIST_TYPE.OUTLET && (
                        <p>
                            Are you sure you want to change the whitelist status of the
                            {' '}
                            <span className="text-weight-bold">{dataSelected ? dataSelected.outlet_name : ''}</span>
                            {' '}
                            ?
                        </p>
                    )}
                    { pageType === WHITELIST_TYPE.AREA && (
                        `Are you sure you want to ${isActiveAll ? 'deactivate' : 'activate'} all outlets in this area?`
                    )}
                </ModalPopup>
                <ModalPopup
                    ref={(c) => { this.perRowModal = c; }}
                    headerTitle="Edit Data"
                    confirmText="Simpan"
                    show={show}
                    onHide={() => this.setState({ selectedData: { show: false, data: {} } })}
                    confirmHandle={() => this.handleUpdateOutlet()}
                >
                    <InputText label="Nama Outlet" value={selectedData.outlet_name || ''} disabled />
                    <InputText label="Location" value={selectedData.address || ''} disabled />
                    <div style={{ justifyContent: 'space-between' }} className="d-flex">
                        <InputPercent
                            desimal
                            maxDecimalPlaceLen={2}
                            value={selectedData.status === 1 ? selectedData.mdr_percent || '0' : ''}
                            label="MDR + Biaya Layanan%"
                            changeEvent={(e) => {
                                if (Number(e) <= 100) return this.handleSetDetailPopup(e, 'mdr_percent');
                            }}
                        />
                        <span className="d-flex flex-column">
                            <label className="control-label">
                                Action
                            </label>
                            <Switch label="Action" disabledText checked={selectedData.status === 1} changeEvent={e => this.handleSetDetailPopup(Number(e), 'status')} />
                        </span>
                    </div>
                </ModalPopup>
            </div>
        );
    }
}

export default WhitelistOutlets;
