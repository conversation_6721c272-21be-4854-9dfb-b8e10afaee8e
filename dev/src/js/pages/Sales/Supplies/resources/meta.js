import React from 'react';
import DateTimeColumn from '../../../../components/table/components/DateTimeColumn';
import PriceColumn from '../../../../components/table/components/PriceColumn';
import PaymentStatusColumn from '../../../../components/table/components/PaymentStatusColumn';
import ShipmentStatusColumn from '../../../../components/table/components/ShipmentStatusColumn';

const TABLE_META_SUPPLIES = [
    {
        Header: 'Nomor Order',
        accessor: 'number',
        colMinWidth: 150,
        unsortable: true,
    },
    {
        Header: 'Tanggal Pemesanan',
        accessor: 'order_date',
        Cell: DateTimeColumn,
        unsortable: true,
    },
    {
        Header: '<PERSON>a <PERSON>',
        accessor: 'user_name',
        unsortable: true,
    },
    {
        Header: 'Cabang',
        accessor: 'outlet_name',
        unsortable: true,
    },
    {
        Header: 'Produk',
        accessor: 'product',
        unsortable: true,
    },
    {
        Header: 'Total Belanja',
        accessor: 'bill_total',
        Cell: PriceColumn,
        unsortable: true,
    },
    {
        Header: 'Status Pembayaran',
        accessor: 'payment_status',
        Cell: PaymentStatusColumn,
        unsortable: true,
    },
    {
        Header: 'Status Pengiriman',
        accessor: 'shipment_status',
        Cell: ShipmentStatusColumn,
        unsortable: true,
    },
];

const COLUMN_CATEGORY = [
    {
        Header: 'Name',
        accessor: 'name',
        unsortable: true,
    },
    {
        Header: 'Description',
        accessor: 'description_label',
        unsortable: true,
    },
    {
        Header: 'Business Type',
        accessor: 'business_type_label',
        unsortable: true,
    },
    {
        Header: 'No',
        accessor: 'no',
        unsortable: true,
    },
];

const COLUMN_MASTER_SUPPLIES = [
    {
        Header: 'Product Name',
        accessor: 'name',
        unsortable: true,
    },
    {
        Header: 'Category Name',
        accessor: 'category_name',
        colWidth: 200,
        unsortable: true,
    },
    {
        Header: 'Price',
        accessor: 'price',
        Cell: ({ value }) => <PriceColumn value={value} isTextRight={false} />,
        colWidth: 200,
        unsortable: true,
    },
    {
        Header: 'Coverage',
        accessor: 'coverage',
        unsortable: true,
    },
];

const FILTERTYPE = {
    SHIPMENT: 'shipment',
    PAYMENT: 'payment',
    DATE: 'date',
    ALL: 'all',
};

const MAX_FILE_SIZE = 5 * 1024 * 1024;

export {
    TABLE_META_SUPPLIES,
    COLUMN_CATEGORY,
    COLUMN_MASTER_SUPPLIES,
    FILTERTYPE,
    MAX_FILE_SIZE,
};
