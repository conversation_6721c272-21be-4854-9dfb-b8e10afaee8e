import React, { Component } from 'react';
import PropTypes from 'prop-types';
import CoreHOC from '../../../core/CoreHOC';
import Table from '../../../components/retina/table/Table';
import SidePopup from '../../../components/sidepopup/Container';
import InputText from '../../../components/form/InputText';
import ModalPopup from '../../../components/modalpopup/Container';
import Autocomplete from '../../../components/form/Autocomplete';
import ImageUpload from '../../../components/form/ImageUpload';
import Switch from '../../../components/form/Switch';
import {
  getSuppliesMaster, getSuppliesCategory, updateSuppliesMaster, addSuppliesMaster,
  getCityCoverageList,
} from '../../../data/sales/master';
import { catchError } from '../../../utils/helper';
import { COLUMN_MASTER_SUPPLIES, MAX_FILE_SIZE } from './resources/meta';
import InputNumber from '../../../components/form/InputNumber';
import InputPercent from '../../../components/form/InputPercent';
import InputTextArea from '../../../components/form/InputTextArea';
import SelectMultiple from '../../../components/form/SelectMultiple';

class Suppliesmaster extends Component {
  static propTypes = {
    assignCalendar: PropTypes.func,
    assignButtons: PropTypes.func,
    notificationSystem: PropTypes.shape({
      addNotification: PropTypes.func,
    }),
    showProgress: PropTypes.func,
    hideProgress: PropTypes.func,
    router: PropTypes.shape({}),
    assignFilterColoumn: PropTypes.func,
    assignRangeDate: PropTypes.func,
  }

  static defaultProps = {
    assignCalendar: () => { },
    assignButtons: () => { },
    assignFilterColoumn: () => { },
    assignRangeDate: () => { },
    notificationSystem: null,
    router: null,
    showProgress: () => {},
    hideProgress: () => {},
  }

  constructor(props) {
    super(props);

    this.state = {
      namaProduk: '',
      harga: '',
      diskon: '',
      category: [],
      categoryTerpilih: '',
      id: '',
      imageURL: '',
      type: 'edit',
      titleSide: 'Add Supplies',
      desc: '0',
      weight: 0,
      display: false,
      active: true,
      stock: 999999,
      isFavorit: false,
      cityCoverage: [],
      selectedCity: [],
      search: '',
      isFetch: false,
      dataSource: {
          list: [],
          meta: {
            pageIndex: 0,
            pageSize: 10,
            total: 0,
          },
      },
    };
  }

  componentWillMount() {
    const {
      assignFilterColoumn, assignRangeDate, assignCalendar, assignButtons,
    } = this.props;
    const { dataSource: { meta } } = this.state;
    assignFilterColoumn([]);
    assignRangeDate([]);
    assignCalendar(null, null, null);
    assignButtons([{ type: 'primary', content: <span> Add Supply Barang </span>, action: () => { this.callAddHandler(); } }]);
    this.getCategory();
    this.getListCity();
    this.imageURLChangeHandler = this.imageURLChangeHandler.bind(this);
    this.handleFetchTable(meta);
  }

  handleFetchTable = async (state) => {
    const { notificationSystem } = this.props;
    const { search } = this.state;
    const {
      pageIndex, pageSize,
    } = state;
    const payload = {
      limit: pageSize,
      page: parseInt(pageIndex, 10) + 1,
      ...search && ({ search }),
    };

    try {
      this.setState({ isFetch: true });
      const { data, meta: { total } } = await getSuppliesMaster(payload);
      this.setState({
        isFetch: false,
        dataSource: {
          list: data,
          meta: {
              pageIndex,
              pageSize,
              total,
          },
        },
      });
    } catch (e) {
      notificationSystem.addNotification({
        title: 'Get data failed',
        message: catchError(e),
        level: 'error',
      });
      this.setState({
        isFetch: false,
        dataSource: {
          list: [],
          meta: {
            pageIndex,
            pageSize,
            total: 0,
          },
        },
      });
    }
  }

  getCategory = () => {
    const { notificationSystem } = this.props;
    // buat menghilangkan limit data
    const limit = 500;
    getSuppliesCategory({ limit })
        .then((response) => {
          const newData = response.data.map(category => ({
            id: category.no,
            name: category.name,
          }));
          this.setState({ category: newData });
        })
        .catch((err) => {
          notificationSystem.addNotification({
            title: 'Terjadi Kesalahan',
            message: catchError(err),
            level: 'error',
          });
        });
  }

  getListCity = async () => {
    const { notificationSystem } = this.props;
    try {
      const { data } = await getCityCoverageList();
      const cityCoverage = data.map(({ city_id: id, city_name: name }) => ({ value: +id, label: name }));
      this.setState({ cityCoverage });
    } catch (err) {
      notificationSystem.addNotification({
        title: 'Terjadi Kesalahan',
        message: catchError(err),
        level: 'error',
      });
    }
  }

  handleOnUploadImage = () => {
    const { showProgress } = this.props;
    showProgress();
    this.setState({
      imageURL: '',
    });
  }

  handleOnErrorCrop = (err) => {
    const { notificationSystem, hideProgress } = this.props;
    this.setState({
      imageURL: '',
    });
    hideProgress();
    notificationSystem.addNotification({
      title: 'Failed Upload Image',
      message: catchError(err),
      level: 'error',
    });
  }

  handleOnSearch = (val) => {
    const { dataSource: { meta } } = this.state;
    this.setState({
      search: val,
    }, () => this.handleFetchTable({ ...meta, pageIndex: 0 }));
  }

  callAddHandler() {
    this.setState({
      id: '',
      namaProduk: '',
      harga: '',
      diskon: '0',
      categoryTerpilih: '',
      imageURL: '',
      type: 'add',
      titleSide: 'Add Supplies',
      desc: '',
      weight: 0,
      display: true,
      active: true,
      stock: 999999,
      isFavorit: false,
      selectedCity: [],
    }, () => {
      this.sidePop.showPopup();
    });
  }

  callEditDetailHandler(val) {
    const { cityCoverage } = this.state;
    const data = val.original;
    const cityRaw = data.city_id || [];
    const selectedCity = cityCoverage.filter(({ value }) => cityRaw.some(e => +e === +value));
    this.setState({
      id: data.id,
      namaProduk: data.name,
      harga: data.price,
      diskon: data.discount,
      categoryTerpilih: data.category_no,
      imageURL: data.image_url,
      type: 'edit',
      titleSide: 'Update Supplies',
      desc: data.desc,
      weight: data.weight,
      display: +data.is_display === 1,
      active: data.is_active,
      stock: data.stock_qty,
      isFavorit: +data.is_favorite === 1,
      selectedCity,
    }, () => {
      this.sidePop.showPopup();
    });
  }

  saveHandler(type) {
    const {
      notificationSystem,
    } = this.props;
    const {
      diskon, namaProduk, harga, categoryTerpilih, id, imageURL, desc,
      display, active, weight, isFavorit, stock, selectedCity: cityRaw,
      dataSource: { meta },
    } = this.state;
    const selectedCity = cityRaw.map(val => val.value);
    const parameter = {
      name: namaProduk,
      price: +harga,
      vendor: '',
      discount: +diskon,
      category_id: categoryTerpilih,
      id: +id,
      image_url: imageURL,
      desc,
      weight: +weight,
      stock_qty: +stock,
      is_display: display,
      is_active: active,
      is_favorite: isFavorit,
      hpp: 0,
      tax: 0,
      is_bundling: false,
      additional_cost: 0,
      city_id: selectedCity,
    };
    this.sidePop.hidePopup();
    if (type !== 'add') {
      updateSuppliesMaster(parameter)
          .then(() => {
            notificationSystem.addNotification({
              title: 'Berhasil',
              message: 'update master supplies success',
              level: 'success',
            });
            this.sidePop.hidePopup();
            this.handleFetchTable(meta);
          })
          .catch((err) => {
            notificationSystem.addNotification({
              title: 'Update Failed',
              message: catchError(err),
              level: 'error',
            });
          });
    } else {
      addSuppliesMaster(parameter)
          .then(() => {
            notificationSystem.addNotification({
              title: 'Berhasil',
              message: 'Berhasil menambah master supplies',
              level: 'success',
            });
            this.sidePop.hidePopup();
            this.handleFetchTable(meta);
          })
          .catch((err) => {
            notificationSystem.addNotification({
              title: 'Add Data Failed',
              message: catchError(err),
              level: 'error',
            });
          });
    }
  }

  callRemoveHandler() {
    this.actionPopup.showPopup();
  }

  deleteActionHandler() {
    this.actionPopup.hidePopup();
  }

  changeEventCategory(val) {
    this.setState({
      categoryTerpilih: val,
    });
  }

  imageURLChangeHandler(value) {
    const { hideProgress } = this.props;
    this.setState({
      imageURL: value,
    }, () => {
      hideProgress();
    });
  }

  render() {
    const {
      namaProduk, harga, diskon, category, categoryTerpilih, display, active,
      imageURL, type, titleSide, desc, stock, weight, isFavorit, cityCoverage,
      selectedCity, isFetch, search, dataSource: { list, meta },
    } = this.state;
    const { notificationSystem } = this.props;
    return (
      <div>
        <section className="panel">
          <div className="panel-heading">
            <h4 className="panel-title">Master Supplies</h4>
          </div>

          <div>
            <div className="panel-heading table-header">
              <div className="row">
                <div className="col-md-offset-8 col-xs-12 col-sm-4">
                  <InputText
                    classes="filter"
                    placeholder="Cari ..."
                    changeEvent={this.handleOnSearch}
                    wait={500}
                  />
                </div>
              </div>
            </div>
            <div className="panel-body">
              <Table
                  columns={COLUMN_MASTER_SUPPLIES}
                  data={list}
                  isLoading={isFetch}
                  searchQuery={search}
                  pageIndex={meta.pageIndex}
                  rowLimit={meta.pageSize}
                  totalData={meta.total}
                  onRowClick={val => this.callEditDetailHandler(val)}
                  fetchData={this.handleFetchTable}
              />
            </div>
          </div>
        </section>
        <SidePopup
          ref={(c) => { this.sidePop = c; }}
          width={560}
          saveHandle={() => this.saveHandler(type)}
        >
          <h4 className="side-popup-title">
            {titleSide}
          </h4>
          <div className="row mb-sm">
            <div className="col-sm-12">
              <InputText label="Product Name" placeholder="Product Name" value={namaProduk} changeEvent={value => this.setState({ namaProduk: value })} />
            </div>
          </div>
          <div className="row mb-sm">
            <div className="col-sm-12">
              <InputTextArea
                  label="Product Desc"
                  placeholder="Product Desc"
                  value={desc}
                  row={4}
                  changeEvent={value => this.setState({ desc: value })}
              />
            </div>
          </div>
          <div className="row mb-sm">
            <div className="col-sm-12">
              <Autocomplete
                  data={category}
                  selector="name"
                  value={categoryTerpilih}
                  changeEvent={(val) => { this.changeEventCategory(val); }}
                  placeholder="Search ..."
                  label="Category Name"
              />
            </div>
          </div>
          <div className="row mb-sm">
            <div className="col-sm-12">
              <SelectMultiple
                  options={cityCoverage}
                  value={selectedCity}
                  selector="label"
                  placeholder="Coverage"
                  label="Coverage"
                  changeEvent={val => this.setState({ selectedCity: val })}
              />
            </div>
          </div>
          <div className="row mb-sm">
            <div className="col-sm-12">
              <InputNumber
                  label="Price"
                  placeholder="Price"
                  value={harga}
                  changeEvent={value => this.setState({ harga: value })}
                  name="price"
              />
            </div>
          </div>
          <div className="row mb-sm">
            <div className="col-sm-12">
              <InputNumber label="Weight (Kg)" placeholder="Weight" value={weight} changeEvent={value => this.setState({ weight: value })} />
            </div>
          </div>
          <div className="row mb-sm">
            <div className="col-sm-12">
              <InputText label="Stock Qty" placeholder="Stock Qty" value={stock} disabled changeEvent={value => this.setState({ stock: value })} />
            </div>
          </div>
          <div className="row mb-sm">
            <div className="col-sm-12">
              <InputPercent label="Discount (%)" placeholder="Discount (%)" value={diskon} changeEvent={value => this.setState({ diskon: +value })} />
            </div>
          </div>
          <div className="row mb-sm text-left">
            <div className="col-sm-6">
              Is Display
            </div>
            <div className="col-sm-6 text-right">
              <Switch className="text-right" checked={display} valueTrue="Yes" valueFalse="No" changeEvent={value => this.setState({ display: value })} />
            </div>
          </div>
          <div className="row mb-sm">
            <div className="col-sm-6 text-left">
              Active Status
            </div>
            <div className="col-sm-6 text-right">
              <Switch className="text-right" checked={active} valueTrue="Active" valueFalse="No" changeEvent={value => this.setState({ active: value })} />
            </div>
          </div>
          <div className="row mb-sm">
            <div className="col-sm-6 text-left">
              Product Favorite
            </div>
            <div className="col-sm-6 text-right">
              <Switch className="text-right" checked={isFavorit} valueTrue="Yes" valueFalse="No" changeEvent={() => this.setState({ isFavorit: !isFavorit })} />
            </div>
          </div>
          <div className="row mb-sm">
            <div className="col-sm-3">
              <ImageUpload
                  changeEvent={this.imageURLChangeHandler}
                  imageURL={imageURL}
                  imageWidth={450}
                  imageHeight={304}
                  imageCrop
                  notificationSystem={notificationSystem}
                  maxFileSize={MAX_FILE_SIZE}
                  onError={this.handleOnErrorCrop}
                  onUploading={this.handleOnUploadImage}
              />
            </div>
          </div>
        </SidePopup>


        <ModalPopup
          title="Action"
          width={560}
          confirmHandle={(val) => { this.deleteActionHandler(val); }}
          ref={(c) => { this.actionPopup = c; }}
        >

          <div className="row mb-sm">
            <div className="col-sm-12">
              <span>{`Are you sure to delete ${namaProduk} ?`}</span>
            </div>
          </div>
        </ModalPopup>
      </div>
    );
  }
}

export default CoreHOC(Suppliesmaster);
