import React, { Component } from 'react';
import PropTypes from 'prop-types';
import moment from 'moment';

/* COMPONENTS */
import Select from '../../../components/form/Select';
import CoreHOC from '../../../core/CoreHOC';
import Table from '../../../components/retina/table/Table';
import InputText from '../../../components/form/InputText';

/* DATA */
import { getBusinessLoan } from '../../../data/support';

/* CONFIG */
import { busninessMetaTabel, dataLimit } from './config/BusninessLoaning';
import { catchError } from '../../../utils/helper';

class BusinessLoaning extends Component {
  static propTypes = {
    calendar: PropTypes.shape({ start: PropTypes.string, end: PropTypes.string }),
    assignCalendar: PropTypes.func.isRequired,
    assignButtons: PropTypes.func.isRequired,
    router: PropTypes.shape({ push: PropTypes.func }).isRequired,
    notificationSystem: PropTypes.shape({ addNotification: PropTypes.func }),
    assignFilterColoumn: PropTypes.func,
    assignRangeDate: PropTypes.func,
    hideProgress: PropTypes.func.isRequired,
    showProgress: PropTypes.func.isRequired,
  }

  static defaultProps = {
    calendar: { start: '', end: '' },
    notificationSystem: ({ addNotification: () => { } }),
    assignFilterColoumn: () => { },
    assignRangeDate: () => { },
  }

  constructor(props) {
    super(props);
    const { calendar } = this.props;

    this.state = {
      dateFilter: calendar,
      pageSize: 10,
      searchQuery: '',
      tableData: [],
      tableMeta: {
        pageIndex: 1,
        limit: 10,
        totalData: 0,
      },
      tableLoading: false,
    };
  }

  componentDidMount() {
    const {
      assignFilterColoumn, assignRangeDate, assignButtons, assignCalendar, hideProgress,
    } = this.props;
    assignFilterColoumn([]);
    assignRangeDate([]);
    assignButtons([]);
    assignCalendar(null, null, (startDate, endDate) => {
      this.changeDateHandler(startDate, endDate);
    }, true);

    this._onFetchs({ pageIndex: 0 });
    hideProgress();
  }

  componentDidUpdate(prevProps, prevState) {
    const { dateFilter, pageSize, searchQuery } = this.state;
    const filtersChanged = (prevState.dateFilter
      && (prevState.dateFilter.start !== dateFilter.start
      || prevState.dateFilter.end !== dateFilter.end))
      || prevState.pageSize !== pageSize
      || prevState.searchQuery !== searchQuery;

    if (filtersChanged) {
      this._onFetchs({ pageIndex: 0, pageSize });
    }
  }

  _onFetchs = async (params) => {
    const { notificationSystem, showProgress, hideProgress } = this.props;
    const {
      pageIndex, pageSize: paramsPageSize, sortAccessor, sortDirection,
    } = params;
    const {
      dateFilter: { start, end }, pageSize: statePageSize, searchQuery, tableMeta,
    } = this.state;

    showProgress();
    this.setState({ tableLoading: true });

    let payload = {
      per_page: paramsPageSize || statePageSize,
      page: pageIndex + 1,
      start_date: moment(start, 'DD/MM/YYYY').format('YYYY-MM-DD'),
      end_date: moment(end, 'DD/MM/YYYY').format('YYYY-MM-DD'),
      ...(searchQuery && { search: searchQuery }),
    };

    if (sortAccessor) {
      payload = { ...payload, order: sortAccessor, sort: sortDirection };
    }

    try {
      const resApi = await getBusinessLoan(payload);
      if (resApi) {
        const { data: newData, metadata } = resApi;
        this.setState({
          tableData: newData,
          tableMeta: {
            pageIndex,
            limit: paramsPageSize || statePageSize,
            totalData: metadata.total,
            pageCount: metadata.total_page,
          },
        });
      }
    } catch (e) {
      notificationSystem.addNotification({
        title: 'Error', message: catchError(e), level: 'error',
      });
      this.setState({ tableData: [], tableMeta: { ...tableMeta, totalData: 0 } });
    } finally {
      this.setState({ tableLoading: false });
      hideProgress();
    }
  }

  callEditDetailHandler = ({ original: data }) => {
    const { router } = this.props;
    router.push(`/business-dev/loan/detail/${data.no}`);
  }

  changeDateHandler = (start, end) => {
    const { assignCalendar } = this.props;
    assignCalendar(start, end);
    this.setState({ dateFilter: { start, end } });
  }

  handleSearchChange = (value) => {
    this.setState({ searchQuery: value });
  }

  render() {
    const {
      pageSize, tableData, tableMeta, tableLoading, searchQuery,
    } = this.state;

    return (
      <div>
        <section className="panel">
          <div className="panel-heading table-header">
            <div className="row">
              <div className="col-md-8">
                <h4 className="panel-title" style={{ paddingTop: '8px' }}>Business Loan</h4>
              </div>
              <div className="col-md-4">
                <div className="row">
                  <div className="col-md-9">
                    <InputText
                      classes="filter"
                      placeholder="Cari..."
                      changeEvent={this.handleSearchChange}
                      wait={500}
                    />
                  </div>
                  <div className="col-md-3" style={{ paddingLeft: 0 }}>
                    <Select
                      data={dataLimit}
                      value={pageSize}
                      changeEvent={value => this.setState({ pageSize: value })}
                      classes="displayCount mb-reset"
                    />
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div className="panel-body">
            <Table
              columns={busninessMetaTabel}
              data={tableData}
              fetchData={this._onFetchs}
              isLoading={tableLoading}
              onRowClick={this.callEditDetailHandler}
              pageIndex={tableMeta.pageIndex}
              rowLimit={tableMeta.limit}
              totalData={tableMeta.totalData}
              searchQuery={searchQuery}
            />
          </div>
        </section>
      </div>
    );
  }
}

export default CoreHOC(BusinessLoaning);
