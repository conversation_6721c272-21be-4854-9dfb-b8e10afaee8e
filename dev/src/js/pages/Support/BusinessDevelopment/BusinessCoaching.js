import React, { Component } from 'react';
import PropTypes from 'prop-types';
import update from 'immutability-helper';
import CoreHOC from '../../../core/CoreHOC';

/* COMPONENTS */
import Table from '../../../components/retina/table/Table';
import InputText from '../../../components/form/InputText';
import SwitchBox from '../../../components/form/SwitchBox';
import SidePopup from '../../../components/sidepopup/Container';
import InputTextArea from '../../../components/form/InputTextArea';

/* DATA */
import { getBusinessCoaching, updateBusinessCoaching } from '../../../data/support';

/* CONFIG */
import { TABLE_META, STATUS_FILTER_LIST, STATUS_EDIT_LIST } from './config/BusinessCoaching';
import { catchError } from '../../../utils/helper';

class BusinessCoaching extends Component {
  static propTypes = {
    assignCalendar: PropTypes.func,
    assignButtons: PropTypes.func,
    assignFilterColoumn: PropTypes.func,
    assignRangeDate: PropTypes.func,
    notificationSystem: PropTypes.shape({ addNotification: PropTypes.func }),
  }

  static defaultProps = {
    assignCalendar: () => { },
    assignButtons: () => { },
    assignFilterColoumn: () => { },
    assignRangeDate: () => { },
    notificationSystem: { addNotification: () => { } },
  }

  constructor(props) {
    super(props);

    this.state = {
      switchFilter: 'all',
      searchQuery: '',
      tableData: [],
      tableLoading: false,
      detail: {
        id: '',
        status: '',
        detail: '',
        name: '',
        applicant: '',
        phone: '',
        email: '',
        address: '',
        province: '',
        city: '',
        goal: '',
        date: '',
        time: '',
      },
    };
  }

  componentDidMount() {
    const {
      assignFilterColoumn, assignRangeDate, assignCalendar, assignButtons,
    } = this.props;
    assignFilterColoumn([]);
    assignRangeDate([]);
    assignCalendar(null, null, null);
    assignButtons([]);
    this.fetchData();
  }

  fetchData = async () => {
    const { notificationSystem } = this.props;
    this.setState({ tableLoading: true });
    try {
      const res = await getBusinessCoaching();
      this.setState({ tableData: res.data });
      this.sidePop.hidePopup();
    } catch (error) {
      notificationSystem.addNotification({
        title: 'Gagal mendapatkan data',
        message: catchError(error),
        level: 'error',
      });
    } finally {
      this.setState({ tableLoading: false });
    }
  }

  callEditDetailHandler = ({ original: data }) => {
    const { detail } = this.state;
    const newDetail = update(detail, {
      id: { $set: data.id },
      status: { $set: data.status_meeting_txt },
      detail: { $set: data.tujuan_meeting },
      name: { $set: data.usaha },
      applicant: { $set: data.pemohon },
      phone: { $set: data.no_telp },
      email: { $set: data.alamat_email },
      address: { $set: data.alamat_lengkap },
      province: { $set: data.province },
      city: { $set: data.city },
      date: { $set: data.date },
      time: { $set: data.time },
    });
    this.setState({ detail: newDetail }, () => {
      this.sidePop.showPopup();
    });
  }

  statusChangeHandler = (newStatus) => {
    const { detail } = this.state;
    const newDetail = update(detail, {
      status: { $set: newStatus },
    });
    this.setState({ detail: newDetail });
  }

  saveHandle = async () => {
    const { detail, tableData } = this.state;
    const originalRecord = tableData.find(df => df.id === detail.id);

    if (originalRecord && originalRecord.status_meeting_txt === detail.status) {
      this.sidePop.hidePopup();
    } else {
      const param = { id: detail.id, status: detail.status };
      await updateBusinessCoaching(param);
      await this.fetchData();
    }
  }

  handleSearchChange = (value) => {
    this.setState({ searchQuery: value });
  }

  render() {
    const {
      detail, switchFilter, tableData, tableLoading, searchQuery,
    } = this.state;

    const filteredByStatus = switchFilter === 'all'
      ? tableData
      : tableData.filter(item => item.status_meeting_txt === switchFilter);

    const filteredData = filteredByStatus.filter(item => !searchQuery
      || (item.pemohon && item.pemohon.toLowerCase().includes(searchQuery.toLowerCase()))
      || (item.usaha && item.usaha.toLowerCase().includes(searchQuery.toLowerCase())));

    return (
      <div>
        <section className="panel">
          <div className="panel-heading table-header">
            <div className="row">
              <div className="col-md-3">
                <h4 className="panel-title" style={{ margin: '8px' }}>
                  Business Coaching
                </h4>
              </div>
              <div className="col-md-6">
                <div style={{ textAlign: 'center' }}>
                  <SwitchBox
                    dataset={STATUS_FILTER_LIST}
                    value={switchFilter}
                    changeEvent={val => this.setState({ switchFilter: val })}
                  />
                </div>
              </div>
              <div className="col-md-3">
                <InputText
                  classes="filter"
                  placeholder="Cari..."
                  changeEvent={this.handleSearchChange}
                  wait={500}
                />
              </div>
            </div>
          </div>
          <div className="panel-body">
            <Table
              columns={TABLE_META}
              data={filteredData}
              isLoading={tableLoading}
              onRowClick={this.callEditDetailHandler}
              totalData={filteredData.length}
              searchQuery={searchQuery}
            />
          </div>
        </section>
        <SidePopup
          ref={(c) => { this.sidePop = c; }}
          width={560}
          saveHandle={this.saveHandle}
        >
          <h4 className="side-popup-title">
            Business Coaching Detail
          </h4>
          <div className="row mb-sm">
            <div className="col-sm-12">
              <div>
                <label className="control-label">Status</label>
              </div>
              <div>
                <SwitchBox
                  dataset={STATUS_EDIT_LIST}
                  value={detail.status}
                  changeEvent={this.statusChangeHandler}
                />
              </div>
            </div>
          </div>
          <div className="row mb-sm">
            <div className="col-sm-12">
              <InputTextArea label="Detail" value={detail.detail} disabled row={2} />
            </div>
          </div>
          <div className="row mb-sm">
            <div className="col-sm-6">
              <InputText label="Business Name" value={detail.name} disabled />
            </div>
            <div className="col-sm-6">
              <InputText label="Applicant" value={detail.applicant} disabled />
            </div>
          </div>
          <div className="row mb-sm">
            <div className="col-sm-6">
              <InputText label="Phone" value={detail.phone} disabled />
            </div>
            <div className="col-sm-6">
              <InputText label="Email" value={detail.email} disabled />
            </div>
          </div>
          <div className="row mb-sm">
            <div className="col-sm-12">
              <InputTextArea label="Address" value={detail.address} disabled row={2} />
            </div>
          </div>
          <div className="row mb-sm">
            <div className="col-sm-6">
              <InputText label="Province" value={detail.province} disabled />
            </div>
            <div className="col-sm-6">
              <InputText label="City" value={detail.city} disabled />
            </div>
          </div>
          <div className="row mb-sm">
            <div className="col-sm-6">
              <InputText label="Date" value={detail.date} disabled />
            </div>
            <div className="col-sm-6">
              <InputText label="Time" value={detail.time} disabled />
            </div>
          </div>
        </SidePopup>
      </div>
    );
  }
}

export default CoreHOC(BusinessCoaching);
