import React, { PureComponent } from 'react';
import PropTypes from 'prop-types';
import moment from 'moment';
import CoreHOC from '../../../core/CoreHOC';
import { catchError, hoursToHumanize } from '../../../utils/helper';

/* COMPONENTS */
import Table from '../../../components/retina/table/Table';
import InputText from '../../../components/form/InputText';

/* DATA */
import { getAccessLogin } from '../../../data/support';

/* CONFIG */
import { TABLE_META } from './config/AccessLogin';

class AccessLogin extends PureComponent {
  static propTypes = {
    calendar: PropTypes.shape({ start: PropTypes.string, end: PropTypes.string }),
    assignCalendar: PropTypes.func,
    assignFilterColoumn: PropTypes.func,
    assignButtons: PropTypes.func,
    notificationSystem: PropTypes.shape({ addNotification: PropTypes.func }),
  }

  static defaultProps = {
    calendar: { start: '', end: '' },
    assignCalendar: () => { },
    assignFilterColoumn: () => { },
    assignButtons: () => { },
    notificationSystem: { addNotification: null },
  }

  constructor(props) {
    super(props);
    const { calendar } = this.props;

    this.state = {
      dateFilter: calendar,
      searchQuery: '',
      tableData: [],
      tableMeta: {
        pageIndex: 1,
        limit: 10,
        totalData: 0,
      },
      tableLoading: false,
    };
  }

  componentDidMount() {
    const { assignCalendar, assignButtons, assignFilterColoumn } = this.props;
    assignFilterColoumn([]);
    assignButtons([]);
    assignCalendar(null, null, (startDate, endDate) => {
      this.changeDateHandler(startDate, endDate);
    });
    this._onFetchs({ pageIndex: 0 });
  }

  componentDidUpdate(prevProps, prevState) {
    const { dateFilter, searchQuery, tableMeta } = this.state;
    const filtersChanged = (prevState.dateFilter
      && (prevState.dateFilter.start !== dateFilter.start
        || prevState.dateFilter.end !== dateFilter.end))
      || prevState.searchQuery !== searchQuery;

    if (filtersChanged) {
      this._onFetchs({ pageIndex: 0, pageSize: tableMeta.limit });
    }
  }

  _onFetchs = async (params) => {
    const { notificationSystem } = this.props;
    const {
      pageIndex, pageSize, sortAccessor, sortDirection,
    } = params;
    const { dateFilter, searchQuery, tableMeta } = this.state;

    this.setState({ tableLoading: true });

    let payload = {
      start_date: moment(dateFilter.start, 'DD/MM/YYYY').format('YYYY-MM-DD'),
      end_date: moment(dateFilter.end, 'DD/MM/YYYY').format('YYYY-MM-DD'),
      per_page: pageSize || 10,
      page: pageIndex + 1,
      search: searchQuery,
    };

    if (sortAccessor) {
      payload = { ...payload, order: sortAccessor, sort: sortDirection };
    }

    try {
      const res = await getAccessLogin(payload);
      const { data, meta } = res;
      const result = data.map(item => ({
        date: item.start_date,
        username: item.username,
        outlet: item.outlet_name ? item.outlet_name : '-',
        merchant: item.store_name ? item.store_name : '-',
        password: item.code,
        status: item.STATUS,
        note: item.note,
        duration: hoursToHumanize(item.duration),
        copy: {
          text: `user: ${item.username} / pass: ${item.code}`,
          status: item.STATUS,
          message: 'Username and password copied to clipboard.',
        },
      }));

      this.setState({
        tableData: result,
        tableMeta: {
          pageIndex,
          limit: pageSize || 10,
          totalData: meta.total,
        },
      });
    } catch (e) {
      notificationSystem.addNotification({
        title: 'Gagal mendapatkan data',
        message: catchError(e),
        level: 'error',
      });
      this.setState({ tableData: [], tableMeta: { ...tableMeta, totalData: 0 } });
    } finally {
      this.setState({ tableLoading: false });
    }
  }

  changeDateHandler = (start, end) => {
    const { assignCalendar } = this.props;
    assignCalendar(start, end);
    this.setState({ dateFilter: { start, end } });
  }

  handleSearchChange = (value) => {
    this.setState({ searchQuery: value });
  }

  render() {
    const {
      tableData, tableMeta, tableLoading, searchQuery,
    } = this.state;

    return (
      <section className="panel">
        <div className="panel-heading table-header">
          <div className="row">
            <div className="col-md-9">
              <h4 className="panel-title" style={{ margin: '8px' }}>
                Support Access Login
              </h4>
            </div>
            <div className="col-md-3">
              <InputText
                classes="filter"
                placeholder="Cari..."
                changeEvent={this.handleSearchChange}
                wait={500}
              />
            </div>
          </div>
        </div>
        <div className="panel-body">
          <Table
            columns={TABLE_META}
            data={tableData}
            fetchData={this._onFetchs}
            isLoading={tableLoading}
            pageIndex={tableMeta.pageIndex}
            rowLimit={tableMeta.limit}
            totalData={tableMeta.totalData}
            searchQuery={searchQuery}
          />
        </div>
      </section>
    );
  }
}

export default CoreHOC(AccessLogin);
