import React from 'react';
import PropTypes from 'prop-types';

import InputText from '../../../../components/form/InputText';
import FileUpload from '../../../../components/form/FileUpload';
import { printProviderName } from '../utils';

function GrabForm(props) {
    const {
        data,
        grabType,
    } = props;
    let dataImg = { filename: '', path: '', thumbnail: '' };
    if (data.profile_grab_merchant_img && data.profile_grab_merchant_img.length > 0) {
        const img = data.profile_grab_merchant_img[0];
        dataImg = { filename: img.filename, path: img.large, thumbnail: img.thumbnail };
    }
    return (
        <div className="row mb-sm">
            <div className="col-sm-4">
                <h4 style={{ color: 'white' }}>Outlets</h4>
            </div>
            <div className="col-sm-8 row">
                <div className="col-sm-6">
                    <InputText
                        label={(
                            <p className="m-none">
                                Outlet ID
                                <span className="text-danger"> *</span>
                            </p>
                        )}
                        value={data.id_outlet}
                        disabled
                    />
                    <InputText
                        label={(
                            <p className="m-none">
                                {`${printProviderName(grabType)}’s Outlet Name`}
                                <span className="text-danger"> *</span>
                            </p>
                        )}
                        value={data.name}
                        disabled
                    />
                    <InputText
                        label={(
                            <p className="m-none">
                                Outlet Address
                                <span className="text-danger"> *</span>
                            </p>
                        )}
                        value={data.address}
                        disabled
                    />
                    <InputText
                        label={(
                            <p className="m-none">
                                City
                                <span className="text-danger"> *</span>
                            </p>
                        )}
                            value={data.city}
                        disabled
                    />
                    <InputText label="Number of Products" classes="mb-sm" value={data.total_product} disabled />
                    <InputText
                        label={(
                            <p className="m-none">
                                Email GrabMerchant
                                <span className="text-danger"> *</span>
                            </p>
                        )}
                            value={data.email}
                        disabled
                    />
                    <InputText
                        label={(
                            <p className="m-none">
                                MID
                                <span className="text-danger"> *</span>
                            </p>
                        )}
                            value={data.mid}
                        disabled
                    />
                    <FileUpload
                        label="Screenshoot Profile Toko"
                        value={dataImg}
                        name="profile_grab_merchant"
                        disabled
                        mainURL="wallet"
                        changeEvent={() => {}}
                    />
                </div>
            </div>
        </div>
    );
}

GrabForm.propTypes = {
    data: PropTypes.shape({}),
    grabType: PropTypes.number.isRequired,
};

GrabForm.defaultProps = {
    data: {},
};

export default GrabForm;
