import React, { Component } from 'react';
import PropTypes from 'prop-types';
import moment from 'moment';

import CoreHOC from '../../../../core/CoreHOC';
import Table from '../../../../components/retina/table/Table';
import Select from '../../../../components/form/Select';

import {
  getSubmission, printExcel,
} from '../../../../data/setting/marketplace';

import { catchError } from '../../../../utils/helper';
import { convertDate, style } from './helper';

import { tableColumn, statusList } from './config';
import { getFilterValue } from '../../../../utils/table.util';
import InputText from '../../../../components/form/InputText';

@CoreHOC
export default class Tokopedia extends Component {
  static propTypes = {
    calendar: PropTypes.shape({
      start: PropTypes.string,
      end: PropTypes.string,
    }),
    notificationSystem: PropTypes.shape({
      addNotification: PropTypes.func,
    }),
    router: PropTypes.shape({
      push: PropTypes.func,
    }),
    assignCalendar: PropTypes.func,
    assignButtons: PropTypes.func,
    location: PropTypes.shape({
      pathname: PropTypes.string,
    }).isRequired,
    showProgress: PropTypes.func.isRequired,
    hideProgress: PropTypes.func.isRequired,
  };

  static defaultProps = {
    calendar: {
      start: '',
      end: '',
    },
    notificationSystem: {
      addNotification: null,
    },
    router: {
      push: null,
    },
    assignCalendar: () => { },
    assignButtons: () => {},
  };

  constructor(props) {
    super(props);

    this.state = {
        statusTerpilih: '',
        tanggalStart: '',
        tanggalEnd: '',
        search: '',
        isFetch: false,
        tableData: [],
        tableMeta: {
          pageIndex: 0,
          pageSize: 10,
          total: 0,
        },

    };
  }

  componentWillMount() {
    const {
      assignButtons, assignCalendar, calendar,
    } = this.props;

    assignCalendar(null, null, (startDate, endDate) => {
      this.changeDateHandler(startDate, endDate);
    });
    assignButtons([
      {
        type: 'primary',
        content: (
          <span>
            <i className="fa fa-download" />
            Download Pengajuan
          </span>
        ),
        action: () => this.handleDownloadPengajuan(),
      },
    ]);

    const tanggalStart = calendar.start;
    const tanggalEnd = calendar.end;

    this.changeDateHandler(tanggalStart, tanggalEnd);
  }

  componentDidUpdate(prevProps, prevState) {
    const { statusTerpilih, tableMeta } = this.state;

    if (prevState.statusTerpilih !== statusTerpilih) {
      this.fetchDataHandler({ ...tableMeta, pageIndex: 0 });
    }
  }

  fetchDataHandler = async ({
    pageIndex, pageSize, sortAccessor, sortDirection,
  }) => {
    const { showProgress, hideProgress, notificationSystem } = this.props;
    const {
      tanggalStart, tanggalEnd, statusTerpilih, search,
    } = this.state;

    const payload = {
      page: pageIndex + 1,
      limit: pageSize,
      ...search && { filterKeyword: search, search },
      start_date: moment(tanggalStart, 'DD-MM-YYYY').format('YYYY-MM-DD'),
      end_date: moment(tanggalEnd, 'DD-MM-YYYY').format('YYYY-MM-DD'),
      status: statusTerpilih,
      type: '1',
      ...sortAccessor && { column: sortAccessor },
      ...sortDirection && { isAsc: sortDirection },
    };

    showProgress();
    this.setState({ isFetch: false });
    try {
      const res = await getSubmission(payload);
      if (!res) throw new Error('Gagal Mendapatkan Data');
      this.setState({
        isFetch: false,
        tableData: res.data,
        tableMeta: {
          pageIndex,
          pageSize,
          total: res.total_data,
          ...sortAccessor && { sortAccessor },
          ...sortDirection && { sortDirection },
        },
      });
    } catch (e) {
      notificationSystem.addNotification({
        title: 'Gagal Mendapatkan Data Akun',
        message: catchError(e),
        level: 'error',
      });
      this.setState({
        isFetch: false,
        tableData: [],
        tableMeta: {
          pageIndex,
          pageSize,
          total: 0,
          ...sortAccessor && { sortAccessor },
          ...sortDirection && { sortDirection },
        },
      });
    } finally {
      hideProgress();
    }
  }

  handleDownloadPengajuan = async () => {
    const {
      notificationSystem, hideProgress, showProgress,
    } = this.props;
    const { tableData: submissionList } = this.state;

    showProgress();

    const param = {
      data: submissionList,
      type: '1',
    };

    try {
      const res = await printExcel(param);
      if (!res.status) throw Error('Gagal mendapatkan data');
      window.location = res.data;
    } catch (err) {
        notificationSystem.addNotification({
          title: 'Terjadi Kesalahan',
          message: catchError(err),
          level: 'error',
        });
    }

    hideProgress();
  }

  callEditDetailHandler = ({ original: data }) => {
    const { router } = this.props;

    router.push(`/marketplace/tokopedia/detail/${data.submission_number}`);
  }

  handleOnSearch = (val) => {
    const { tableMeta } = this.state;
    this.setState({
        search: val,
    }, () => this.fetchDataHandler({ ...tableMeta, pageIndex: 0 }));
  }

  changeCustomStatus(statusTerpilih) {
    this.setState({ statusTerpilih });
  }

  changeDateHandler(startDate, endDate) {
    const { assignCalendar } = this.props;
    const { tableMeta } = this.state;
    assignCalendar(startDate, endDate);
    this.setState({
        tanggalStart: startDate,
        tanggalEnd: endDate,
    }, () => {
      this.fetchDataHandler({ ...tableMeta, pageIndex: 0 });
    });
  }

  render() {
    const { calendar } = this.props;
    const {
      statusTerpilih, isFetch, search,
      tableData, tableMeta,
    } = this.state;

    return (
        <section className="panel">
            <div className="panel-heading">
              <h4 className="panel-title mt-reset">
                Tokopedia Integration
              </h4>
              <h5
                className="subtitle"
                style={style.subtitleStyle}
              >
                {`${convertDate(calendar.start)} - ${convertDate(calendar.end)}`}
              </h5>
            </div>
            <div className="panel-heading table-header">
              <div className="row">
                <div className="col-md-12">
                  <div style={{ width: '100%' }}>
                    <div className="row">
                      <div className="col-md-4" style={{ paddingLeft: '0px', float: 'right' }}>
                        <InputText
                          classes="filter"
                          placeholder="Cari ..."
                          changeEvent={this.handleOnSearch}
                          wait={500}
                        />
                      </div>
                      <div className="col-md-2" style={{ paddingLeft: '0px', float: 'right' }}>
                        <Select
                          data={statusList}
                          value={statusTerpilih}
                          changeEvent={val => this.changeCustomStatus(val)}
                        />
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div className="panel-body">
                <Table
                    columns={tableColumn}
                    data={tableData}
                    pageIndex={tableMeta.pageIndex}
                    totalData={tableMeta.total}
                    rowLimit={tableMeta.pageSize}
                    searchQuery={search}
                    isLoading={isFetch}
                    onRowClick={val => this.callEditDetailHandler(val)}
                    fetchData={this.fetchDataHandler}
                />
            </div>
        </section>
    );
  }
}
