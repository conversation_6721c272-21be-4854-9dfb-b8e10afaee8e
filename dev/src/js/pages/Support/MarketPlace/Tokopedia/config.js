import React from 'react';

import NumberDateTimeColumn from '../../../../components/table/components/NumberDateTimeColumn';
import MarketplaceStatus from '../components/MarketplaceStatusTokped';

const tableColumn = [
    {
        Header: 'Merchant Name',
        accessor: 'merchant.name',
        colMinWidth: 175,
    },
    {
        Header: 'Email',
        accessor: 'merchant.email',
        colMinWidth: 175,
    },
    {
        Header: 'Owner Name',
        accessor: 'merchant.owner_name',
        colMinWidth: 160,
    },
    {
        Header: 'Status',
        accessor: 'outlets',
        colMinWidth: 610,
        Cell: ({ row: { original } }) => <MarketplaceStatus data={original.outlets} />,
    },
    {
        Header: 'Created Date',
        accessor: 'createdate',
        Cell: NumberDateTimeColumn,
        colMinWidth: 150,
    },
    {
        Header: 'Updated Date',
        accessor: 'updatedate',
        colMinWidth: 150,
        Cell: ({ row }) => {
            const { original: { log_update_name: logName } } = row;
            return (
                <div style={{ display: 'flex', flexDirection: 'column' }}>
                    <NumberDateTimeColumn {...row} />
                    {logName && (
                        <div>{`By ${logName}`}</div>
                    )}
                </div>
            );
        },
    },
];

const statusList = [
    {
        value: '',
        option: 'All',
    },
    {
        value: '0',
        option: 'Unpaired',
    },
    {
        value: '1',
        option: 'In progress',
    },
    {
        value: '2',
        option: 'Rejected',
    },
    {
        value: '4',
        option: 'Approved',
    },
    {
        value: '5',
        option: 'Unpair Request',
    },
    {
        value: '6',
        option: 'Integrated',
    },
];

export { tableColumn, statusList };
