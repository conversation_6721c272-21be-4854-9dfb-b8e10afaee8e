import React, { Component } from 'react';
import PropTypes from 'prop-types';
import moment from 'moment';

import CoreHOC from '../../../../core/CoreHOC';
import Table from '../../../../components/retina/table/Table';
import InputText from '../../../../components/form/InputText';
import Select from '../../../../components/form/Select';
import { COLUMN_CONFIG, STATUS_LIST, FILTER_TYPES } from './config';

import {
    printExcel, getSubmissionV12,
} from '../../../../data/setting/marketplace';
import { PROVIDER_ID } from '../../../../data/setting/marketplace/enum';

import { catchError } from '../../../../utils/helper';
import { getFilterValue, getUpdatedFilterValue } from '../../../../utils/table.util';

@CoreHOC
export default class Grab<PERSON>ood extends Component {
    static propTypes = {
        calendar: PropTypes.shape({
            start: PropTypes.string,
            end: PropTypes.string,
        }),
        notificationSystem: PropTypes.shape({
            addNotification: PropTypes.func,
        }),
        router: PropTypes.shape({
            push: PropTypes.func,
        }),
        assignCalendar: PropTypes.func,
        assignButtons: PropTypes.func,
        location: PropTypes.shape({
            pathname: PropTypes.string,
        }).isRequired,
        showProgress: PropTypes.func.isRequired,
        hideProgress: PropTypes.func.isRequired,
    };

    static defaultProps = {
        calendar: {
            start: '',
            end: '',
        },
        notificationSystem: {
            addNotification: null,
        },
        router: {
            push: null,
        },
        assignCalendar: () => { },
        assignButtons: () => {},
    };

    constructor(props) {
        super(props);
        const { calendar: { start, end } } = props;
        this.state = {
            submissionList: [],
            filters: [
                { id: 'date', value: { start, end } },
                { id: 'status', value: '' },
                { id: 'all', value: '' },
            ],
            isFetching: false,
            tableMeta: {
                pageIndex: 0,
                pageSize: 10,
                total: 0,
            },
        };
    }

    componentWillMount() {
        const {
            assignButtons, assignCalendar, calendar,
        } = this.props;

        assignCalendar(null, null, (start, end) => {
            this.updateFilter({ start, end }, FILTER_TYPES.DATE);
        });
        assignButtons([
            {
            type: 'primary',
            content: (
                <span>
                <i className="fa fa-download" />
                Download Pengajuan
                </span>
            ),
            action: () => this.handleDownloadPengajuan(),
            },
        ]);
        this.updateFilter({ start: calendar.start, end: calendar.end }, FILTER_TYPES.DATE);
    }

    updateFilter = (val, type) => {
        const { assignCalendar } = this.props;

        const { filters, tableMeta } = this.state;
        const updatedFilterValue = getUpdatedFilterValue(filters, type, val);

        if (type === FILTER_TYPES.DATE) {
            const { start: startDate, end: endDate } = val;
            assignCalendar(startDate, endDate);
        }

        this.setState({ filters: updatedFilterValue, [type]: val }, () => this._onFetch({ ...tableMeta, pageIndex: 0 }));
    }

    _onFetch = async (state) => {
        const { hideProgress, showProgress, notificationSystem } = this.props;
        const { filters } = this.state;
        const {
            pageSize, pageIndex,
        } = state;

        const filterDate = getFilterValue(filters, 'date');
        const filterKeyword = getFilterValue(filters, 'all');
        const filterStatus = getFilterValue(filters, 'status');

        const payload = {
            limit: pageSize,
            page: pageIndex + 1,
            provider_id: PROVIDER_ID.GRAB_INTEGRATION,
            search: filterKeyword,
            status: filterStatus,
            start_date: this.convertDate(filterDate.start),
            end_date: this.convertDate(filterDate.end),
        };
        try {
            this.setState({ isFetching: true });
            showProgress();
            const res = await getSubmissionV12(payload);
            this.setState({
                submissionList: res.data,
                tableMeta: {
                    pageIndex,
                    pageSize,
                    total: res.meta.total,
                },
            });
        } catch (e) {
            this.setState({
                submissionList: [],
                tableMeta: {
                    pageIndex,
                    pageSize,
                    total: 0,
                },
            });
            notificationSystem.addNotification({
                title: 'Terjadi Kesalahan',
                message: catchError(e),
                level: 'error',
            });
        } finally {
            this.setState({ isFetching: false });
            hideProgress();
        }
    }

    convertDate = date => moment(date, 'DD-MM-YYYY').format('YYYY-MM-DD');

    handleDownloadPengajuan = async () => {
        const {
            notificationSystem, hideProgress, showProgress,
        } = this.props;
        const {
            submissionList: data,
        } = this.state;

        showProgress();

        try {
            if (data.length <= 0) throw Error('No Data Record Found');

            const res = await printExcel({ data });
            window.location = res.data;
        } catch (err) {
            notificationSystem.addNotification({
                title: 'Terjadi Kesalahan',
                message: catchError(err),
                level: 'error',
            });
        }

        hideProgress();
    }

    callEditDetailHandler = ({ original }) => {
        const { router } = this.props;
        const { submission_no: id, id_outlet: outletId } = original;
        router.push(`/marketplace/grabfood/detail/${id}?outlet_id=${outletId}`);
    }

    render() {
        const {
            filters, submissionList, tableMeta, isFetching,
        } = this.state;

        const search = getFilterValue(filters, 'all');

        return (
            <div>
                <section className="panel">
                    <div className="panel-heading table-header">
                        <div className="row">
                            <div className="col-md-12">
                                <div style={{ width: '100%' }}>
                                    <div className="row">
                                        <div className="col-md-5 pt-xs">
                                            <h4 className="panel-title">Grab Food</h4>
                                        </div>
                                        <div className="col-md-4" style={{ paddingLeft: '0px', float: 'right' }}>
                                            <InputText
                                                classes="filter"
                                                placeholder="Cari ..."
                                                changeEvent={value => this.updateFilter(value, 'all')}
                                                wait={500}
                                            />
                                        </div>
                                        <div className="col-md-2" style={{ paddingLeft: '0px', float: 'right' }}>
                                            <Select
                                                data={STATUS_LIST}
                                                value={filters.find(val => val.id === 'status').value}
                                                changeEvent={val => this.updateFilter(val, FILTER_TYPES.STATUS)}
                                            />
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div className="panel-body">
                        <Table
                            columns={COLUMN_CONFIG}
                            data={submissionList}
                            pageIndex={tableMeta.pageIndex}
                            rowLimit={tableMeta.pageSize}
                            totalData={tableMeta.total}
                            onRowClick={this.callEditDetailHandler}
                            fetchData={this._onFetch}
                            isLoading={isFetching}
                            searchQuery={search}
                        />
                    </div>
                </section>
            </div>
        );
    }
}
