import React from 'react';
import NumberDateTimeColumn from '../../../../../components/table/components/NumberDateTimeColumn';
import MarketplaceStatus from '../../components/MarketplaceStatusGrab';

export const COLUMN_CONFIG = [
    {
        Header: 'Merchant Name',
        accessor: 'merchant_name',
        unsortable: true,
    },
    {
        Header: 'Email',
        accessor: 'email',
        colMinWidth: 180,
        unsortable: true,
    },
    {
        Header: 'Outlet',
        accessor: 'outlet_name',
        unsortable: true,
    },
    {
        Header: 'Store id',
        accessor: 'mid',
        unsortable: true,
    },
    {
        Header: 'Email Grab Merchant',
        accessor: 'marketplace_merchant_information.email',
        unsortable: true,
    },
    {
        Header: 'Owner Name',
        accessor: 'owner_name',
        unsortable: true,
    },
    {
        Header: 'Status',
        accessor: 'status',
        Cell: MarketplaceStatus,
        colMinWidth: 150,
        unsortable: true,
    },
    {
        Header: 'Created Date',
        accessor: 'createdate',
        Cell: NumberDateTimeColumn,
        unsortable: true,
    },
    {
        Header: 'Updated Date',
        accessor: 'updatedate',
        unsortable: true,
        Cell: ({ row }) => {
            const { original: { log_update_name: logName } } = row;
            return (
                <div style={{ display: 'flex', flexDirection: 'column' }}>
                    <NumberDateTimeColumn {...row} />
                        {logName && (
                        <div>{`By ${logName}`}</div>
                    )}
                </div>
            );
        },
    },
];

export const STATUS_LIST = [
    {
        value: '',
        option: 'All',
    },
    {
        value: '0',
        option: 'Unpaired',
    },
    {
        value: '1',
        option: 'In progress',
    },
    {
        value: '2',
        option: 'Rejected',
    },
    {
        value: '4',
        option: 'Approved',
    },
    {
        value: '5',
        option: 'Unpair Request',
    },
    {
        value: '6',
        option: 'Waiting Integration',
    },
    {
        value: '7',
        option: 'Integrated',
    },
];

export const DATA_SELECT_STATUS = [
    {
        id: '0',
        label: 'Unpaired',
    },
    {
        id: '1',
        label: 'In progress',
    },
    {
        id: '2',
        label: 'Rejected',
    },
    {
        id: '4',
        label: 'Approved',
    },
    {
        id: '5',
        label: 'Unpair Request',
    },
    {
        id: '6',
        label: 'Waiting Integration',
    },
    {
        id: '7',
        label: 'Integrated',
    },
];

export const STATUS_INTEGRATION = {
    UNPAIRED: '0',
    IN_PROGRESS: '1',
    REJECTED: '2',
    APPROVED: '4',
    UNPAIR_REQUEST: '5',
    WAITING_INTEGRATION: '6',
    INTEGRATED: '7',
};

export const DATA_SELECT_STATUS_BY_STATUS = {
    [STATUS_INTEGRATION.UNPAIRED]: [0, 4],
    [STATUS_INTEGRATION.IN_PROGRESS]: [1, 2, 6],
    [STATUS_INTEGRATION.REJECTED]: [2],
    [STATUS_INTEGRATION.APPROVED]: [3, 6],
    [STATUS_INTEGRATION.UNPAIR_REQUEST]: [0, 4],
    [STATUS_INTEGRATION.WAITING_INTEGRATION]: [5, 2, 6],
    [STATUS_INTEGRATION.INTEGRATED]: [6],
};

export const FILTER_TYPES = {
    DATE: 'date',
    STATUS: 'status',
};
