import React, { Component } from 'react';
import { PropTypes } from 'prop-types';
import moment from 'moment';

import Table from '../../../../components/retina/table/Table';
import Select from '../../../../components/form/Select';

import CoreHOC from '../../../../core/CoreHOC';
import { catchError } from '../../../../utils/helper';

import * as walletApi from '../../../../data/wallet';

import { tableColumn, statusFilterList, FILTER_TYPES } from './config/grabConfig';
import { SUB_TYPE_SUBMISSIONS } from '../../../../enum/submissionType';
import { PROVIDER_ID } from '../../../../data/setting/marketplace/enum';
import { getFilterValue, getUpdatedFilterValue } from '../../../../utils/table.util';
import InputText from '../../../../components/form/InputText';


class GrabFoodSubmission extends Component {
  static propTypes = {
    calendar: PropTypes.shape({
      start: PropTypes.string,
      end: PropTypes.string,
    }),
    assignCalendar: PropTypes.func,
    assignButtons: PropTypes.func,
    router: PropTypes.shape({
      push: PropTypes.func,
    }),
    notificationSystem: PropTypes.shape({
      addNotification: PropTypes.func,
    }),
  }

  static defaultProps = {
    calendar: {
      start: '',
      end: '',
    },
    assignCalendar: null,
    assignButtons: null,
    router: {
      push: null,
    },
    notificationSystem: ({
      addNotification: () => {
        // do nothing
      },
    }),
  }

  constructor(props) {
    super(props);
    const date = props.calendar;
    this.state = {
      filterTable: [
        { id: FILTER_TYPES.FILTER_DATE, value: { start: date.start, end: date.end } },
        { id: FILTER_TYPES.FILTER_STATUS, value: '0' },
        { id: FILTER_TYPES.FILTER_ALL, value: '' },
      ],
      isFetch: false,
      tableData: [],
      tableMeta: {
        pageIndex: 0,
        pageSize: 10,
        total: 0,
      },
    };
  }

  componentWillMount() {
    const {
      assignCalendar, assignButtons,
    } = this.props;
    const { tableMeta } = this.state;
    assignCalendar(null, null, (startDate, endDate) => {
      this._updateFilters(FILTER_TYPES.FILTER_DATE, { start: startDate, end: endDate });
    });
    assignButtons();
    this.fetchDataHandler(tableMeta);
  }

  callEditDetailHandler = ({ original: data }) => {
    const { router } = this.props;
    router.push(`/marketplace/grabfoodSubmission/detail-grabfood/${data.submission_no}`);
  }

  reformateDate = val => moment(val, 'DD-MM-YYYY').format('YYYY-MM-DD');

  _updateFilters = (type, value) => {
    const { assignCalendar } = this.props;
    const { filterTable, tableMeta } = this.state;

    const updatedFilterValue = getUpdatedFilterValue(filterTable, type, value);
    if (type === FILTER_TYPES.FILTER_DATE) {
      const { start: startDate, end: endDate } = value;
        assignCalendar(startDate, endDate);
    }

    this.setState({ filterTable: updatedFilterValue }, () => this.fetchDataHandler({ ...tableMeta, pageIndex: 0 }));
  }

  fetchDataHandler = async (state) => {
    const { notificationSystem } = this.props;
    const { filterTable } = this.state;
    const {
      pageIndex, pageSize, sortAccessor, sortDirection,
    } = state;

    const filterAll = getFilterValue(filterTable, FILTER_TYPES.FILTER_ALL);
    const filterDate = getFilterValue(filterTable, FILTER_TYPES.FILTER_DATE);
    const filterStatus = getFilterValue(filterTable, FILTER_TYPES.FILTER_STATUS);

    const payload = {
      page: parseInt(pageIndex, 10) + 1,
      limit: pageSize,
      type: SUB_TYPE_SUBMISSIONS.GRAB,
      provider_id: PROVIDER_ID.GRABFOOD_SUBMISSION,
      ...filterAll && ({ search: filterAll }),
      ...filterDate && ({
        start_date: this.reformateDate(filterDate.start),
        end_date: this.reformateDate(filterDate.end),
      }),
      ...filterStatus && filterStatus !== statusFilterList[0].id && ({ status: filterStatus }),
      ...sortAccessor && { order: sortAccessor },
      ...sortDirection && { sort: sortDirection },
    };
    this.setState({ isFetch: true });
    try {
      const res = await walletApi.getListSubmission(payload);
      const { total } = res.meta;
      this.setState({
        isFetch: false,
        tableData: res.data,
        tableMeta: {
          pageIndex,
          pageSize,
          total,
          ...sortAccessor && { sortAccessor },
          ...sortDirection && { sortDirection },
        },
      });
    } catch (e) {
      notificationSystem.addNotification({
        title: 'Gagal mendapatkan data',
        message: catchError(e),
        level: 'error',
      });
      this.setState({
        isFetch: false,
        tableData: [],
        tableMeta: {
          pageIndex,
          pageSize,
          total: 0,
          ...sortAccessor && { sortAccessor },
          ...sortDirection && { sortDirection },
        },
      });
    }
  }

  render() {
    const {
 filterTable, isFetch, tableData, tableMeta,
} = this.state;

    const search = getFilterValue(filterTable, FILTER_TYPES.FILTER_ALL);
    const statusFilter = getFilterValue(filterTable, FILTER_TYPES.FILTER_STATUS);

    return (
      <div>
        <section className="panel">
          <div className="panel-heading table-header">
            <div className="row">
              <div className="col-md-5">
                <h4 className="title">Grab Food Submission</h4>
              </div>
              <div className="col-md-4">
                <Select
                  data={statusFilterList}
                  value={statusFilter}
                  changeEvent={val => this._updateFilters(FILTER_TYPES.FILTER_STATUS, val)}
                />
              </div>
              <div className="col-md-3">
                <InputText
                  classes="filter"
                  placeholder="Cari ..."
                  changeEvent={val => this._updateFilters(FILTER_TYPES.FILTER_ALL, val)}
                  wait={500}
                />
              </div>
            </div>
            <div className="panel-body">
              <Table
                columns={tableColumn}
                data={tableData}
                pageIndex={tableMeta.pageIndex}
                rowLimit={tableMeta.pageSize}
                totalData={tableMeta.total}
                isLoading={isFetch}
                searchQuery={search}
                onRowClick={this.callEditDetailHandler}
                fetchData={this.fetchDataHandler}
              />
            </div>
          </div>
        </section>
      </div>
    );
  }
}

export default CoreHOC(GrabFoodSubmission);
