import React, { Component } from 'react';
import PropTypes from 'prop-types';
import moment from 'moment';
import { debounce } from 'lodash';

import CoreHOC from '../../../../core/CoreHOC';
import { getUpdatedFilterValue, getFilterValue } from '../../../../utils/table.util';
import Autocomplete from '../../../../components/form/Autocomplete';
import SelectMultiple from '../../../../components/form/SelectMultiple';
import InputText from '../../../../components/form/InputText';
import Table from '../../../../components/retina/table/Table';
import { catchError } from '../../../../utils/helper';

import {
    getDetailsReport,
    downloadReportDetails,
    getOutletList,
} from '../../../../data/users/report/weborder';
import { getPaymentList } from '../../../../data/users/report/settlements';

import { tableColumnDetailsReport, filterType } from './table';
import './style.less';

const defaultDate = {
    start: moment().subtract(1, 'day').format('DD/MM/YYYY'),
    end: moment().format('DD/MM/YYYY'),
};

@CoreHOC
export default class TransactionDetailsReport extends Component {
    static propTypes = {
        calendar: PropTypes.shape({
            start: PropTypes.string,
            end: PropTypes.string,
        }),
        notificationSystem: PropTypes.shape({
            addNotification: PropTypes.func,
        }),
        router: PropTypes.shape({
            push: PropTypes.func,
        }),
        assignCalendar: PropTypes.func,
        assignFilterColoumn: PropTypes.func,
        assignRangeDate: PropTypes.func,
        assignButtons: PropTypes.func,
        hideProgress: PropTypes.func.isRequired,
        showProgress: PropTypes.func.isRequired,
    };

    static defaultProps = {
        calendar: {
            start: '',
            end: '',
        },
        notificationSystem: ({
            addNotification: () => { },
        }),
        router: {
            push: null,
        },
        assignCalendar: () => { },
        assignFilterColoumn: () => { },
        assignRangeDate: () => { },
        assignButtons: () => { },
    };

    constructor(props) {
        super(props);

        this.state = {
            paymentList: [],
            outletList: [],
            tableFilter: [
                { id: filterType.CALENDAR, value: defaultDate },
                { id: filterType.PAYMENT_METHOD, value: [] },
                { id: filterType.OUTLET, value: '' },
                { id: filterType.SEARCH, value: '' },
            ],
            tableData: [],
            tableMeta: {
                pageIndex: 0,
                limit: 10,
                totalData: 0,
                sortAccessor: '',
                sortDirection: '',
            },
            tableLoading: false,
        };
    }

    componentWillMount = () => {
        const {
            assignFilterColoumn, assignRangeDate, assignCalendar, assignButtons,
        } = this.props;

        assignFilterColoumn([]);
        assignRangeDate([]);
        const { start, end } = defaultDate;
        assignCalendar(start, end, (startDate, endDate) => {
            this.changeDateHandler(startDate, endDate);
        });
        assignButtons([
            {
                type: 'primary',
                content: (
                    <span>
                        <i className="fa fa-download" />
                        Download Laporan
                    </span>
                ),
                action: () => this.downloadLaporan(),
            },
        ]);
        this.getPaymentList();
        this.fetchReportList({ pageIndex: 0 });
    }

    componentDidUpdate(prevProps, prevState) {
        const {
            tableFilter: prevTableFilter,
        } = prevState;
        const {
            tableFilter: nextTableFilter,
        } = this.state;

        if (prevTableFilter !== nextTableFilter) {
            this.fetchReportList({ pageIndex: 0 });
        }
    }

    fetchReportList = async (tableState) => {
        const {
            showProgress, hideProgress, notificationSystem,
        } = this.props;
        const {
            tableFilter,
        } = this.state;

        const {
            pageIndex,
            pageSize,
            sortAccessor,
            sortDirection,
        } = tableState;

        const filterOutlet = getFilterValue(tableFilter, filterType.OUTLET);
        const filterPayment = getFilterValue(tableFilter, filterType.PAYMENT_METHOD);
        const calendar = getFilterValue(tableFilter, filterType.CALENDAR);
        const filterKeyword = getFilterValue(tableFilter, filterType.SEARCH);

        const payload = {
            start_date: moment(calendar.start, 'DD-MM-YYYY').format('YYYY-MM-DD'),
            end_date: moment(calendar.end, 'DD-MM-YYYY').format('YYYY-MM-DD'),
            ...filterKeyword && {
                search: filterKeyword,
            },
            ...filterOutlet && {
                outlet: filterOutlet,
            },
            ...filterPayment.length && {
                payment_method: filterPayment,
            },
            limit: pageSize || 10,
            page: +pageIndex + 1,
            ...sortAccessor && {
                'sort[]': `${sortAccessor},${sortDirection}`,
            },
        };

        this.setState({ tableLoading: true });
        showProgress();
        try {
            const response = await getDetailsReport(payload);
            const { data = [], meta: { current_page: currPage = 1, per_page: limitPerPage = 0, total: totalData = 0 } = {} } = response || {};

            this.setState({
                tableData: data,
                tableMeta: {
                    pageIndex: +currPage - 1,
                    limit: limitPerPage,
                    totalData,
                    sortAccessor: sortAccessor || '',
                    sortDirection: sortDirection || '',
                },
            });
        } catch (e) {
            notificationSystem.addNotification({
                title: 'Gagal mendapatkan data',
                message: catchError(e),
                level: 'error',
            });
        } finally {
            hideProgress();
            this.setState({ tableLoading: false });
        }
    }

    changeTableFilter = (val, type) => {
        const { tableFilter } = this.state;
        const updatedFilterValue = getUpdatedFilterValue(tableFilter, type, val);

        this.setState({ tableFilter: updatedFilterValue, [type]: val });
    }

    getPaymentList = async () => {
        const { notificationSystem } = this.props;

        try {
            const res = await getPaymentList();
            if (!res.data) throw Error(res.msg);
            const paymentList = res.data.map(x => ({ value: x.id, label: x.name }));

            this.setState({
                paymentList,
            });
        } catch (err) {
            notificationSystem.addNotification({
                title: 'Gagal mendapatkan data',
                message: catchError(err),
                level: 'error',
            });
        }
    }

    getOutletList = async (keyword) => {
        const { notificationSystem, hideProgress } = this.props;

        try {
            const res = await getOutletList({
                limit: 50,
                page: 1,
                ...keyword && { search: keyword },
            });
            if (!res.data) throw Error(res.msg);

            this.setState({
                outletList: res.data.map(x => ({ id: String(x.cabang_id), name: x.outlet })),
            });
        } catch (err) {
            notificationSystem.addNotification({
                title: 'Gagal mendapatkan data',
                message: catchError(err),
                level: 'error',
            });
        } finally {
            hideProgress();
        }
    }

    changeDateHandler = (startDate, endDate) => {
        const { assignCalendar } = this.props;
        assignCalendar(startDate, endDate);

        this.changeTableFilter({
            start: startDate,
            end: endDate,
        }, filterType.CALENDAR);
    }

    changeEventPayment = (val) => {
        this.changeTableFilter(val.map(v => v.value), filterType.PAYMENT_METHOD);
    }

    changeEventOutlet = async (outletID, keyword, options = {}) => {
        if (options.isReset) {
            this.changeTableFilter('', filterType.OUTLET);
            this.setState({ outletList: [] });
            return;
        }
        const { showProgress } = this.props;
        if (outletID) showProgress();
        await this.getOutletList(keyword);
        this.changeTableFilter(outletID, filterType.OUTLET);
    }

    changeEventSearch = (keyword) => {
        this.changeTableFilter(keyword, filterType.SEARCH);
    }

    downloadLaporan = async () => {
        const {
            showProgress, hideProgress, notificationSystem, calendar,
        } = this.props;
        const {
            tableFilter,
            tableMeta: {
                sortAccessor,
                sortDirection,
            },
        } = this.state;

        const filterOutlet = getFilterValue(tableFilter, filterType.OUTLET);
        const filterPayment = getFilterValue(tableFilter, filterType.PAYMENT_METHOD);
        const filterKeyword = getFilterValue(tableFilter, filterType.SEARCH);

        showProgress();

        const payload = {
            start_date: moment(calendar.start, 'DD-MM-YYYY').format('YYYY-MM-DD'),
            end_date: moment(calendar.end, 'DD-MM-YYYY').format('YYYY-MM-DD'),
            ...filterKeyword && {
                search: filterKeyword,
            },
            ...filterOutlet && {
                outlet: filterOutlet,
            },
            ...filterPayment.length && {
                payment_method: filterPayment,
            },
            ...sortAccessor && {
                'sort[]': `${sortAccessor},${sortDirection}`,
            },
        };

        try {
            const res = await downloadReportDetails(payload);
            window.open(res.data);
        } catch (err) {
            notificationSystem.addNotification({
                title: 'Terjadi Kesalahan',
                message: catchError(err),
                level: 'error',
            });
        } finally {
            hideProgress();
        }
    }

    render() {
        const {
            outletList, paymentList, tableFilter,
            tableData, tableLoading, tableMeta,
        } = this.state;

        const filterOutlet = getFilterValue(tableFilter, filterType.OUTLET);
        const filterPayment = getFilterValue(tableFilter, filterType.PAYMENT_METHOD);
        const filterKeyword = getFilterValue(tableFilter, filterType.SEARCH);

        return (
            <section className="panel">
                <div className="panel-heading table-header">
                    <div className="row" style={{ marginBottom: '15px' }}>
                        <div className="col-md-12">
                            <h4 className="panel-title" style={{ paddingTop: '8px' }}>Weborder Transaction Details</h4>
                        </div>
                    </div>
                    <div className="row wo-table-filter">
                        <div className="col-md-4 wo-select-multi" style={{ zIndex: '99' }}>
                            <SelectMultiple
                                options={paymentList}
                                selector="label"
                                value={filterPayment}
                                changeEvent={(val) => { this.changeEventPayment(val); }}
                                placeholder="Semua Metode Pembayaran"
                            />
                        </div>
                        <div className="col-md-3 p-none" style={{ zIndex: '99' }}>
                            <Autocomplete
                                data={outletList}
                                value={filterOutlet}
                                changeEvent={debounce(this.changeEventOutlet, 700)}
                                selector="name"
                                name="outlets"
                                placeholder="Cari outlet ..."
                            />
                        </div>
                        <div className="col-md-3">
                            <InputText
                                classes="filter"
                                placeholder="Cari ..."
                                changeEvent={debounce(this.changeEventSearch, 700)}
                            />
                        </div>
                    </div>
                </div>
                <div className="panel-body">
                    <Table
                        columns={tableColumnDetailsReport}
                        data={tableData}
                        pageIndex={tableMeta.pageIndex}
                        rowLimit={tableMeta.limit}
                        totalData={tableMeta.totalData}
                        fetchData={this.fetchReportList}
                        isLoading={tableLoading}
                        searchQuery={filterKeyword}
                    />
                </div>
            </section>
        );
    }
}
