import React from 'react';

import DateColumn from '../../../../components/table/components/DateColumn';
import DateTimeColumn from '../../../../components/table/components/DateTimeColumn';
import PriceColumn from '../../../../components/table/components/PriceColumn';

export const TABLE_META_TRANSACTION_DETAILS = [
    {
        Header: 'MID',
        accessor: 'mid',
        colWidth: 150,
    },
    {
        Header: 'Outlet',
        accessor: 'outlet',
        colWidth: 150,
    },
    {
        Header: 'No. Transaction',
        accessor: 'transaction_no',
        colWidth: 200,
    },
    {
        Header: 'Payment Number',
        accessor: 'payment_number',
        colWidth: 350,
    },
    {
        Header: 'Payment Method',
        accessor: 'payment_method',
        colWidth: 160,
    },
    {
        Header: 'Date',
        accessor: 'transaction_date',
        colWidth: 180,
        Cell: DateTimeColumn,
    },
    {
        Header: 'Sales (Rp)',
        accessor: 'settlement_amount',
        colWidth: 200,
        Cell: PriceColumn,
    },
    {
        Header: 'MDR (Rp)',
        accessor: 'mdr_amount',
        colWidth: 200,
        Cell: PriceColumn,
    },
];

export const TABLE_META_SETTLEMET_REQUEST = [
    {
        Header: 'Request Date',
        accessor: 'request_date',
        Cell: (data) => {
            const {
                row: { original: { request_date: requestDate } },
            } = data;

            return (
                <DateColumn
                    data={requestDate}
                    displayFormat="DD-MM-YYYY"
                    emptyValue="-"
                />
            );
        },
        colWidth: 130,
        colMinWidth: 100,
        resizable: false,
    },
    {
        Header: 'MID',
        accessor: 'mid',
        colWidth: 150,
        colMinWidth: 120,
        resizable: false,
    },
    {
        Header: 'Outlet',
        accessor: 'outlet',
        colWidth: 180,
        colMinWidth: 150,
    },
    {
        Header: 'Transaction Number',
        accessor: 'transaction_no',
        colWidth: 250,
        colMinWidth: 230,
        resizable: false,
    },
    {
        Header: 'Amount (Rp)',
        accessor: 'amount',
        Cell: PriceColumn,
        colWidth: 180,
        colMinWidth: 130,
        resizable: false,
    },
    {
        Header: 'Transfer Free (Rp)',
        accessor: 'transfer_fee',
        Cell: PriceColumn,
        colWidth: 180,
        colMinWidth: 130,
        resizable: false,
    },
    {
        Header: 'Bank',
        accessor: 'bank',
        colWidth: 250,
        colMinWidth: 200,
    },
    {
        Header: 'Account Number',
        accessor: 'account_number',
        colWidth: 220,
        colMinWidth: 200,
    },
    {
        Header: 'Account Holder',
        accessor: 'account_holder',
        colWidth: 200,
        colMinWidth: 180,
    },
    {
        Header: 'Status',
        accessor: 'status',
        resizable: false,
    },
    {
        Header: 'Settlement Data',
        accessor: 'settlement_date',
        Cell: (data) => {
            const {
                row: { original: { settlement_date: settlementDate } },
            } = data;

            return (
                <DateColumn
                    data={settlementDate}
                    displayFormat="DD-MM-YYYY"
                    emptyValue="-"
                />
            );
        },
        colWidth: 150,
        colMinWidth: 100,
        resizable: false,
    },
];

export const TABLE_META_TRANSACTION_SUMMARY = [
    {
        Header: 'MID',
        accessor: 'mid',
        colWidth: 150,
        colMinWidth: 120,
        resizable: false,
    },
    {
        Header: 'Outlet',
        accessor: 'outlet',
        colWidth: 180,
        colMinWidth: 150,
    },
    {
        Header: 'Beginning Balance (Rp)',
        accessor: 'beginning_balance',
        colWidth: 200,
        Cell: PriceColumn,
    },
    {
        Header: 'Sales (Rp)',
        accessor: 'sales_amount',
        colWidth: 200,
        Cell: PriceColumn,
    },
    {
        Header: 'MDR (Rp)',
        accessor: 'mdr_amount',
        colWidth: 200,
        Cell: PriceColumn,
    },
    {
        Header: 'Transfer Fee (Rp)',
        accessor: 'transfer_fee',
        colWidth: 200,
        Cell: PriceColumn,
    },
    {
        Header: 'Settlement (Rp)',
        accessor: 'settlement_amount',
        colWidth: 200,
        Cell: PriceColumn,
    },
    {
        Header: 'Ending Balance (Rp)',
        accessor: 'ending_balance',
        colWidth: 200,
        Cell: PriceColumn,
    },
];

export const PAYMENT_METHOD_LIST = [
    {
        value: '',
        label: 'Semua Metode Pembayaran',
    },
    {
        value: 1,
        label: 'Tunai',
    },
    {
        value: 2,
        label: 'EDC',
    },
    {
        value: 3,
        label: 'Kupon',
    },
    {
        value: 4,
        label: 'Transfer Bank',
    },
    {
        value: 5,
        label: 'Komplimen',
    },
    {
        value: 6,
        label: 'Wallet (QRIS)',
    },
    {
        value: 7,
        label: 'Poin',
    },
    {
        value: 8,
        label: 'ECR',
    },
    {
        value: 9,
        label: 'Deposit',
    },
    {
        value: 10,
        label: 'OVO Grabfood',
    },
    {
        value: 11,
        label: 'Multiple',
    },
    {
        value: 12,
        label: 'Diskon',
    },
    {
        value: 13,
        label: 'Wallet (Push2Pay)',
    },
];

export const FILTER_TYPE = {
    DATE_RANGE: 'dateRange',
    PAYMENT_METHOD: 'paymentMethod',
    OUTLET: 'outlet',
    STATUS: 'status',
    SEARCH: 'all',
};

export const STATUS_LIST = [
    {
        value: 0,
        label: 'Menunggu',
    },
    {
        value: 2,
        label: 'Berhasil',
    },
    {
        value: 3,
        label: 'Gagal',
    },
];
