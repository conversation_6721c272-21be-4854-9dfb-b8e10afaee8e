import React, { Component } from 'react';
import PropTypes from 'prop-types';
import update from 'immutability-helper';
import moment from 'moment';

import { debounce } from 'lodash';
import InputText from '../../../../components/form/InputText';
import Table from '../../../../components/retina/table/Table';
import Select from '../../../../components/form/Select';
import Autocomplete from '../../../../components/form/Autocomplete';

import {
    getOutlet, getDataTranscationDetail, downloadDataTranscationDetail,
} from '../../../../data/majoolite';
import { getFilterValue, getUpdatedFilterValue } from '../../../../utils/table.util';
import CoreHOC from '../../../../core/CoreHOC';
import { catchError } from '../../../../utils/helper';

import {
    TABLE_META_TRANSACTION_DETAILS, FILTER_TYPE, PAYMENT_METHOD_LIST,
} from './config';

@CoreHOC
export default class TransactionDetails extends Component {
    constructor(props) {
        super(props);

        const { calendar } = this.props;
        this.state = {
            outletList: [],
            outletFilter: {
                search: '',
                page: 1,
            },
            excelFilter: {
                search: '',
                limit: '10',
                page: '1',
            },
            filterTable: [
                { id: FILTER_TYPE.DATE_RANGE, value: calendar },
                { id: FILTER_TYPE.OUTLET, value: '' },
                { id: FILTER_TYPE.PAYMENT_METHOD, value: '' },
                { id: FILTER_TYPE.SEARCH, value: '' },
            ],
            isFetch: false,
            tableData: [],
            tableMeta: {
                pageSize: 10,
                pageIndex: 0,
                total: 0,
            },
        };
    }

    componentDidMount = async () => {
        const { assignCalendar, assignButtons } = this.props;
        const { tableMeta } = this.state;

        assignCalendar(null, null, (startDate, endDate) => {
            this.changeDateHandle(startDate, endDate);
        });
        assignButtons([
            {
                type: 'primary',
                content: (
                    <span>
                    <i className="fa fa-download" />
                    Download Laporan
                    </span>
                ),
                action: () => this.downloadReport(),
            },
        ]);
        this._onFetch({ ...tableMeta, pageIndex: 0 });
    }

    changeDateHandle = async (start, end) => {
        const { assignCalendar } = this.props;
        assignCalendar(start, end);

        this.updateCustomFilter({ start, end }, FILTER_TYPE.DATE_RANGE);
    }

    eventScroll = async (val) => {
        const { outletFilter } = this.state;
        const newOutletFilter = update(outletFilter, {
            page: { $set: val + 1 },
        });

        this.setState({
            outletFilter: newOutletFilter,
        }, () => {
            this.getOutletData();
        });
    }

    cariKeyword = async (search) => {
        const { outletFilter } = this.state;
        const newOutletFilter = update(outletFilter, {
            page: { $set: 1 },
            search: { $set: search },
        });

        this.setState({
            outletFilter: newOutletFilter,
        }, () => {
            this.getOutletData();
        });
    }

    getOutletData = async () => {
        const { outletFilter: { page, search } } = this.state;
        const { notificationSystem } = this.props;

        const payload = {
            limit: 5,
            page,
            search,
            channel: 6, // channel khusus majoo_lite toko online settlement
        };

        try {
            const res = await getOutlet(payload);
            const { data: outletList } = res;
            const newOutletList = outletList.map(({ cabang_id: id, outlet }) => ({ id: `${id}`, name: outlet }));

            this.setState({
                outletList: newOutletList,
            });
        } catch (err) {
            notificationSystem.addNotification({
                title: 'Get data failed',
                message: catchError(err),
                level: 'error',
            });
        }
    }

    changeEventUser = async (val, e) => {
        this.updateCustomFilter(val, FILTER_TYPE.OUTLET);
        await this.cariKeyword(e);
    }

    _onFetch = async (state) => {
        const { filterTable } = this.state;
        const { notificationSystem } = this.props;
        const {
            pageIndex, pageSize, sortAccessor, sortDirection,
        } = state;

        const { start, end } = getFilterValue(filterTable, FILTER_TYPE.DATE_RANGE);
        const outlet = getFilterValue(filterTable, FILTER_TYPE.OUTLET);
        const paymentMethod = getFilterValue(filterTable, FILTER_TYPE.PAYMENT_METHOD);
        const search = getFilterValue(filterTable, FILTER_TYPE.SEARCH);

        const payload = {
            start_date: moment(start, 'DD/MM/YYYY').format('YYYY-MM-DD'),
            end_date: moment(end, 'DD/MM/YYYY').format('YYYY-MM-DD'),
            limit: pageSize,
            page: Number(pageIndex) + 1,
            channel: 6, // channel khusus majoo_lite toko online settlement
            ...search && { search },
            ...outlet && { outlet },
            ...paymentMethod && { payment_method: paymentMethod },
            ...sortAccessor && { order: sortAccessor },
            ...sortDirection && { sort: sortDirection },
        };
        try {
            this.setState({ isFetch: true });
            const { data, meta: { total } } = await getDataTranscationDetail(payload);
            this.setState({
                isFetch: false,
                tableData: data || [],
                tableMeta: {
                    pageIndex,
                    pageSize,
                    total,
                    ...sortAccessor && { sortAccessor },
                    ...sortDirection && { sortDirection },
                },
            });
        } catch (e) {
            notificationSystem.addNotification({
                title: 'Gagal!',
                message: catchError(e),
                level: 'error',
            });
        } finally {
            const { excelFilter } = this.state;

            const newExcelFilter = update(excelFilter, {
                search: { $set: search || '' },
                limit: { $set: pageSize },
                page: { $set: pageIndex },
            });

            this.setState({
                isFetch: false,
                excelFilter: newExcelFilter,
            });
        }
    }

    downloadReport = async () => {
        const {
            notificationSystem, showProgress, hideProgress,
        } = this.props;
        const { filterTable, excelFilter } = this.state;
        const { limit, page, search } = excelFilter;

        const { start, end } = getFilterValue(filterTable, FILTER_TYPE.DATE_RANGE);
        const outlet = getFilterValue(filterTable, FILTER_TYPE.OUTLET);
        const paymentMethod = getFilterValue(filterTable, FILTER_TYPE.PAYMENT_METHOD);

        const payload = {
            start_date: moment(start, 'DD/MM/YYYY').format('YYYY-MM-DD'),
            end_date: moment(end, 'DD/MM/YYYY').format('YYYY-MM-DD'),
            limit,
            page,
            channel: 6, // channel khusus majoo_lite toko online settlement
            ...search && { search },
            ...outlet && { outlet },
            ...paymentMethod && { payment_method: paymentMethod },
        };

        try {
            showProgress();
            const res = await downloadDataTranscationDetail(payload);
            const { data } = res;

            window.open(data);
        } catch (err) {
            notificationSystem.addNotification({
                title: 'Get data failed',
                message: catchError(err),
                level: 'error',
            });
        } finally {
            hideProgress();
        }
    }

    updateCustomFilter = (val, type) => {
        const { filterTable, tableMeta } = this.state;
        const updatedFilterValue = getUpdatedFilterValue(filterTable, type, val);
        this.setState({ filterTable: updatedFilterValue }, () => this._onFetch({ ...tableMeta, pageIndex: 0 }));
    }

    render() {
        const {
            filterTable, outletList,
            tableData, tableMeta: { pageIndex, pageSize, total }, isFetch,
        } = this.state;

        const outletFilter = getFilterValue(filterTable, FILTER_TYPE.OUTLET);
        const PaymentMethodFilter = getFilterValue(filterTable, FILTER_TYPE.PAYMENT_METHOD);
        const keyword = getFilterValue(filterTable, FILTER_TYPE.SEARCH);

        return (
            <div>
                <section className="panel">
                    <div className="panel-heading table-header">
                        <div className="row">
                            <div className="col-md-9">
                                <h4
                                    className="panel-title"
                                    style={{ paddingTop: '8px' }}
                                >
                                    Transaction Details
                                </h4>
                            </div>
                        </div>
                    </div>
                    <div className="panel-heading table-header">
                        <div className="row">
                            <div className="col-md-4">
                                <Select
                                    data={PAYMENT_METHOD_LIST}
                                    value={PaymentMethodFilter}
                                    changeEvent={(val) => { this.updateCustomFilter(val, FILTER_TYPE.PAYMENT_METHOD); }}
                                />
                            </div>
                            <div className="col-md-4">
                                <Autocomplete
                                    data={outletList}
                                    selector="name"
                                    value={outletFilter}
                                    changeEvent={async (val, e) => { await this.changeEventUser(val, e); }}
                                    changeScroll={val => this.eventScroll(val)}
                                    placeholder="All Outlet"
                                    isPaginate
                                />
                            </div>
                            <div className="col-md-4">
                                <InputText
                                    classes="filter"
                                    placeholder="Cari ..."
                                    changeEvent={debounce(val => this.updateCustomFilter(val, FILTER_TYPE.SEARCH), 500)}
                                />
                            </div>
                        </div>
                    </div>
                    <div className="panel-body">
                        <Table
                            columns={TABLE_META_TRANSACTION_DETAILS}
                            data={tableData}
                            pageIndex={pageIndex}
                            rowLimit={pageSize}
                            totalData={total}
                            fetchData={this._onFetch}
                            isLoading={isFetch}
                            searchQuery={keyword}
                        />
                    </div>
                </section>
            </div>
        );
    }
}

TransactionDetails.propTypes = {
    calendar: PropTypes.shape({
        start: PropTypes.string,
        end: PropTypes.string,
    }),
    assignCalendar: PropTypes.func,
    notificationSystem: PropTypes.shape({
        addNotification: PropTypes.func,
    }),
    assignButtons: PropTypes.func.isRequired,
    showProgress: PropTypes.func.isRequired,
    hideProgress: PropTypes.func.isRequired,
};

TransactionDetails.defaultProps = {
    calendar: {
        start: '',
        end: '',
    },
    assignCalendar: () => {},
    notificationSystem: {
        addNotification: null,
    },
};
