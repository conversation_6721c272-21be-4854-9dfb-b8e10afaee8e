import React, { Component } from 'react';
import PropTypes from 'prop-types';
import moment from 'moment';
import update from 'immutability-helper';

import { debounce } from 'lodash';
import InputText from '../../../../components/form/InputText';
import Table from '../../../../components/retina/table/Table';
import SelectMultiple from '../../../../components/form/SelectMultiple';
import Autocomplete from '../../../../components/form/Autocomplete';
import UploadCSV from '../../../../components/UploadCSV';
import { getFilterValue, getUpdatedFilterValue } from '../../../../utils/table.util';
import {
    getSettlementRequest, downloadTemplate, getOutlet, updateStatus,
} from '../../../../data/majoolite';

import CoreHOC from '../../../../core/CoreHOC';
import { catchError, csvJSON } from '../../../../utils/helper';

import {
    TABLE_META_SETTLEMET_REQUEST, FILTER_TYPE, STATUS_LIST,
} from './config';

@CoreHOC
export default class SettlementRequest extends Component {
    constructor(props) {
        super(props);

        const { calendar } = this.props;
        this.state = {
            file: '',
            outletList: [],
            outletFilter: {
                search: '',
                page: 1,
            },
            excelFilter: {
                search: '',
                limit: '10',
                page: '1',
            },
            respondUpload: {
                success: undefined,
                failed: undefined,
                linkFailedData: '',
            },
            confirmDisabled: true,
            filterTable: [
                { id: FILTER_TYPE.DATE_RANGE, value: calendar },
                { id: FILTER_TYPE.OUTLET, value: '' },
                { id: FILTER_TYPE.STATUS, value: [] },
                { id: FILTER_TYPE.SEARCH, value: '' },
            ],
            isFetch: false,
            tableData: [],
            tableMeta: {
                pageSize: 10,
                pageIndex: 0,
                total: 0,
            },
        };
    }

    componentDidMount = async () => {
        const { assignCalendar, assignButtons } = this.props;
        const { tableMeta } = this.state;
        assignCalendar(null, null, (startDate, endDate) => {
            this.changeDateHandle(startDate, endDate);
        });
        assignButtons([
            {
                type: 'primary',
                content: (
                    <span>
                        <i className="fa fa-edit" />
                        Update Status
                    </span>
                ),
                action: () => this.callUploadHandle(),
            },
        ]);

        this.getOutletData();
        this._onFetch({ ...tableMeta, pageIndex: 0 });
    }

    changeDateHandle = async (start, end) => {
        const { assignCalendar } = this.props;
        assignCalendar(start, end);

        this.updateCustomFilter({ start, end }, FILTER_TYPE.DATE_RANGE);
    }

    eventScroll = async (val) => {
        const { outletFilter } = this.state;
        const newOutletFilter = update(outletFilter, {
            page: { $set: val + 1 },
        });

        this.setState({
            outletFilter: newOutletFilter,
        }, () => {
            this.getOutletData();
        });
    }

    cariKeyword = async (search) => {
        const { outletFilter } = this.state;
        const newOutletFilter = update(outletFilter, {
            page: { $set: 1 },
            search: { $set: search },
        });

        this.setState({
            outletFilter: newOutletFilter,
        }, () => {
            this.getOutletData();
        });
    }

    getOutletData = async () => {
        const { outletFilter: { page, search } } = this.state;
        const { notificationSystem } = this.props;

        const payload = {
            limit: 5,
            page,
            search,
            channel: 6, // channel khusus majoo_lite toko online settlement
        };

        try {
            const res = await getOutlet(payload);
            const { data: outletList } = res;
            const newOutletList = outletList.map(({ cabang_id: id, outlet }) => ({ id: `${id}`, name: outlet }));

            this.setState({
                outletList: newOutletList,
            });
        } catch (err) {
            notificationSystem.addNotification({
                title: 'Get data failed',
                message: catchError(err),
                level: 'error',
            });
        }
    }

    changeEventUser = async (val, e) => {
        this.updateCustomFilter(val, FILTER_TYPE.OUTLET);
        await this.cariKeyword(e);
    }

    updateCustomFilter = (val, type) => {
        const { filterTable, tableMeta } = this.state;
        const updatedFilterValue = getUpdatedFilterValue(filterTable, type, val);
        this.setState({ filterTable: updatedFilterValue }, () => this._onFetch({ ...tableMeta, pageIndex: 0 }));
    }

    _onFetch = async (state) => {
        const { filterTable } = this.state;
        const { notificationSystem } = this.props;
        const {
            pageIndex, pageSize, sortAccessor, sortDirection,
        } = state;

        const { start, end } = getFilterValue(filterTable, FILTER_TYPE.DATE_RANGE);
        const outlet = getFilterValue(filterTable, FILTER_TYPE.OUTLET);
        const status = getFilterValue(filterTable, FILTER_TYPE.STATUS).map(x => x.value).toString();
        const search = getFilterValue(filterTable, FILTER_TYPE.SEARCH);

        const payload = {
            start_date: moment(start, 'DD/MM/YYYY').format('YYYY-MM-DD'),
            end_date: moment(end, 'DD/MM/YYYY').format('YYYY-MM-DD'),
            limit: pageSize,
            page: Number(pageIndex) + 1,
            channel: 6, // channel khusus majoo_lite toko online settlement
            ...search && { search },
            ...outlet && { outlet },
            ...status && { status },
            ...sortAccessor && { order: sortAccessor },
            ...sortDirection && { sort: sortDirection },
        };

        try {
            this.setState({ isFetch: true });
            const { data, meta: { total } } = await getSettlementRequest(payload);
            this.setState({
                isFetch: false,
                tableData: data || [],
                tableMeta: {
                    pageIndex,
                    pageSize,
                    total,
                    ...sortAccessor && { sortAccessor },
                    ...sortDirection && { sortDirection },
                },
            });
        } catch (e) {
            notificationSystem.addNotification({
                title: 'Gagal!',
                message: catchError(e),
                level: 'error',
            });
        } finally {
            const { excelFilter } = this.state;

            const newExcelFilter = update(excelFilter, {
                search: { $set: search || '' },
                limit: { $set: pageSize },
                page: { $set: pageIndex },
            });

            this.setState({
                isFetch: false,
                excelFilter: newExcelFilter,
            });
        }
    }

    callUploadHandle = () => {
        this.uploadModal.showPopup();
    }

    downloadTemplateHandle = async () => {
        const {
            notificationSystem, showProgress, hideProgress,
        } = this.props;
        const { filterTable, excelFilter } = this.state;
        const { limit, page, search } = excelFilter;

        const { start, end } = getFilterValue(filterTable, FILTER_TYPE.DATE_RANGE);
        const outlet = getFilterValue(filterTable, FILTER_TYPE.OUTLET);
        const status = getFilterValue(filterTable, FILTER_TYPE.STATUS);

        showProgress();
        try {
            const payload = {
                start_date: moment(start, 'DD/MM/YYYY').format('YYYY-MM-DD'),
                end_date: moment(end, 'DD/MM/YYYY').format('YYYY-MM-DD'),
                limit,
                page,
                channel: 6, // channel khusus majoo_lite toko online settlement
                ...search && { search },
                ...outlet && { outlet },
                ...status && { status },
            };

            const res = await downloadTemplate(payload);
            const { data } = res;

            window.open(data);
        } catch (err) {
            notificationSystem.addNotification({
                title: 'Failed get Data',
                message: catchError(err),
                level: 'error',
            });
        } finally {
            hideProgress();
        }
    }

    readFileHandle = (file) => {
        this.setState({
            file,
            confirmDisabled: false,
        });
    }

    uploadData = async () => {
        const { file } = this.state;
        const { showProgress, hideProgress, notificationSystem } = this.props;

        showProgress();

        try {
            if (!file) throw Error('Please provide a file');
            const uploadData = csvJSON(file, ',');

            const data = uploadData.map((x) => {
                const settlementDate = x.settlement_date.split('\r')[0];

                return {
                        request_date: x.request_date,
                        mid: x.mid,
                        outlet: x.outlet,
                        transaction_no: x.transaction_no,
                        amount: parseInt(x.amount, 10),
                        transfer_fee: parseInt(x.transfer_fee, 10),
                        bank: x.bank,
                        account_number: x.account_number,
                        account_holder: x.account_holder,
                        settlement_date: settlementDate,
                };
            });

            const payload = {
                channel: 6, // channel khusus majoo_lite toko online settlement
                data,
            };

            const res = await updateStatus(payload);
            const {
                data: {
                    success_upload: success, failed_upload: failed, link_failed_data: linkFailedData,
                },
            } = res;

            this.setState({
                respondUpload: {
                    success: success || [],
                    failed: failed || [],
                    linkFailedData,
                },
                confirmDisabled: true,
            }, () => {
                notificationSystem.addNotification({
                    title: 'Success Upload',
                    level: 'success',
                });
            });
        } catch (err) {
            notificationSystem.addNotification({
                title: 'Failed get Data',
                message: catchError(err),
                level: 'error',
            });
        } finally {
            hideProgress();
        }
    }

    downloadErrorHandle = async () => {
        const {
          showProgress, hideProgress, notificationSystem,
        } = this.props;

        const { respondUpload: { linkFailedData } } = this.state;

        showProgress();

        try {
            window.open(linkFailedData);
        } catch (err) {
            notificationSystem.addNotification({
                title: 'Failed get Data',
                message: catchError(err),
                level: 'error',
            });
        } finally {
            hideProgress();
        }
    }

    render() {
        const {
            outletList, filterTable, respondUpload, confirmDisabled,
            tableData, tableMeta: { pageIndex, pageSize, total }, isFetch,
        } = this.state;

        const PaymentMethodFilter = getFilterValue(filterTable, FILTER_TYPE.OUTLET);
        const StatusFilter = getFilterValue(filterTable, FILTER_TYPE.STATUS);
        const keyword = getFilterValue(filterTable, FILTER_TYPE.SEARCH);

        return (
            <div>
                <section className="panel">
                    <div className="panel-heading table-header">
                        <div className="row">
                            <div className="col-md-9">
                                <h4
                                    className="panel-title"
                                    style={{ paddingTop: '8px' }}
                                >
                                    Settlement Request
                                </h4>
                            </div>
                        </div>
                    </div>
                    <div className="panel-heading table-header">
                        <div className="row">
                            <div className="col-md-5">
                                <Autocomplete
                                    data={outletList}
                                    selector="name"
                                    value={PaymentMethodFilter}
                                    changeEvent={async (val, e) => { await this.changeEventUser(val, e); }}
                                    changeScroll={val => this.eventScroll(val)}
                                    placeholder="All Outlet"
                                    isPaginate
                                />
                            </div>
                            <div className="col-md-4">
                                <SelectMultiple
                                    options={STATUS_LIST}
                                    value={StatusFilter}
                                    changeEvent={value => this.updateCustomFilter(value, FILTER_TYPE.STATUS)}
                                    placeholder="All Status"
                                />
                            </div>
                            <div className="col-md-3">
                                <InputText
                                    classes="filter"
                                    placeholder="Cari ..."
                                    changeEvent={debounce(val => this.updateCustomFilter(val, FILTER_TYPE.SEARCH), 500)}
                                />
                            </div>
                        </div>
                    </div>
                    <div className="panel-body">
                        <Table
                            columns={TABLE_META_SETTLEMET_REQUEST}
                            data={tableData}
                            pageIndex={pageIndex}
                            rowLimit={pageSize}
                            totalData={total}
                            fetchData={this._onFetch}
                            isLoading={isFetch}
                            searchQuery={keyword}
                        />
                    </div>
                </section>

                <UploadCSV
                    ref={(c) => { this.uploadModal = c; }}
                    title="Upload Status Laporan"
                    desc="Silahkan pilih file .csv untuk mengubah status secara bersamaan, gunakan tombol download yang tersedia apabila anda belum memiliki template."
                    confirmHandle={this.uploadData}
                    downloadHandle={this.downloadTemplateHandle}
                    downloadErrorHandle={this.downloadErrorHandle}
                    readFileHandle={this.readFileHandle}
                    resUpload={respondUpload}
                    confirmDisabled={confirmDisabled}
                />
            </div>
        );
    }
}

SettlementRequest.propTypes = {
    calendar: PropTypes.shape({
        start: PropTypes.string,
        end: PropTypes.string,
    }),
    assignCalendar: PropTypes.func,
    notificationSystem: PropTypes.shape({
        addNotification: PropTypes.func,
    }),
    assignButtons: PropTypes.func.isRequired,
    showProgress: PropTypes.func.isRequired,
    hideProgress: PropTypes.func.isRequired,
};

SettlementRequest.defaultProps = {
    calendar: {
        start: '',
        end: '',
    },
    assignCalendar: () => {},
    notificationSystem: {
        addNotification: null,
    },
};
