import { styled } from '../../../stitches.config';

export const Wrapper = styled('header', {
    position: 'fixed',
    backgroundColor: '#003737',
    border: '0',
    left: 0,
    right: 0,
    top: 0,
    display: 'flex',
    padding: '0',
    zIndex: 11,
    flexDirection: 'column',
});

export const InnerWrapper = styled('div', {
    height: '60px',
    display: 'flex',
    alignItems: 'center',
    padding: '0',
    gap: '10px',
});

export const TopMenuWrapper = styled('div', {
    marginLeft: '0px',
    display: 'none',
    '@md': {
        display: 'flex',
        minHeight: '43px',
    },
    '& > button': {
        padding: '0 17px',
        '&:hover': {
            backgroundColor: 'rgb(0, 31, 31)',
        },
    },
    '& > ul': {
        left: '17em',
    },
});

export const TopMenuContentWrapper = styled('ul', {
    listStyle: 'none',
    margin: '0',
    padding: '0',
    position: 'relative',
    whiteSpace: 'nowrap',
    '& li': {
        display: 'inline-block',
        '& button': {
            fontSize: '13px',
            color: '#FFF',
            background: 'transparent',
            border: 0,
            display: 'block',
            padding: '8px 15px',
            fontWeight: '600',
            textDecoration: 'none',
            textTransform: 'uppercase',
            borderBottom: '5px solid transparent',
        },
    },
    '& li.active': {
        '& button': {
            borderBottom: '5px solid #00b7b5',
            color: '#fccb36',
        },
    },
});

export const HamburgerButtonWrapper = styled('button', {
    display: 'block',
    border: '0',
    background: 'transparent',
    color: '#38b7b5',
    fontSize: '21px',
});

export const LogoContainer = styled('div', {
    padding: '0px 0px 0px 20px',
    display: 'flex',
    overflow: 'hidden',
    '& button': {
        display: 'block',
        border: '0',
        background: 'transparent',
        color: '#38b7b5',
        fontSize: '21px',
        marginRight: '10px',
        '@md': {
            display: 'none',
        },
    },
});

export const UserMenuWrapper = styled('div', {
    display: 'flex',
    justifyContent: 'flex-end',
    color: '#FFF',
    height: '100%',
    flex: '1',
    '& img': {
        width: '35px',
        height: '35px',
    },
    '& h5': {
        display: 'none',
        '@md': {
            display: 'block',
        },
    },
});

export const UserMenuButton = styled('button', {
    display: 'flex',
    alignItems: 'center',
    gap: '10px',
    border: '0px',
    backgroundColor: 'transparent',
    padding: '0 20px',
    '&.active': {
        backgroundColor: '#041f1f',
    },
    '& .fa': {
        fontSize: '18px',
        color: '#00b7b5',
    },
});

export const MenuContentWrapper = styled('div', {
    position: 'fixed',
    top: '60px',
    zIndex: 1,
    backgroundColor: '#00b7b5',
    boxShadow: '0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1)',
    '& ul': {
        listStyle: 'none',
        margin: '0',
        padding: '0',
        '& a': {
            fontSize: '14px',
            color: '#FFF',
            display: 'block',
            padding: '10px 20px',
            textDecoration: 'none',
            '&:hover': {
                backgroundColor: '#FFF',
                color: '#00b7b5',
            },
        },
    },
});
