import React from 'react';
import moment from 'moment';
import PropTypes from 'prop-types';
import { connect } from 'react-redux';
import ProgressBar from './ProgressBar';
import Header from './Header';
import SideMenu from './SideMenu';
import PageContent from './PageContent';
import Footer from '../../components/layout/Footer';
import { handleDeriveStoreMenuData } from './utils';
import routesData from '../../config/routesData';
import { styled } from '../../stitches.config';

const createDefaultState = () => ({
    storeMenuData: {},
    breadcrumbRoutes: [],
    customFooter: undefined,
});

const CustomFooterStyle = styled('div', {
    width: '100%',
    backgroundColor: '#003737',
    padding: '8px 16px',
    position: 'fixed',
    bottom: 0,
    zIndex: 4,
    variants: {
        showedMenu: {
            true: {
                '@lg': {
                    width: 'calc(100% - 270px)',
                    left: '270px',
                },
            },
        },
    },
});

class Layout extends React.PureComponent {
    state = createDefaultState()

    static getDerivedStateFromProps(props, state) {
        const derivedStoreMenuData = handleDeriveStoreMenuData(props, state);

        if (derivedStoreMenuData) return derivedStoreMenuData;

        return null;
    }

    componentDidMount() {
        this.initializeCalendar();
        this.handleFetchMenu();
        this.handleUpdateWindowHeight();
    }

    initializeCalendar = () => {
        const firstDate = `01/${moment().format('MM/YYYY')}`;
        const lastDate = `${moment().daysInMonth()}/${moment().format('MM/YYYY')}`;

        this.handleSetCalendar(firstDate, lastDate);
    }

    handleUpdateWindowHeight = () => {
        const { onSetWindowHeight } = this.props;

        onSetWindowHeight(window.innerHeight);
    }

    handleFetchMenu = () => {
        const { onFetchMenu, getPassport } = this.props;

        onFetchMenu(getPassport());
    }

    handleSetCalendar = (start = null, end = null, onchange, showHelper = false) => {
        const { onSetCalendar } = this.props;

        onSetCalendar(start, end, onchange, showHelper);
    }

    handleSetGlobalMessage = (data) => {
        const { notificationSystem } = this.props;

        notificationSystem.addNotification(data);
    }

    handleSetBreadcrumbRoutes = (data) => {
        const { breadcrumbRoutes } = this.state;
        if (JSON.stringify(data) !== JSON.stringify(breadcrumbRoutes)) this.setState({ breadcrumbRoutes: data });
    }

    render() {
        const {
            children, onSetContentHeaderButtons, onSetContentRangeDate, onSetContentFilterColumn, calendar, filterBranch, loaderApi, routeName,
            isShowingSidebarMenu,
        } = this.props;

        const { breadcrumbRoutes, customFooter } = this.state;

        const childrenWithProps = React.Children.map(
            children,
            child => React.cloneElement(child, {
                assignCalendar: (start, end, onchange, showHelper) => { this.handleSetCalendar(start, end, onchange, showHelper); },
                assignButtons: (buttons) => { onSetContentHeaderButtons(buttons); },
                assignRangeDate: (rangeDate) => { onSetContentRangeDate(rangeDate); },
                assignFilterColoumn: (rangeDate) => { onSetContentFilterColumn(rangeDate); },
                assignCustomFooter: footer => this.setState({ customFooter: footer }),
                showProgress: () => { this.progressBar.handleShowProgress(); },
                hideProgress: () => { this.progressBar.handleHideProgress(); },
                calendar,
                filterBranch,
                setGlobalMessage: data => this.handleSetGlobalMessage(data),
                routesData,
                routeName,
                assignBreadcrumbRoutes: data => this.handleSetBreadcrumbRoutes(data),
                isShowingSidebarMenu,
            }),
        );

        return (
            <React.Fragment>
                <ProgressBar ref={(c) => { this.progressBar = c; }} loaderApi={loaderApi} />
                <Header />
                <SideMenu />
                <PageContent {...this.props} breadcrumbRoutes={breadcrumbRoutes}>
                    {childrenWithProps}
                    <Footer />
                </PageContent>
                {customFooter && (
                    <CustomFooterStyle showedMenu={isShowingSidebarMenu}>
                        {customFooter}
                    </CustomFooterStyle>
                )}
            </React.Fragment>
        );
    }
}

Layout.propTypes = {
    menuList: PropTypes.arrayOf(PropTypes.shape({})),
    filterBranch: PropTypes.shape({}),
    notificationSystem: PropTypes.shape({
      addNotification: PropTypes.func,
    }),
    children: PropTypes.shape({}).isRequired,
    location: PropTypes.shape({}).isRequired,
    calendar: PropTypes.shape({}).isRequired,
    onSetWindowHeight: PropTypes.func.isRequired,
    onFetchMenu: PropTypes.func.isRequired,
    getPassport: PropTypes.func.isRequired,
    onSetCalendar: PropTypes.func.isRequired,
    onSetContentHeaderButtons: PropTypes.func.isRequired,
    onSetContentRangeDate: PropTypes.func.isRequired,
    onSetContentFilterColumn: PropTypes.func.isRequired,
    loaderApi: PropTypes.bool.isRequired,
};

Layout.defaultProps = {
    menuList: [],
    filterBranch: {},
    notificationSystem: ({
      addNotification: () => {},
    }),
};
const mapStateToProps = (state = []) => ({
    isShowingSidebarMenu: state.menu.isShowingSidebarMenu,
});

export default connect(mapStateToProps)(Layout);
